﻿#include <QDebug>
#include <QWidget>
#include "c3a0200protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include <QThread>

C3a0200Protocol::C3a0200Protocol(QObject *parent) : CBaseProtocol(parent)
{
     CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
     CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
    if(dripage != NULL){
        qDebug()<<"connect C3a0100Protocol";
        connect(this, &C3a0200Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &C3a0200Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
    }
    m_slProtoKeys.append(QString::fromLocal8Bit("时间戳一:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("校准加速度-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("校准加速度-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("校准加速度-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("角加速度-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("角加速度-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("角加速度-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("线性加速度-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("线性加速度-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("线性加速度-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("四元数-w:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("四元数-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("四元数-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("四元数-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("时间戳二:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始加速度-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始加速度-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始加速度-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始陀螺-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始陀螺-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始陀螺-z:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始磁力计-x:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始磁力计-y:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("初始磁力计-z:"));
    m_slProtoKeys.append("Temp-UNO:");
    m_slProtoKeys.append("Temp-DUE:");
}

C3a0200Protocol::~C3a0200Protocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool C3a0200Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = barr.at(5) + (barr.at(6) << 8) + 3 + 4 + 4;

    if(m_uMsgLen != sizeof(Stru3a0200)){
        m_uMsgLen = sizeof(Stru3a0200);
        return false;
    }

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(5)<<barr.at(6);
}

void C3a0200Protocol::paseMsg(const QByteArray msg){
    Stru3a0200 st_3a0200;
    ::memset(&st_3a0200, 0x00, sizeof(Stru3a0200));
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');

    m_iReqCount++;

    ::memcpy(&st_3a0200, msg.data(), 67);
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;
    QStringList dataValues;
    /*
    QDateTime utcDateTime = QDateTime::fromSecsSinceEpoch(st_3a0100.timestamp);
    QTimeZone systemTimeZone = QTimeZone::systemTimeZone();
    QDateTime localDateTime = utcDateTime.toTimeZone(systemTimeZone);
    dataValues.append(localDateTime.toString());
    */
    dataValues.append(QString::number(st_3a0200.timestamp1));
    dataValues.append(QString::number(st_3a0200.CAccel_x));
    dataValues.append(QString::number(st_3a0200.CAccel_y));
    dataValues.append(QString::number(st_3a0200.CAccel_z));
    dataValues.append(QString::number(st_3a0200.Alocity_x));
    dataValues.append(QString::number(st_3a0200.Alocity_y));
    dataValues.append(QString::number(st_3a0200.Alocity_z));
    dataValues.append(QString::number(st_3a0200.LAcce_x));
    dataValues.append(QString::number(st_3a0200.LAcce_y));
    dataValues.append(QString::number(st_3a0200.LAcce_z));
    dataValues.append(QString::number(st_3a0200.Quaternion_w));
    dataValues.append(QString::number(st_3a0200.Quaternion_x));
    dataValues.append(QString::number(st_3a0200.Quaternion_y));
    dataValues.append(QString::number(st_3a0200.Quaternion_z));
    dataValues.append(QString::number(st_3a0200.timestamp2));
    dataValues.append(QString::number(st_3a0200.oriAccel_x));
    dataValues.append(QString::number(st_3a0200.oriAccel_y));
    dataValues.append(QString::number(st_3a0200.oriAccel_z));
    dataValues.append(QString::number(st_3a0200.oriGyro_x));
    dataValues.append(QString::number(st_3a0200.oriGyro_y));
    dataValues.append(QString::number(st_3a0200.oriGyro_z));
    dataValues.append(QString::number(st_3a0200.OriMagnet_x));
    dataValues.append(QString::number(st_3a0200.OriMagnet_y));
    dataValues.append(QString::number(st_3a0200.OriMagnet_z));

    writeCvsFile("3A02", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }

}

