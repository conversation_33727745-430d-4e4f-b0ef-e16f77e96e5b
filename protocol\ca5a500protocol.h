﻿#ifndef CA5A500PROTOCOL_H
#define CA5A500PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"
#include <QVector>

class CA5A500Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit CA5A500Protocol(QObject *parent = nullptr);
    ~CA5A500Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool preInit();
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strua5a500{
        unsigned short head;
        //unsigned char pgidex;
        int flog;
        short temp;
        unsigned char ero;
    }Strua5a500;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN, const int protoindex);
    void sigFreqUpdate(const QStringList &datakeys, const QVector<double> dataValues, const QString &sportN, const int protoindex);
private:
    long m_lcatCurrCounts;
    QVector<double> m_vGyrDatas;
    QVector<double> m_vTempDatas;
    double m_davggyr;
    double m_davgtemp;
    QStringList m_slFreqKeys;
};

#endif // C5A5A00PROTOCOL_H
