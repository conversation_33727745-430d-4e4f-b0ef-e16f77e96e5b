﻿#ifndef CA6A600PROTOCOL_H
#define CA6A600PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"
#include <QVector>

class CA6A600Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit CA6A600Protocol(QObject *parent = nullptr);
    ~CA6A600Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool preInit();
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strua5a500{
        unsigned short head;
        int flogx;
        int flogy;
        int flogz;
        short accelx;
        short accely;
        short accelz;
        short flogtemp;
        short acceltemp;
        unsigned char ero;
    }Strua6a600;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN, const int protoindex);
    void sigFreqUpdate(const QStringList &datakeys, const QVector<double> dataValues, const QString &sportN, const int protoindex);
private:
    long m_lcatCurrCounts;
    QVector<double> m_vGyrDatasx;
    QVector<double> m_vGyrDatasy;
    QVector<double> m_vGyrDatasz;
    QVector<double> m_vCaliDatasx;
    QVector<double> m_vCaliDatasy;
    QVector<double> m_vCaliDatasz;
    QVector<double> m_vGTempDatas;
    QVector<double> m_vCTempDatas;
    double m_davggyrx;
    double m_davggyry;
    double m_davggyrz;
    double m_davgcalx;
    double m_davgcaly;
    double m_davgcalz;
    double m_davggyrtemp;
    double m_davgcaltemp;
    QStringList m_slFreqKeys;

};

#endif // CA6A600PROTOCOL_H
