﻿#ifndef C4A0100PROTOCOL_H
#define C4A0100PROTOCOL_H

#include <QObject>
#include <QTimerEvent>
#include "cbaseprotocol.h"

class C4a0100Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C4a0100Protocol(QObject *parent = nullptr);
    ~C4a0100Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru4a0100{
        unsigned short head;
        unsigned short cmd;
        unsigned short len;
        float accel_x;
        float accel_y;
        float accel_z;
        float gyro_x;
        float gyro_y;
        float gyro_z;
        float temp_UNO;
        float temp_DUE;
        unsigned short checksum;
    }Stru4a0100;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);

private:
};

#endif // C4A0100PROTOCOL_H
