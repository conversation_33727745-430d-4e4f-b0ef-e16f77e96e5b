﻿#ifndef CPARAMDATA_H
#define CPARAMDATA_H

#include <QObject>
#include <QMap>
#include <QColor>
#include <QSharedPointer>

/************************************
* 类名 CProtoParamData
* 父类 QObject
* 说明 参数数据类，用于保存通过参数设置界面设置的相关参数
*************************************/

typedef struct _MapParmSt_{
    int m_idashMode =0;
    int m_iFreshNum = 200;
    int m_iPlaystyle = 0;
    int m_iInterTime = 200;
    int m_iInterPoint = 200;
    int m_iDotNum = 2;
    QString m_sfdotColor = "#FF0000";
    QString m_sflineColor = "#FF0000";
    QString m_ssdotColor = "#00FF00";
    QString m_sslineColor = "#00FF00";
    QString m_stdotColor = "#0000FF";
    QString m_stlineColor = "#0000FF";
    int m_cfdotwidth = 1;
    int m_csdotwidth = 1;
    int m_ctdotwidth = 1;
    int m_cflinewidth = 1;
    int m_cslinewidth = 1;
    int m_ctlinewidth = 1;
    int m_pflinestyle = 0;
    int m_pslinestyle = 0;
    int m_ptlinestyle = 0;
    QPointF m_pDefSite = QPointF(113.81893000,  22.74857000);
    int m_iShowLevel = 14;
    QVector<int> m_vdbnum = { 2, 1, 3, 4, 6, 8, 10};
    QVector<Qt::PenStyle> m_vdstyle = {Qt::SolidLine, Qt::DashLine, Qt::DotLine, Qt::DashDotLine};
}MapParmSt;

typedef struct _DevParmSt_{
    QString prototype;
    QString calitype;
    QString gyrotype;
    int gyrodirect;
    int gyrofrq;
    int catfrq;
    int fpgafrq;
}DevParmSt;

typedef struct _ProtoFieldSt{
    int protoindex;
    QVector<int> field;
    QMap<QString, QString> divisor;
}ProtoFieldSt;

typedef struct _FileParmSt_{
    QString savedir = "/Data";  //默认在data目录
    bool isNeedDat = false;    //默认不生成
    bool isNeedCsv = true;    //默认不生成
    int  datType = 0;         //生成二进制数据格式类型
    QString mapcachedir = "MapCache";   //地图缓存目录
}FileParmSt;

/*协议参数类*/
class CProtoParamData : public QObject
{
    Q_OBJECT
public:
    explicit CProtoParamData(QObject *parent = nullptr);
    void initParam(QString key, QString value);
    void initParam(QMap<QString,QString> &mparms);
    void informParamSig();
    int getTimeOut(){
        return m_mparmMap["TIMEOUT"].toInt();
    }

    int getTimeRound(){
        return m_mparmMap["ROUND"].toInt();
    }

    int getIntvTimes(){
        return m_mparmMap["INTVTIME"].toInt();
    }

    /*设置协议字段*/
    static void setProtocolField(int index, QVector<int>);
    /*设置协议标度因数*/
    static void setProtocolDivisor(int index, QMap<QString, QString> &);
    static QVector<int>& getProtocolField(int index);
    /*获取标度因数*/
    static void getProtocolDivisor(int index, QMap<QString, QString> &divisor);
    static bool getIsNeedDat();
    static MapParmSt& getMapParmSt();
    static DevParmSt& getDevParmSt();
    static QMap<int, bool>& getFileSaveMap();
    static FileParmSt* getFileParmSt();
private:
    /*固定参数*/
    static QMap<int, ProtoFieldSt *> CProtoParamData::m_mPFieldMap;  //协议字段配置
    static QMap<int, bool> m_mNeedDatMap;         //需要生成二进制文件的协议配置
    static MapParmSt m_sMapParmst;
    static DevParmSt m_sDevParmst;
    static FileParmSt m_sFileParmst;
private:
    /*实时参数*/
    QMap<QString, QString> m_mparmMap;

signals:
    void sigParamChange(const QMap<QString, QString> &map);

};

#endif // CPARAMDATA_H
