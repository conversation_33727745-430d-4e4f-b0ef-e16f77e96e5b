﻿#ifndef CREVOCTRPAGE_H
#define CREVOCTRPAGE_H

#include <QWidget>
#include "ccserialpage.h"
#include "cturntablejeditdialog.h"

#define CMDLINE_MAXLENGTH 64

enum MotionType{
    E_NoChange,
    E_Stop,
    E_Sit,
    E_Speed,
    E_Swing,
    E_GiveChg,
    E_RelatPos
};

enum MotionStatus{
    E_stIdle,       //空闲
    E_stStillness,  //静止
    E_stSit,        //位置
    E_stSpeed,      //速度
    E_stSwing,      //摇摆
    E_stGiv,        //找零
    E_stRpos,       //相对位置
    E_stStop        //停止
};

namespace Ui {
class CRevoCtrPage;
}

/*
*  转台页面
*/

class CRevoCtrPage : public QWidget
{
    Q_OBJECT

public:
    explicit CRevoCtrPage(QWidget *parent = nullptr);
    ~CRevoCtrPage();
    void init();
    bool enable_CmdTo_Turntable(int turnType, int optype);
    bool motion_CmdTo_Turntable(int turnType, MotionType optype, float sit, float speed, float accel);
    bool clear_CmdTo_Turntable(int turnType, int optype);
    void setSeriPage(CcserialPage *page);
    QString statusEToStr(MotionStatus es);
    void startAutoTurnTWork();
    void timerEvent(QTimerEvent *e);
    void auto_Turnt_Work_Complete();
    bool is_Next_needStop(const QList<ES_PAIR> &tasklist, int start_index);

private slots:
    void on_bt_inenable_clicked();
    void on_bt_outenable_clicked();
    void slotInMotionModChange(const QString &text);
    void slotOutMotionModChange(const QString &text);    
    void on_bt_outstart_clicked();

    void on_bt_outstop_clicked();

    void on_bt_instart_clicked();

    void on_bt_instop_clicked();

    void on_bt_importfile_clicked();

    void on_bt_editfile_clicked();

    void on_bt_startwork_clicked();

    void on_bt_stopwork_clicked();

public slots:
    void slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
signals:
    void SigSeriWrite(const char * arr, int &len);
    void sigSeriOpen(const QString portn,const int optype, QString fileprefix);

private:
    Ui::CRevoCtrPage *ui;
    CcserialPage *m_oseriPage;
    QString m_sPortn;
    QVector<QString> m_vsDataPortn;
    bool m_bInEnable;
    bool m_bOutEnable;
    MotionStatus m_sInRunStatus;
    MotionStatus m_sOutRunStatus;
    QStringList m_sWorkFiles;
    CTurntableJEditDialog *m_dTurntdlg;
    QVector<CSseriService *> m_vsWorkSer;
    int m_iIndex;
    bool m_bInInPlace;
    bool m_bOutInPlace;
    int m_iTimerId;
    QPointer<CSseriService> m_sTurnServ;
    QString m_sFilePre;
};

#endif // CREVOCTRPAGE_H
