v1.0.6b
1、支持转台采集数据连续采集功能，即转台操作不同的步骤，数据采集不中断，并且数据保存到同一个文件中
v1.0.6c
1、解决数据视图中当图形拖放到最后一个部分时，再次用鼠标缩小时，会出现当前数据重复刷新的现象
2、解决参数下发后，若是当前模式是输出原始数据，会出现原始数据输出异常，程序卡死的情况
3、解决奇偶校验未正常翻译，导致设置奇校验无效的问题
4、优化多窗口显示时字段过多，部分字段无法正常显示出来的问题
v1.0.6d
1、增加支持按二进制方式采集未定义格式的串口数据，并生成dat文件
2、增加文件工具功能
v1.0.6e
1、解决下发参数后，重启串口后，主界面串口打开状态显示不正确，并且重新打开串口时，第一次打开会打开失败的问题
v1.0.6f
1、仪表视图新增飞行仪表页面
2、解决未定义串口数据采集异常问题
3、升级功能新增一个总包校机制
v1.0.6g
1、解决转台操作错误后，重新导入时脚本加载异常的问题
v1.0.7
1、组合导航协议输出增加标准差数据输出
2、解决数据视图界面同一个串口切换不同协议时异常退出问题
3、优化升级流程，与下位机一起增加数据包顺序校验，确保两边升级状态一致
4、3b02协议新增系统时间字段
5、增加串口下发配置信息功能
v1.0.7a
1、完善工具功能，支持文件平滑或者文件拆分，支持目前我们上位机采集的所有文件。
v1.0.7c
1、串口配置命令增加四个快捷命令
2、平滑工具增加支持两个计算因数，并优化带因数的计算速度，增加全选反选。
3、文件工具增加通知线程退出机制
4、调试窗口增加输出频率显示
5、参数设置页面的陀螺频率，采样频率，帧计数改为可编辑状态
v1.0.7d
1、修改默认语言为中文
2、解决数据视图界面取消游标后数据窗口变小的问题，生成的游标默认值改为窗口最小值
v1.0.8a
1、调整BB00协议
2、调整组合导航协议后处理数据为不处理，直接写入
3、根据利工提供的新的标定算法重新打包新版本标定工具
4、报文工具新增BB00解析，并将报文工具，标定工具集成到上位机中
5、解决B55B协议解析异常的问题
v1.0.8c
1、调整BDDB0B协议，将横滚标准差改为显示pps延迟时间
2、组合导航协议时间戳增加校验
3、支持B55B协议可配置陀螺加计的标度因数
4、优化个别翻译错误
5、新增SDK存储控制命令参数
6、优化标定工具，支持3A01协议数据标定计算
v1.0.8d
1、新增4a020b01协议
2、优化BDDB0B协议周内秒跑车视图页面数据处理
3、解决BB00协议算法输出字段俯仰跟横滚名称显示反了的问题及部分精度字段错别字问题
v1.0.8g
1、新增A6A6协议支持自定义标度因数配置功能
2、SDK读取功能新增读取结束判断，读取结束后关闭串口
3、固件参数模块新增密码认证机制，除设置Debug模式，波特率，输出频率，SDK存储，固化参数外，其他参数设置均需要输入密码
v1.0.8h
1、重整BDDB1B协议，调整陀螺，加计数据协议长度，作为新的组合导航输出协议
2、地图新增支持19-20层级空白缩放
3、地图测距时鼠标箭头从手掌模式切换到箭头模式，方便精准点击
4、SD卡读取完成新增弹框提示
5、工具-》报文工具，新增ASCII码文件转16进制字符串文件功能
6、参数下发后调整为不重启串口
v1.0.8i
1、优化长时间运行后台视图及地图刷新导致单口调试计时异常的问题
v1.0.8j
1、工具-》标定工具，新增东北天标定，新增IMU零偏计算功能
2、地图新增标记功能，可在地图上点击标记，标记当前轨迹点，同时会生成一个文件保存标记轨迹点的经纬度及标记时间
3、测距支持手工输入经纬度，在第二个经纬度编辑框输入完成后回车自动计算距离