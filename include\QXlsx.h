﻿/*

   ____                               ____      ___ ____       ____  ____      ___
  6MMMMb                              `MM(      )M' `MM'      6MMMMb\`MM(      )M'
 8P    Y8                              `MM.     d'   MM      6M'    ` `MM.     d'
6M      Mb __ ____     ____  ___  __    `MM.   d'    MM      MM        `MM.   d'
MM      MM `M6MMMMb   6MMMMb `MM 6MMb    `MM. d'     MM      YM.        `MM. d'
MM      MM  MM'  `Mb 6M'  `Mb MMM9 `Mb    `MMd       MM       YMMMMb     `MMd
MM      MM  MM    MM MM    MM MM'   MM     dMM.      MM           `Mb     dMM.
MM      MM  MM    MM MMMMMMMM MM    MM    d'`MM.     MM            MM    d'`MM.
YM      M9  MM    MM MM       MM    MM   d'  `MM.    MM            MM   d'  `MM.
 8b    d8   MM.  ,M9 YM    d9 MM    MM  d'    `MM.   MM    / L    ,M9  d'    `MM.
  YMMMM9    MMYMMM9   YMMMM9 _MM_  _MM_M(_    _)MM_ _MMMMMMM MYMMMM9 _M(_    _)MM_
            MM
            MM
           _MM_

  Copyright (c) 2018, <PERSON>

  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
  - Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the
    documentation and/or other materials provided with the distribution.
  - Neither the name of the author nor the
    names of any contributors may be used to endorse or promote products
    derived from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

 */

#ifndef QXLSX_H
#define QXLSX_H

#include "header/xlsxabstractooxmlfile.h"
#include "header/xlsxabstractooxmlfile_p.h"
#include "header/xlsxabstractsheet.h"
#include "header/xlsxabstractsheet_p.h"
#include "header/xlsxcell.h"
#include "header/xlsxcell_p.h"
#include "header/xlsxcellformula.h"
#include "header/xlsxcellformula_p.h"
#include "header/xlsxcelllocation.h"
#include "header/xlsxcellrange.h"
#include "header/xlsxcellreference.h"
#include "header/xlsxchart.h"
#include "header/xlsxchart_p.h"
#include "header/xlsxchartsheet.h"
#include "header/xlsxchartsheet_p.h"
#include "header/xlsxcolor_p.h"
#include "header/xlsxconditionalformatting.h"
#include "header/xlsxconditionalformatting_p.h"
#include "header/xlsxcontenttypes_p.h"
#include "header/xlsxdatavalidation.h"
#include "header/xlsxdatavalidation_p.h"
#include "header/xlsxdatetype.h"
#include "header/xlsxdocpropsapp_p.h"
#include "header/xlsxdocpropscore_p.h"
#include "header/xlsxdocument.h"
#include "header/xlsxdocument_p.h"
#include "header/xlsxdrawing_p.h"
#include "header/xlsxdrawinganchor_p.h"
#include "header/xlsxformat.h"
#include "header/xlsxformat_p.h"
#include "header/xlsxglobal.h"
#include "header/xlsxmediafile_p.h"
#include "header/xlsxnumformatparser_p.h"
#include "header/xlsxrelationships_p.h"
#include "header/xlsxrichstring.h"
#include "header/xlsxrichstring_p.h"
#include "header/xlsxsharedstrings_p.h"
#include "header/xlsxsimpleooxmlfile_p.h"
#include "header/xlsxstyles_p.h"
#include "header/xlsxtheme_p.h"
#include "header/xlsxutility_p.h"
#include "header/xlsxworkbook.h"
#include "header/xlsxworkbook_p.h"
#include "header/xlsxworksheet.h"
#include "header/xlsxworksheet_p.h"
#include "header/xlsxzipreader_p.h"
#include "header/xlsxzipwriter_p.h"

#endif    // QXLSX_H
