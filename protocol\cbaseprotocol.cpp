﻿#include "cbaseprotocol.h"
#include <QDebug>
#include <QTextCodec>
#include "cdritestpage.h"
#include "csubpagemanager.h"
#include <QThread>
#include <iostream>
#include <QtMath>
#include <QDir>

CBaseProtocol::CBaseProtocol(QObject *parent) : QObject(parent)
{
    m_pcvsfile = NULL;
    m_bIsNeedShow = false;
    m_bIsNeedShowL = false;
    m_bIsNeedShowC = false;

    m_bisSaveFile = 1;
    m_eFrameErr = E_FRAME_OK;
    m_tCode = QTextCodec::codecForName("GBK");
    m_iLossFrameSum = 0;
    m_iCheckFailSum = 0;
    m_bIsNeedKeys = true;
    m_uRunTimes = 0;
    m_iCurrentFeq = 0; //当前数据频率
    m_iTimeout = 100;
    m_iReqCount = 0;

    m_UpgTimer.setTimerType(Qt::PreciseTimer);
    qDebug()<<"m_uRunTimes:"<<QTime::currentTime().toString()<<QThread::currentThreadId();

    qRegisterMetaType<QVector<double>>("QVector<double>");

    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    connect(dripage, &CDriTestPage::sigToProtocol, this, &CBaseProtocol::slotFromDriPage);
    connect(&m_UpgTimer, &QTimer::timeout, this, [=](){
        //qDebug()<<"upgtimer:"<<m_uRunTimes;
        m_bIsNeedShow = true;
        m_bIsNeedShowL = true;
        m_bIsNeedShowC = true;
        m_uRunTimes++;
        //每10s来计算一次频率
        if(m_uRunTimes % 100 == 0){
            m_iCurrentFeq = m_iReqCount / 10;
            m_iReqCount = 0;
        }
    });
    m_UpgTimer.start(m_iTimeout);
    m_uMsgLen = 1;
    m_dGyrFreq = 0;
    m_dCatFreq = 1;
    m_iFildSize = 0;

    m_freqpcvsfile = NULL;
    m_forifile = NULL;
    m_iFpgaFc = 200;
    m_writedLine = 0;
    m_iGnsstime = 0;
}

CBaseProtocol::~CBaseProtocol()
{
    qDebug()<<"~CBaseProtocol";
    if(m_pcvsfile != NULL){
        m_pcvsfile->close();
        if(!m_bisSaveFile){
            m_pcvsfile->remove();
        }
        delete m_pcvsfile;
        m_pcvsfile = NULL;
    }
    if(m_freqpcvsfile != NULL){
        m_freqpcvsfile->close();
        delete m_freqpcvsfile;
        m_freqpcvsfile = NULL;
    }

    if(m_forifile != NULL){
        m_forifile->close();
        delete m_forifile;
        m_forifile = NULL;
    }

    m_UpgTimer.stop();
    emit sigDestory();
}

bool CBaseProtocol::setProtoLength(const QByteArray &barr){
    return true;
}

bool CBaseProtocol::preInit(){
    m_vWriteFields = CProtoParamData::getProtocolField(m_iProtoIndex);
    m_iFildSize = m_vWriteFields.size();
    pFilest = CProtoParamData::getFileParmSt();
    m_bIsNeedDat = pFilest->isNeedDat;
    m_iDatType = pFilest->datType;
    qDebug()<<m_iProtoIndex<<m_vWriteFields<<m_bIsNeedDat;
    qDebug()<<"parent preInit";
    if(m_dCatFreq == 0 || m_dGyrFreq == 0){
        m_icatFreqCounts = 0;
    }else{
        m_icatFreqCounts = m_dGyrFreq / m_dCatFreq;
    }

    emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") +QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail")+ QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABEXCEPT"), QStringList(tr("Except") + ":0"), m_sPortN);
    return true;
}

void CBaseProtocol::paseMsg(const QByteArray msg){

}

bool CBaseProtocol::filePrefixChange(QString prefilex, QStringList &vskey, int filetype){
    qDebug()<<"filePrefixChange:"<<prefilex;
    QByteArray readywrite;
    bool startflag = true;
    int loopi = 0;
    int indexi = 0;

    QFile *filehandel = getFileHandle(filetype);

    if(filehandel != NULL){
        filehandel->close();
        delete filehandel;
    }

    QString sFileName = genDtFileName(prefilex, "csv");
    filehandel = new QFile(sFileName);
    if(!filehandel->open(QIODevice::WriteOnly)){
        qDebug()<<"file open fail:"<<filehandel->errorString();
        filehandel = NULL;
        return false;
    }
    setFileHandle(filehandel, filetype);

    QStringList writekeys;
    if(vskey.isEmpty()){
        writekeys.append(m_slProtoKeys);
    }else{
        writekeys.append(vskey);
    }

    for(QString str:writekeys){
        //默认写全部字段
        if(m_iFildSize != 0 && !prefilex.endsWith("C" + QString::number(m_icatFreqCounts))){
            if(indexi >= m_iFildSize){
                break;
            }

            if(loopi++ != m_vWriteFields.at(indexi)){
                continue;
            }
            indexi++;
        }

        if(startflag){
            startflag = false;
        }else{
           readywrite.append(",");
        }
        str.remove(':');
        if(prefilex.startsWith("AA55")){
            readywrite.append( str );
        }else{
            readywrite.append("\""+ str + "\"");
        }
    }
    QString ws = readywrite;

    readywrite.append("\n");
    //为了兼容excel正常显示 中文，将数据转成GBK保存
    QByteArray gbkdata = m_tCode->fromUnicode(readywrite);
    //m_pcvsfile->write(readywrite);
    filehandel->write(gbkdata);
    //qDebug()<<"readwrite:"<<readywrite;
    return true;
}

QFile *CBaseProtocol::getFileHandle(int filetype){
    switch (filetype) {
    case 0:
        return  m_pcvsfile;
    case 1:
        return m_freqpcvsfile;
    default:
        return m_pcvsfile;
    }
}

void CBaseProtocol::setFileHandle(QFile *filehandle, int filetype){
    switch (filetype) {
    case 0:
        m_pcvsfile = filehandle;
        return;
    case 1:
        m_freqpcvsfile = filehandle;
        return;
    default:
        m_pcvsfile = filehandle;
        return;
    }
}

void CBaseProtocol::writeCvsFile(QString fileName, QStringList &keyList, QStringList &dataList, int filetype){
    m_writedLine++;
    QByteArray readywrite;
    bool startflag = true;
    int loopi = 0;
    int indexi = 0;

    //是否需要生成csv文件
    if(!pFilest->isNeedCsv){
        return;
    }

    QFile *filehandel = getFileHandle(filetype);

    if(filehandel == NULL){
        if(!filePrefixChange(fileName, keyList, filetype)){
            return;
        }
    }

    filehandel = getFileHandle(filetype);

    for(QString str:dataList){
        //默认写全部字段
        if(m_iFildSize != 0 && !fileName.endsWith("C" + QString::number(m_icatFreqCounts))){
            if(indexi >= m_iFildSize){
                break;
            }

            if(loopi++ != m_vWriteFields.at(indexi)){
                continue;
            }
            indexi++;
        }

        if(startflag){
            startflag = false;
        }else{
           readywrite.append(",");
        }
        //AA55协议字段中不会出现逗号，并且算法后处理解析不支持有双引号的分隔
        if(fileName.startsWith("AA55")){
            readywrite.append( str );
        }else{
            readywrite.append("\""+ str + "\"");
        }
    }
    readywrite.append("\n");
    //为了兼容excel正常显示 中文，将数据转成GBK保存
    QByteArray gbkdata = m_tCode->fromUnicode(readywrite);
    //m_pcvsfile->write(readywrite);
    filehandel->write(gbkdata);
    filehandel->flush();
}

void CBaseProtocol::writeDatFile(const QString &prefix, const QByteArray &arrdata){
    if(m_forifile == NULL){
        QString OriFilename = genDtFileName(prefix, "dat");
        m_forifile = new QFile(OriFilename);
        if(!m_forifile->open(QIODevice::WriteOnly)){
            qDebug()<<"file open fail";
            return;
        }
    }
    m_forifile->write(arrdata);
}

/********************************
 * func_name:genDtFileName
 * parm 1:QString
 * return:QString
 * desc: 生成cvs文件名
**********************************/
QString CBaseProtocol::genDtFileName(QString prefix, QString suffix){
    QDateTime datet = QDateTime::currentDateTime();
    QString fileName = pFilest->savedir + QString("/%1_%2-%3-%4_%05.%6.%7_%8.%9").arg(prefix).arg(datet.date().year())\
            .arg(datet.date().month(), 2, 10, QLatin1Char('0')).arg(datet.date().day(), 2, 10, QLatin1Char('0'))\
            .arg(datet.time().hour(), 2, 10, QLatin1Char('0')).arg(datet.time().minute(), 2, 10, QLatin1Char('0'))\
            .arg(datet.time().second(), 2, 10, QLatin1Char('0')).arg(m_sPortN).arg(suffix);
    return fileName;
}

void CBaseProtocol::setFileSave(bool filesave){
    m_bisSaveFile = filesave;
}

void CBaseProtocol::setPortN(QString sportn){
    m_sPortN = sportn;
}

/********************************
 * func_name:seriFeedback
 * parm 1:unsigned char
 * parm 2:int
 * parm 3:int
 * return:void
 * desc: 构建一个响应报文，并发送到串口
**********************************/
void CBaseProtocol::seriFeedback(unsigned char cmd, int parameter0, int parameter1){
    txmsgonerecord_t onerecord;
    ::memset(&onerecord, 0, sizeof(onerecord));
    onerecord.willbesend = 1;
    onerecord.msglistindex = parameter0;
    onerecord.msgindex = parameter0;

    onerecord.msg.cmd = cmd;
    onerecord.msg.parameter0 = parameter0;
    onerecord.msg.parameter1 = parameter1;	//c_msg_ask_retransmission   0:-list index   1-retrans index
    onerecord.msg.msglistindex = onerecord.msglistindex;
    onerecord.msg.msgindex = onerecord.msgindex;

    onerecord.msg.len = sizeof(txmsgserialport_t);
    onerecord.msg.head0 = 0xaa;
    onerecord.msg.head1 = 0x55;
    onerecord.msg.tail = 0xfe;

    unsigned char checksum = 0;
    unsigned char* ptmpchar;
    int size = sizeof(txmsgserialport_t);
    ptmpchar = (unsigned char*)&onerecord.msg;
    for (int i = 0; i < size - 2; i++) {
        checksum += *(ptmpchar + i);
    }
    onerecord.msg.checksum = checksum;
}

void CBaseProtocol::setProtoParam(const QMap<QString, QString> &map){
    QString protot = QString(m_bMsgHead.toHex().toUpper()).leftJustified(8, '0');
    qDebug()<<"param:"<<map<<protot;
    if(map["PROTOT"] == protot || map["PROTOT"] == "DEFAULT" ){
        m_sGyrotype = map["GYROT"];
        m_sAcceltype = map["ACCELT"];
        m_sGyroDiret = map["GYRDRET"];
        QString scatfreq = map["CATFREQ"];
        m_dCatFreq = scatfreq.replace("HZ", "").toDouble();
        QString sgyrfreq = map["GYRFREQ"];
        m_dGyrFreq = sgyrfreq.replace("HZ", "").toDouble();
        m_iFpgaFc = map["FPGAFC"].toInt();
    }
}

void CBaseProtocol::slotFromDriPage(const int &type, const QStringList &params){
    if(params.size() == 0 || params.at(0) != m_sPortN){
        return;
    }
    E_PAGE_SIG es = static_cast<E_PAGE_SIG>(type);
    switch (es) {
    case E_PAGE_COMCHG:
        m_bIsNeedKeys = true;
        break;
    default:
        break;

    }

}

QString CBaseProtocol::getRunTimes(){
    //每100ms计数一次，10次就是1s
    uint32_t ttime = m_uRunTimes / 10;
    int hour = ttime / 3600;
    int minutes = (ttime % 3600) / 60;
    int seconds = (ttime % 3600) % 60;
    //qDebug()<<"m_uRunTimes"<<m_uRunTimes;
    return QString("%1:%2:%3").arg(hour, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));
}

void CBaseProtocol::setRunTimes(uint32_t times){
    m_uRunTimes = times;
    m_iFramIndx = -1;
}

bool CBaseProtocol::sum16CheckSum(const QByteArray msg){
    //qDebug()<<"sum16CheckSum"<<msg.size();
    uint16_t umsgchecksum = 0;
    uint16_t ucalchecksum = 0;
    ::memcpy(&umsgchecksum, msg.data() + msg.size() - 2, 2);
    for (int i = 0; i < msg.size() - 2;) {
        ucalchecksum +=  (0x00FF & msg[i]) |  (0xFF00 & (msg[i + 1] << 8));
        i += 2;
    }

    qDebug()<<"sum16CheckSum:"<<umsgchecksum<<"|"<<ucalchecksum;

    if(umsgchecksum == ucalchecksum){
        return true;
    }
    return false;
}

/*参数需要显式转换为一个整数，不能默认转换，例如原数据是short，需要&0x0000FF作为参数传递,因为负数转换补的是FF，而不是0*/
int CBaseProtocol::complement2original(int num){
    //char buf[5] = {0};
    //memcpy(buf, &num, 4);
    //qDebug()<<QByteArray(buf).toHex();
    int original = 0;
    int i = 0;

    while ((num >> i) != 0) {
        original |= (~(num >> i) & 1) << i;
        i++;
    }

    return original + 1;
}

bool CBaseProtocol::sumEorCheck(const QByteArray msg, int startpos, int endpos){
    char msgchecksum;
    char calchecksum = 0x00;
    QByteArray test;
    for(int i = startpos; i < msg.size() - endpos; i++){
        calchecksum ^= msg.at(i);
    }
    msgchecksum = msg.at(msg.size() - 1);
    if(msgchecksum == calchecksum){
        return true;
    }else{
        qDebug()<<"msgchecksum:"<<test.append(msgchecksum).toHex();
        qDebug()<<"calchecksum"<<test.append(calchecksum).toHex();
        return false;
    }
}

void CBaseProtocol::reserverData(QByteArray &src, int pos, int size){
    int len = src.size();
    //qDebug()<<src.toHex()<<pos<<size<<len;
    QByteArray temparr;
    //temparr.append(src, 2);
    for(int i = 0; i < size; i++){
        //qDebug()<<src.toHex()<<pos<<size<<len<<pos + size - i - 1;
        temparr.append(src.at(pos + size - i - 1));
    }
    src.replace(pos, size, temparr);
    //qDebug()<<src.toHex();
}

bool CBaseProtocol::sum8CheckSum(const QByteArray msg, int bpos, int endpos){
    unsigned char msgchecksum = 0;
    unsigned char calchecksum = 0;
    QByteArray test;
    msgchecksum = msg.at(msg.size() - 1);
    //字节bpos到字节size - endpos的累加和
    for (int i = bpos; i < msg.size() - endpos;i++) {
        calchecksum += (unsigned char)msg[i];
    }

    if(msgchecksum == calchecksum){
        return true;
    }else{
        qDebug()<<"msgchecksum:"<<test.append(msgchecksum).toHex();
        qDebug()<<"calchecksum"<<test.append(calchecksum).toHex();
        return false;
    }
}

bool CBaseProtocol::sum8to16CheckSum(const QByteArray msg, int bpos, int endpos){
    unsigned short msgchecksum = 0;
    unsigned short calchecksum = 0;

    ::memcpy(&msgchecksum, msg.data() + msg.size() - 2, 2);
    //字节bpos到字节size - endpos的累加和
    for (int i = bpos; i < m_uMsgLen - endpos;i++) {
        calchecksum += (unsigned char)msg[i];
    }

    if(msgchecksum == calchecksum){
        return true;
    }else{
        qDebug()<<"msgchecksum:"<< msgchecksum;
        qDebug()<<"calchecksum"<< calchecksum;
        return false;
    }
}

QString CBaseProtocol::getSysStatDesByC(const char cstat){
    QString sstat;
    switch (cstat) {
    case 0x70:
        sstat = QString::fromLocal8Bit("初始化中");
        break;
    case 0x71:
        sstat = QString::fromLocal8Bit("待机中");
        break;
    case 0x73:
        sstat = QString::fromLocal8Bit("标定中");
        break;
    case 0x74:
        sstat = QString::fromLocal8Bit("故障中");
        break;
    case 0x75:
        sstat = QString::fromLocal8Bit("相对对准中");
        break;
    case 0x76:
        sstat = QString::fromLocal8Bit("精对准中");
        break;
    case 0x77:
        sstat = QString::fromLocal8Bit("纯惯导导航");
        break;
    case 0x78:
        sstat = QString::fromLocal8Bit("组合导航");
        break;
    case 0x79:
        sstat = QString::fromLocal8Bit("零速模式");
        break;
    case 0x80:
        sstat = QString::fromLocal8Bit("DR组合模式");
        break;
    case 0x81:
        sstat = QString::fromLocal8Bit("开环矫正模式");
        break;
    case 0x82:
        sstat = QString::fromLocal8Bit("航向漂移估计模式");
        break;
    case 0x83:
        sstat = QString::fromLocal8Bit("垂直陀螺模式");
        break;
    default:
        QByteArray astat;
        sstat = astat.append(cstat).toHex();
        break;
    }
    return sstat;
}

int CBaseProtocol::getProtoIndex(QString protoname){
    QString newproton = protoname.toLower();
    qDebug()<<"getProtoIndex:"<<newproton;
    return g_sProtocolHead.indexOf(newproton);
}

void CBaseProtocol::calculateEulerAngles(const double qw, const double qx, const double qy, const double qz, double &oroll, double &opitch, double &oyaw){
    // 计算俯仰角（Pitch）
    double pitch = std::asin(-2.0 * (qy * qz + qw * qx));

    // 计算偏航角（Yaw）
    double yaw = std::atan2(2.0 * (qx * qy - qw * qz), qw * qw - qx * qx - qy * qy + qz * qz);

    // 计算滚转角（Roll）
    double roll = std::atan2(2.0 * (qw * qy + qx * qz), qw * qw + qx * qx - qy * qy - qz * qz);

    // 将弧度转换为度
    opitch = pitch * 180.0 / M_PI;
    oyaw = yaw * 180.0 / M_PI;
    oroll = roll * 180.0 / M_PI;

    // 输出结果
    qDebug() << u8"Pitch (俯仰角):" << opitch;
    qDebug() << u8"Yaw (偏航角):" << oyaw;
    qDebug() << u8"Roll (滚转角):" << oroll;
}

bool CBaseProtocol::lossFrameCheck(int framdindx){
    if(m_iFramIndx == -1){
        m_iFramIndx = framdindx;
        m_iMaxFramIndx = framdindx;
        return true;
    }
    if(framdindx != 0 ){
        if(m_iFramIndx == framdindx - 1){
            if(m_iMaxFramIndx < framdindx){
                m_iMaxFramIndx = framdindx;
            }
            m_iFramIndx = framdindx;
            return true;
        }
        m_iFramIndx = framdindx;
        return false;
    }else if(m_iMaxFramIndx == m_iFramIndx){
        m_iFramIndx = 0;
        return true;
    }
    m_iFramIndx = framdindx;
    return false;
}


