﻿#include "cfirmupgpage.h"
#include "ui_cfirmupgpage.h"
#include <QDir>
#include <QFileDialog>
#include <QMessageBox>
#include "ctlhtools.h"
#include "cinputdialog.h"
#include <cstring>

typedef struct _ParmCmd_{
    uint8_t fc;
    uint8_t sc;
}ParmCmd;

QVector<ParmCmd> parmCommands = {
     {0xAA, 0x11}, {0xAA, 0x13}, {0xAA, 0x14}, {0xAA, 0x26}, {0xAA, 0x18}, {0xAA, 0x19},
     {0xAA, 0x21}, {0xAA, 0x1A}, {0xAA, 0x1B}, {0xAA, 0x1E}, {0xAA, 0x25}, {0xAA, 0xF1},
     {0xAA, 0xF2}, {0xAA, 0xF3}, {0xAA, 0xF4}, {0xAA, 0xC3}, {0xAA, 0xC4}, {0xAA, 0xC1},
     {0xAA, 0xC2}, {0xAA, 0x30}, {0xAA, 0x34}, {0xAA, 0x31}, {0xAA, 0x32}, {0xAA, 0x33},
     {0xAA, 0x51}, {0xAA, 0x52}, {0xAA, 0x55}, {0xAA, 0x59}, {0xAA, 0x35}, {0xAA, 0x36},
     {0xAA, 0xC5}
 };

CFirmUpgPage::CFirmUpgPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CFirmUpgPage)
{
    ui->setupUi(this);
    m_sSeriser = NULL;
    m_bIsUpdating = false;
    m_bParmScroHide = false;
    m_pixleft.load(":/img/bleftarrownew.png");
    m_pixright.load(":/img/brightarrownew.png");
    ui->tb_isHide->setIcon(QIcon(m_pixleft));
    //ui->scrollArea->setHidden(true);
    //ui->fm_opframe->setHidden(true);

    QFont font("Arial", 14);
    font.setBold(true);
    ui->lb_informmsg->setFont(font);

    qRegisterMetaType<QVector<QByteArray>>("QVector<QByteArray>");

    m_parmNames = {
        tr("Output parameters"),
        tr("Baud rate"),
        tr("Output frequency"),
        tr("System working mode"),
        tr("GNSS arm parameters"),
        tr("Antenna installation angle"),
        tr("GNSS initial value"),
        tr("Position vector"),
        tr("Misalignment"),
        tr("User coordinate axis"),
        tr("Static of zero bias time"),
        tr("Cure Param"),
        tr("Def Param"),
        tr("All Apply"),
        tr("Read Back"),
        tr("GPS interruption type"),
        tr("Data output type"),
        tr("Debug mode"),
        tr("Gyro type"),
        tr("Calibration parameters"),
        tr("temperature compensation"),
        tr("Kalman filter Q matrix"),
        tr("Kalman filter R matrix"),
        tr("Indirect filtering coefficient"),
        tr("Software upgrade begins"),
        tr("Send upgrade package"),
        tr("Upgrade package completed"),
        tr("Upgrade termination"),
        tr("Gyro scale factor"),
        tr("Add scale factor"),
        tr("SD Card Operation")
    };
}

CFirmUpgPage::~CFirmUpgPage()
{
    delete ui;
}

void CFirmUpgPage::on_bt_clear_clicked()
{
    ui->te_textinfo->clear();
}

void CFirmUpgPage::setSeriPage(CcserialPage *seripage){
    m_oseriPage = seripage;
}

void CFirmUpgPage::on_bt_openfile_clicked()
{
    m_sWorkFile.clear();
    QString currentDir = QDir::currentPath();
    int index = ui->comboBox->currentIndex();
    if( index > 0){
        m_sWorkFile = QFileDialog::getOpenFileName(this, tr("Open File"), currentDir, tr("xlsx files(*.xlsx)"));

    }else{
        m_sWorkFile = QFileDialog::getOpenFileName(this, tr("Open File"), currentDir, tr("bin files(*.bin)"));
    }
    if(!m_sWorkFile.isEmpty()){
        ui->le_filename->setText(m_sWorkFile);
    }else{
        return;
    }

    if(index == 0){
        return;
    }

    m_vMatrixparms.clear();

    qDebug()<<"index:"<<index;

    switch (index) {
    case 1:
        parseCalibrationFile(m_sWorkFile, m_vMatrixparms);
        break;
    case 2:
        parseWarmsuppleFile(m_sWorkFile, m_vByteParms);
        break;
    case 3:
        parseFilterRmatrixFile(m_sWorkFile, m_vMatrixparms);
        break;
    case 4:
        parseFilterQmatrixFile(m_sWorkFile, m_vMatrixparms);
        break;
    case 5:
        parseFilterFactorFile(m_sWorkFile, m_vMatrixparms);
        break;
    default:

        break;
    }
}

void CFirmUpgPage::parseCalibrationFile(QString filename, QVector<double> &vparms){
    QXlsx::Document xlsx(filename);
    ui->te_textinfo->setTextColor(QColor("#238E23"));
    ui->te_textinfo->append(tr("##") + tr("The parameter file has been imported") + QString(":%1,").arg(m_sWorkFile) + tr("Please verify the parameter file data!"));
    ui->te_textinfo->append(QString::fromLocal8Bit("陀螺常温"));
    QXlsx::Worksheet *worksheet = xlsx.currentWorksheet();
    QVariant value = worksheet->read("A9");
    QMap<QString, QString> gyro_matrix;
    QVector<QString> gyro_matrix_name = {"Sx", "Mxy", "Mxz", "Myx", "Sy", "Myz", "Mzx", "Mzy", "Sz", "Bx", "By", "Bz"};
    QString showtext;
    for(int i = 0; i < 9; i++){
        value = worksheet->read(10 + i/3, i%3 + 1);
        vparms.append(value.toDouble());
        ui->te_textinfo->append(QString::number(value.toDouble(), 'd', 23));
    }

    for(int i = 0; i < 3; i++){
        value = worksheet->read(11,  'H' - 'A' + 1 + i);
        vparms.append(value.toDouble());
        ui->te_textinfo->append(QString::number(value.toDouble(), 'd', 23));
    }



    ui->te_textinfo->append(QString::fromLocal8Bit("加表常温"));
    qDebug()<<xlsx.selectSheet(QString::fromLocal8Bit("加表常温"));
    worksheet = xlsx.currentWorksheet();
    value = worksheet->read("A9");
    qDebug() << value.toString();
    for(int i = 0; i < 9; i++){
        value = worksheet->read(10 + i/3, i%3 + 1);
        vparms.append(value.toDouble());
        ui->te_textinfo->append(QString::number(value.toDouble(), 'd', 23));
    }

    for(int i = 0; i < 3; i++){
        value = worksheet->read(11,  'H' - 'A' + 1 + i);
        vparms.append(value.toDouble());
        ui->te_textinfo->append(QString::number(value.toDouble(), 'd', 23));
    }

    ui->te_textinfo->append(tr("##") + tr("After verification, please click") + tr("Cali Apply") + tr("Button for calibration parameter issuance!"));
}

QByteArray CFirmUpgPage::parseNeuralNet(QXlsx::Document &xlsx, QString sheetname, int type){
    QByteArray parmsbyte;
    float fval = 0.0;
    double dval = 0.0;
    QChar cval = '0x00';
    QDataStream stream(&parmsbyte, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    xlsx.selectSheet(sheetname);
    QXlsx::Worksheet *worksheet = xlsx.currentWorksheet();
    if(type == 0){
        //ANN_Bias_1
        ui->te_textinfo->append(sheetname);
        ui->te_textinfo->append("ANN_Bias_1:");
        dval = worksheet->read("A2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        //ANN_Bias_2
        ui->te_textinfo->append("ANN_Bias_2:");
        dval = worksheet->read("A20").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A21").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A22").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("A23").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        //ANN_Bias_3
        ui->te_textinfo->append("ANN_Bias_3:");
        dval = worksheet->read("A26").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        //ANN_Dense_2 D6-G7
        ui->te_textinfo->append("ANN_Dense_2:");
        dval = worksheet->read("D6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
    }

    if(type == 1){
        //ANN_Dense_2
        dval = worksheet->read("D8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        //ANN_Dense_3
        ui->te_textinfo->append("ANN_Dense_3:");
        dval = worksheet->read("D24").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D25").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D26").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D27").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
    }

    if(type == 2){
        QString str;
        QString fuc;
        //Hidden Layers Activation Function
        ui->te_textinfo->append("Hidden Layers Activation Function:");
        str = worksheet->read("I6").toString();
        fuc.append(str);
        str = worksheet->read("J6").toString();
        fuc.append(str);
        str = worksheet->read("K6").toString();
        fuc.append(str);
        str = worksheet->read("L6").toString();
        fuc.append(str);
        ui->te_textinfo->append(fuc);
        stream.writeRawData(fuc.toUtf8(), 4);
        stream.writeRawData("\0\0\0\0", 4);
        //Normalized Temperature Max
        ui->te_textinfo->append("Normalized Temperature Max:");
        stream.setFloatingPointPrecision(QDataStream::SinglePrecision);
        fval = worksheet->read("I11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Temperature Min
        ui->te_textinfo->append("Normalized Temperature Min:");
        fval = worksheet->read("I14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Temperature Mean
        ui->te_textinfo->append("Normalized Temperature Mean:");
        fval = worksheet->read("I17").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Temperature Diff Max
        ui->te_textinfo->append("Normalized Temperature Diff Max:");
        fval = worksheet->read("M11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Temperature Diff Min
        ui->te_textinfo->append("Normalized Temperature Diff Min:");
        fval = worksheet->read("M14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Temperature Diff Mean
        ui->te_textinfo->append("Normalized Temperature Diff Mean:");
        fval = worksheet->read("M17").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        //Normalized Raw Max
        ui->te_textinfo->append("Normalized Raw Max:");
        stream.setFloatingPointPrecision(QDataStream::DoublePrecision);
        dval = worksheet->read("Q11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'f', 10));
        stream << dval;
        //Normalized Raw Min
        ui->te_textinfo->append("Normalized Raw Min:");
        dval = worksheet->read("Q14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'f', 10));
        stream << dval;
        //Normalized Raw Mean
        ui->te_textinfo->append("Normalized Raw Mean:");
        dval = worksheet->read("Q17").toDouble();
        ui->te_textinfo->append( QString::number(dval, 'f', 10));
        stream << dval;
        //Bias Correct Value
        ui->te_textinfo->append("Bias Correct Value:");
        dval = worksheet->read("I20").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        //ANN_Dense_1
        ui->te_textinfo->append("ANN_Dense_1:");
        dval = worksheet->read("D2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("H2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("I2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("J2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("K2").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("D3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("E3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("F3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("G3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("H3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("I3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("J3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
        dval = worksheet->read("K3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 23));
        stream << dval;
    }
    //qDebug()<<"parms:"<<parmsbyte.toHex(' ')<<parmsbyte.size();
    return parmsbyte;
}

QByteArray CFirmUpgPage::parseAllTemp(QXlsx::Document &xlsx, QString sheetname, int type)
{
    QByteArray parmsbyte;
    float fval = 0;
    double dval = 0.0;
    QDataStream stream(&parmsbyte, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    xlsx.selectSheet(sheetname);
    QXlsx::Worksheet *worksheet = xlsx.currentWorksheet();
    if(type == 0){
        //Temperature_Point&Board_Temperature/°C
        ui->te_textinfo->append(sheetname);
        ui->te_textinfo->append(QString::fromLocal8Bit("Temperature_Point&Board_Temperature/°C:"));
        stream.setFloatingPointPrecision(QDataStream::SinglePrecision);
        fval = worksheet->read("A3").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B3").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C3").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E3").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A4").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B4").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C4").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E4").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A5").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B5").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C5").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E5").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A6").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B6").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C6").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E6").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A7").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B7").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C7").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E7").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A8").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B8").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C8").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E8").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A9").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B9").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C9").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E9").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A10").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B10").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C10").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E10").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E11").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A12").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B12").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C12").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E12").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A13").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B13").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C13").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E13").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("A14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("B14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("C14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
        fval = worksheet->read("E14").toFloat();
        ui->te_textinfo->append(QString::number(fval, 'f', 10));
        stream << fval;
    }

    if(type == 1){
        //Bias_Value/LSB x,y
        ui->te_textinfo->append("Bias_Value/LSB:");
        dval = worksheet->read("H3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
    }

    if(type == 2){
        //Bias_Value/LSB z + Scale_Factor/LSB x
        dval = worksheet->read("H11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("H14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("I14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("J14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        ui->te_textinfo->append("Scale_Factor/LSB:");
        dval = worksheet->read("M3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O3").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O4").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O5").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O6").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
    }

    if(type == 3){
        //Scale_Factor/LSB y,z
        dval = worksheet->read("M7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O7").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O8").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O9").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O10").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O11").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O12").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O13").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("M14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("N14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
        dval = worksheet->read("O14").toDouble();
        ui->te_textinfo->append(QString::number(dval, 'd', 20));
        stream << dval;
    }
    //qDebug()<<"read bytes:"<<parmsbyte.toHex(' ')<<parmsbyte.size();
    return parmsbyte;
}

QByteArray CFirmUpgPage::parseNormalTemp(QXlsx::Document &xlsx, QString sheetname)
{
    QByteArray parmsbyte;
    float fval = 0;
    double dval = 0.0;
    QDataStream stream(&parmsbyte, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    xlsx.selectSheet(sheetname);
    QXlsx::Worksheet *worksheet = xlsx.currentWorksheet();
    //Temperature/°C
    ui->te_textinfo->append(sheetname);
    ui->te_textinfo->append(QString::fromLocal8Bit("Temperature/°C:"));
    stream.setFloatingPointPrecision(QDataStream::SinglePrecision);
    fval = worksheet->read("A3").toFloat();
    ui->te_textinfo->append(QString::number(fval, 'f', 10));
    stream << fval;
    fval = worksheet->read("B3").toFloat();
    ui->te_textinfo->append(QString::number(fval, 'f', 10));
    stream << fval;
    fval = worksheet->read("C3").toFloat();
    ui->te_textinfo->append(QString::number(fval, 'f', 10));
    stream << fval;
    //Board_Temperature/°C
    ui->te_textinfo->append(QString::fromLocal8Bit("Board_Temperature/°C:"));
    fval = worksheet->read("E3").toFloat();
    ui->te_textinfo->append(QString::number(fval, 'f', 10));
    stream << fval;
    //Normal_Temperature_Bias/LSB
    ui->te_textinfo->append("Normal_Temperature_Bias/LSB:");
    stream.setFloatingPointPrecision(QDataStream::DoublePrecision);
    dval = worksheet->read("H3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("I3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("J3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    //Normal_Temperature_Scale_Factor/LSB
    ui->te_textinfo->append("Normal_Temperature_Scale_Factor/LSB:");
    dval = worksheet->read("M3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("N3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("O3").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    //Misalignment_Compensation_Matrix
    ui->te_textinfo->append("Misalignment_Compensation_Matrix:");
    dval = worksheet->read("A6").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("B6").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("C6").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("A7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("B7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("C7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("A8").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("B8").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("C8").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    //Bias_Correct_Val
    ui->te_textinfo->append("Bias_Correct_Val:");
    dval = worksheet->read("F7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("G7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    dval = worksheet->read("H7").toDouble();
    ui->te_textinfo->append(QString::number(dval, 'd', 23));
    stream << dval;
    //qDebug()<<"read bytes:"<<parmsbyte.toHex(' ')<<parmsbyte.size();
    return parmsbyte;
}

void CFirmUpgPage::parseWarmsuppleFile(QString filename, QVector<QByteArray> &vParseByte){
    //qDebug()<<"parseWarmsuppleFile:"<<filename;
    QXlsx::Document xlsx(filename);
    //qDebug()<<"parseWarmsuppleFile1";
    QByteArray bdatatypes;
    vParseByte.clear();
    QDataStream stream(&bdatatypes, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    QVector<short> sdatatype = {10, 11, 20, 21, 22, 23, 30, 31, 32, 33, 40, 41, 42, 50, 51, 52, 60, 61, 62, 70, 71, 72, 80, 81, 82, 90, 91, 92};
    for(short s:sdatatype){
        stream << s;
    }

    ui->te_textinfo->setTextColor(QColor("#238E23"));
    ui->te_textinfo->append(tr("##") + tr("The parameter file has been imported") + QString(":%1,").arg(m_sWorkFile) + tr("Please verify the parameter file data!"));
    qDebug()<<"datatype:"<<bdatatypes.toHex(' ');
    qDebug()<<"bdatatypes.mid(0, 2):"<<bdatatypes.mid(0, 2).toHex();
    vParseByte.append(bdatatypes.mid(0, 2) + parseNormalTemp(xlsx, QString::fromLocal8Bit("陀螺常温")));
    qDebug()<<"vParseByte.size:"<<vParseByte.size()<<"vParseByte.(0)="<<vParseByte.at(0).toHex();

    vParseByte.append(bdatatypes.mid(2, 2) + parseNormalTemp(xlsx, QString::fromLocal8Bit("加表常温")));
    vParseByte.append(bdatatypes.mid(4, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("陀螺全温"), 0));
    vParseByte.append(bdatatypes.mid(6, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("陀螺全温"), 1));
    vParseByte.append(bdatatypes.mid(8, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("陀螺全温"), 2));
    vParseByte.append(bdatatypes.mid(10, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("陀螺全温"), 3));
    vParseByte.append(bdatatypes.mid(12, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("加表全温"), 0));
    vParseByte.append(bdatatypes.mid(14, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("加表全温"), 1));
    vParseByte.append(bdatatypes.mid(16, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("加表全温"), 2));
    vParseByte.append(bdatatypes.mid(18, 2) +parseAllTemp(xlsx, QString::fromLocal8Bit("加表全温"), 3));
    vParseByte.append(bdatatypes.mid(20, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X陀螺神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(22, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X陀螺神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(24, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X陀螺神经网络参数"), 2));
    vParseByte.append(bdatatypes.mid(26, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y陀螺神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(28, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y陀螺神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(30, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y陀螺神经网络参数"), 2));
    vParseByte.append(bdatatypes.mid(32, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z陀螺神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(34, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z陀螺神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(36, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z陀螺神经网络参数"), 2));
    vParseByte.append(bdatatypes.mid(38, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X加表神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(40, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X加表神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(42, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("X加表神经网络参数"), 2));
    vParseByte.append(bdatatypes.mid(44, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y加表神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(46, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y加表神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(48, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Y加表神经网络参数"), 2));
    vParseByte.append(bdatatypes.mid(50, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z加表神经网络参数"), 0));
    vParseByte.append(bdatatypes.mid(52, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z加表神经网络参数"), 1));
    vParseByte.append(bdatatypes.mid(54, 2) +parseNeuralNet(xlsx, QString::fromLocal8Bit("Z加表神经网络参数"), 2));

    ui->te_textinfo->append(tr("##") + tr("After verification, please click") + tr("Temp Apply") + tr("Button for calibration parameter issuance!"));
}
void CFirmUpgPage::parseFilterRmatrixFile(QString, QVector<double> &){

}
void CFirmUpgPage::parseFilterQmatrixFile(QString, QVector<double> &){

}
void CFirmUpgPage::parseFilterFactorFile(QString, QVector<double> &){

}

void CFirmUpgPage::on_bt_startupdate_clicked()
{
    if(!m_sWorkFile.endsWith(".bin")){
        QMessageBox::information(this, tr("Info"), tr("Upgrade file format error, please select bin file!"));
        return;
    }
    if(m_bIsUpdating){
        QMessageBox::information(this, tr("Info"), tr("Already in the process of upgrading, please do not repeat the operation!"));
        return;
    }
    m_bIsUpdating = true;

    if(m_sSeriser == NULL){
        m_sSeriser = m_oseriPage->getServByPortN();
        if(m_sSeriser != NULL){
            connect(this, &CFirmUpgPage::sigStartUpdate, m_sSeriser, &CSseriService::slotStartUpdate);
            connect(m_sSeriser, &CSseriService::sigUpdateEnd, this, &CFirmUpgPage::slotEndUpdate);
        }
    }
    if(m_sSeriser == NULL){
        QMessageBox::critical(this, tr("Info"), tr("Please open the upgrade serial port first!"));
        m_bIsUpdating = false;
        return;
    }

    if(m_sWorkFile.isEmpty()){
        QMessageBox::critical(this, tr("Info"), tr("Please select the upgrade package file first!"));
        m_bIsUpdating = false;
        return;
    }


    m_oseriPage->setUpgStatus(true);
    m_sSeriser->setUpdateMode();
    ui->bt_startupdate->setDisabled(true);
    ui->bt_openfile->setDisabled(true);
    emit sigStartUpdate(m_sWorkFile);

}

void CFirmUpgPage::slotEndUpdate(int status ,QString result){
    if(status > 0){
        m_oseriPage->setUpgStatus(false);
        m_bIsUpdating = false;
        m_sSeriser = NULL;
        ui->bt_startupdate->setDisabled(false);
        ui->bt_openfile->setDisabled(false);
    }

    QFont font("微软雅黑", 12);
    font.setBold(true);
    ui->te_textinfo->setFont(font);

    switch (status) {
    case 1:
        ui->te_textinfo->setTextColor(Qt::red);
        QMessageBox::critical(this, tr("Error"), result);
        break;
    case 2:
        ui->te_textinfo->setTextColor(QColor("#238E23"));
        QMessageBox::information(this, tr("Info"), result);
        break;
    default:
        ui->te_textinfo->setTextColor(Qt::black);
    }
    QDateTime currentTime = QDateTime::currentDateTime();
    ui->te_textinfo->append(currentTime.toString("yyyy-MM-dd hh:mm:ss ") + result);
    ui->te_textinfo->moveCursor(QTextCursor::End);

}

void CFirmUpgPage::on_bt_stopupdate_clicked()
{
    if(!m_bIsUpdating){
        QMessageBox::critical(this, tr("Error"), tr("Not in the process of upgrading!"));
        return;
    }

    qDebug()<<"on_bt_stopupdate_clicked";
    if(m_sSeriser == NULL){
        m_sSeriser = m_oseriPage->getServByPortN();
    }
    if(m_sSeriser == NULL){
        QMessageBox::critical(this, tr("Error"), tr("Please open the upgrade serial port first!"));
        m_bIsUpdating = false;
        return;
    }
    connect(this, &CFirmUpgPage::sigStopUpdate, m_sSeriser, &CSseriService::slotStopUpdate);
    emit sigStopUpdate();
    m_bIsUpdating = false;
    disconnect(this, &CFirmUpgPage::sigStopUpdate, m_sSeriser, &CSseriService::slotStopUpdate);
}

void CFirmUpgPage::on_bt_getfirmvir_clicked()
{
    if(m_bIsUpdating){
        QMessageBox::information(this, tr("Info"), tr("Already in the process of querying/upgrading, please do not repeat the operation!"));
        return;
    }
    m_bIsUpdating = true;

    if(m_sSeriser == NULL){
        m_sSeriser = m_oseriPage->getServByPortN();
        if(m_sSeriser != NULL){
            connect(this, &CFirmUpgPage::sigVersionQuery, m_sSeriser, &CSseriService::slotVersionQuery);
            connect(m_sSeriser, &CSseriService::sigUpdateEnd, this, &CFirmUpgPage::slotEndUpdate);
        }
    }

    if(m_sSeriser == NULL){
        QMessageBox::critical(this, tr("Error"), tr("Please open the upgrade serial port first!"));
        m_bIsUpdating = false;
        return;
    }

    m_oseriPage->setUpgStatus(true);

    emit sigVersionQuery(true);

    m_bIsUpdating = false;
    disconnect(this, &CFirmUpgPage::sigVersionQuery, m_sSeriser, &CSseriService::slotVersionQuery);
    m_sSeriser = NULL;
}

void CFirmUpgPage::on_tb_isHide_clicked()
{
    if(m_bParmScroHide){
        ui->scrollArea->setHidden(false);
        ui->fm_opframe->setHidden(false);
        m_bParmScroHide = false;
        ui->tb_isHide->setIcon(QIcon(m_pixleft));
    }else{
        ui->scrollArea->setHidden(true);
        ui->fm_opframe->setHidden(true);
        m_bParmScroHide = true;
        ui->tb_isHide->setIcon(QIcon(m_pixright));
    }

}

void CFirmUpgPage::on_bt_Issu_clicked()
{
    QByteArray cmdByte;
    QByteArray parmsByte;
    int ival, isec, iold;
    float fx,fy,fz;
    double dx,dy,dz;
    unsigned short sval;
    QVector<float> vfparms;
    bool parmvalid = true;
    QString erroinfo;
    char cval;
    bool isolddev = ui->rb_isolddev->isChecked();
    bool isneedPwd = false;

    qDebug()<<"on_bt_Issu_clicked";

    //获取需要更新的参数
    int parmid = ui->scrollAreaWidgetContents->getCheckedParm();

    if(parmid == -1){
        QMessageBox::critical(this, tr("Info"), tr("Please check the parameter options!"));
        return;
    }

    if(!permitPwdCheck(parmid)){
        return;
    }

    switch (parmid) {
    case -2:
        //波特率
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x13);
        ui->scrollAreaWidgetContents->getBaurdParm(ival);
        parmsByte.resize(2);
        sval = ival / 100;
        memcpy(parmsByte.data(), &sval, 2);
        break;
    case -3:
        //输出频率
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x14);
        parmsByte.resize(2);
        ui->scrollAreaWidgetContents->getFrqParm(ival);
        sval = ival;
        memcpy(parmsByte.data(), &sval, 2);
        break;
    case -4:
        //坐标轴
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x1E);
        ui->scrollAreaWidgetContents->getSitParm(ival,isec);
        siteChangen2o(ival, isec, iold);
        if(iold == -1){
            QMessageBox::critical(this, tr("Error"), tr("The coordinate combination is invalid, please reset the combination!"));
            return;
        }
        parmsByte.resize(2);
        cval = ival;
        parmsByte[0] = cval;
        if(!isolddev){
            cval = isec;
            parmsByte[1] = cval;
        }
        break;
    case -5:
        //GNSS杆臂参数
        isneedPwd = true;
        parmsByte.resize(12);
        cmdByte.append(0xAA);
        cmdByte.append(0x18);
        ui->scrollAreaWidgetContents->getLevelArmParm(fx,fy,fz);
        if(qAbs(fx) > 100 || qAbs(fy) > 100 || qAbs(fz) > 100){
            parmvalid = false;
            erroinfo = tr("GNSS lever arm parameters are invalid!");
        }
        memcpy(parmsByte.data(), &fx, 4);
        memcpy(parmsByte.data() + 4, &fy, 4);
        memcpy(parmsByte.data() + 8, &fz, 4);
        break;
    case -6:
        //天线安装角度
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x19);
        ui->scrollAreaWidgetContents->getAnteParm(fx,fy,fz);
        if(qAbs(fx) > 360 || qAbs(fy) > 360 || qAbs(fz) > 360){
            parmvalid = false;
            erroinfo = tr("The antenna installation angle parameter is invalid!");
        }
        parmsByte.resize(12);
        memcpy(parmsByte.data(), &fx, 4);
        memcpy(parmsByte.data() + 4, &fy, 4);
        memcpy(parmsByte.data() + 8, &fz, 4);
        break;
    case -7:
        //gnss初始值
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x21);
        ui->scrollAreaWidgetContents->getGnssInitParm(vfparms);
        if(vfparms.at(0) > qAbs(360) || vfparms.at(1) > qAbs(360) || vfparms.at(2) > qAbs(360) || vfparms.at(3) > qAbs(360)){
            parmvalid = false;
            erroinfo = tr("The attitude angle parameter is invalid!");
        }
        if(vfparms.at(4) < 0 || vfparms.at(4) > 180){
            parmvalid = false;
            erroinfo = tr("The longitude parameter is invalid!");
        }
        if(vfparms.at(5) < 0 || vfparms.at(5) > 90){
            parmvalid = false;
            erroinfo = tr("The latitude parameter is invalid!");
        }
        parmsByte.resize(28);
        memcpy(parmsByte.data(), &vfparms.at(0), 4);
        memcpy(parmsByte.data() + 4, &vfparms.at(1), 4);
        memcpy(parmsByte.data() + 8, &vfparms.at(2), 4);
        memcpy(parmsByte.data() + 12, &vfparms.at(3), 4);
        memcpy(parmsByte.data() + 16, &vfparms.at(4), 4);
        memcpy(parmsByte.data() + 20, &vfparms.at(5), 4);
        memcpy(parmsByte.data() + 24, &vfparms.at(6), 4);
        break;
    case -8:
        //惯导安装偏差
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x1B);
        ui->scrollAreaWidgetContents->getAngDevParm(fx,fy,fz);
        if(qAbs(fx) > 360 || qAbs(fy) > 360 || qAbs(fz) > 360){
            parmvalid = false;
            erroinfo = tr("Inertial navigation installation deviation parameter is invalid!");
        }
        parmsByte.resize(12);
        memcpy(parmsByte.data(), &fx, 4);
        memcpy(parmsByte.data() + 4, &fy, 4);
        memcpy(parmsByte.data() + 8, &fz, 4);
        break;
    case -9:
        //后轮矢量
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x1A);
        ui->scrollAreaWidgetContents->getAfterWheelParm(fx,fy,fz);
        if(qAbs(fx) > 20 || qAbs(fy) > 20 || qAbs(fz) > 20){
            parmvalid = false;
            erroinfo = tr("Inertial navigation rear wheel vector parameters are invalid!");
        }
        parmsByte.resize(12);
        memcpy(parmsByte.data(), &fx, 4);
        memcpy(parmsByte.data() + 4, &fy, 4);
        memcpy(parmsByte.data() + 8, &fz, 4);
        break;
    case -10:
        //静态零偏
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x25);
        ui->scrollAreaWidgetContents->getBiasTimeParm(ival);
        if( ival < 0 || ival > 600){
            parmvalid = false;
            erroinfo = tr("The static zero bias time parameter is invalid!");
        }
        parmsByte.resize(2);
        sval = ival;
        memcpy(parmsByte.data(), &sval, 2);
        break;
    case -11:
        //debug模式
        cmdByte.append(0xAA);
        cmdByte.append(0xC1);
        ui->scrollAreaWidgetContents->getDebugFlagParm(ival);
        parmsByte.resize(1);
        cval = ival;
        parmsByte[0] = cval;
        break;
    case -12:
        //陀螺类型
        cmdByte.append(0xAA);
        cmdByte.append(0xC2);
        ui->scrollAreaWidgetContents->getFogTypeParm(ival);
        parmsByte.resize(1);
        cval = ival;
        parmsByte[0] = cval;
        break;
    case -13:
        //gps中断类型
        cmdByte.append(0xAA);
        cmdByte.append(0xC3);
        ui->scrollAreaWidgetContents->getGpsTypeParm(ival);
        parmsByte.resize(1);
        cval = ival;
        parmsByte[0] = cval;
        break;
    case -14:
        //数据输出类型
        cmdByte.append(0xAA);
        cmdByte.append(0xC4);
        ui->scrollAreaWidgetContents->getDataTypeParm(ival);
        parmsByte.resize(1);
        switch (ival) {
        case 0:
            parmsByte[0] = 0x00;
            break;
        case 1:
            parmsByte[0] = 0x02;
            break;
        case 2:
            parmsByte[0] = 0xFF;
            break;
        default:
            parmsByte[0] = 0x00;
        }
        break;
    case -15:
        //陀螺标度因数
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x35);
        ui->scrollAreaWidgetContents->getGyroCaliParm(dx, dy, dz);
        parmsByte.resize(32);
        memcpy(parmsByte.data(), &dx, 8);
        memcpy(parmsByte.data() + 8, &dy, 8);
        memcpy(parmsByte.data() + 16, &dz, 8);
        break;
    case -16:
        //加计标度因数
        isneedPwd = true;
        cmdByte.append(0xAA);
        cmdByte.append(0x36);
        ui->scrollAreaWidgetContents->getAddiCaliParm(dx, dy, dz);
        parmsByte.resize(32);
        memcpy(parmsByte.data(), &dx, 8);
        memcpy(parmsByte.data() + 8, &dy, 8);
        memcpy(parmsByte.data() + 16, &dz, 8);
        break;
    case -17:
        //SD卡操作命令
        cmdByte.append(0xAA);
        cmdByte.append(0xC5);
        uint8_t ufval,usval;
        ui->scrollAreaWidgetContents->getSdCardOpParm(ufval, usval);
        parmsByte.resize(2);
        memcpy(parmsByte.data(), &ufval, 1);
        memcpy(parmsByte.data() + 1, &usval, 1);
        break;
    }

    qDebug()<<"parmid:"<<parmid;

    if(!parmvalid){
        QMessageBox::critical(this, tr("Error"), erroinfo);
        return;
    }

    if(!parmsPortConnect()){
        return;
    }

    ui->bt_Issu->setDisabled(true);
    ui->bt_Issuall->setDisabled(true);

    //获取串口服务操作对象
    m_bIsUpdating = true;

    //发送下发参数信号
    ui->te_textinfo->setTextColor(QColor(Qt::black));
    ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdByte) + " " + tr("Message information") + ":"));
    ui->te_textinfo->append(cmdByte.toHex(' ').toUpper() + " | " + parmsByte.toHex(' ').toUpper());
    m_oseriPage->setUpgStatus(true);
    emit sigParmsUpdate(cmdByte, parmsByte, isolddev);

    setButtonStatus(true);
}

void CFirmUpgPage::slotParmsEnd(int status, QByteArray cmd, QByteArray bytearry){
    unsigned short sval;
    int ival;
    float fvalx, fvaly, fvalz;
    double dvalx, dvaly, dvalz;
    int sitex,sitey;
    bool isolddev = ui->rb_isolddev->isChecked();
    setButtonStatus(false);
    parmsPortDisconnect();

    if(status == 99){
        if(static_cast<unsigned char>(cmd[0]) == 0xAA && static_cast<unsigned char>(cmd[1]) == 0xC5){
            QMessageBox::information(this, tr("Info"), tr("Parameter command operation") + tr("SD Data Read successful!"));
        }
    }else if(status != 0){
        QMessageBox::critical(this, tr("Error"), tr("Parameter command operation") + tr("operation failed"));
        ui->te_textinfo->setTextColor(QColor(Qt::red));
        ui->te_textinfo->append(tr("##") + tr( + "Parameter issuance failed!"));
    }else{
        ui->te_textinfo->setTextColor(QColor("#238E23"));
        if(static_cast<unsigned char>(cmd[0]) == 0xAA && static_cast<unsigned char>(cmd[1]) == 0xF1){
            ui->te_textinfo->append(tr("##") + tr("Parameter solidification successful!"));
            QMessageBox::information(this, tr("Info"), tr("Parameter command operation") + tr("Parameter solidification successful!"));
        }else{
            ui->te_textinfo->append(tr("##") + tr("The parameters have been successfully issued. If you need to solidify them, please click the \"Solidify Parameters\" button!"));
            QMessageBox::information(this, tr("Info"), tr("The parameters have been successfully issued. If you need to solidify them, please click the \"Solidify Parameters\" button!"));
        }
        ui->te_textinfo->append(cmd.toHex(' ').toUpper() + " | " + bytearry.toHex(' ').toUpper());

        if((bytearry.size() == 64 && isolddev) || bytearry.size() == 320){
            if(bytearry.size() == 320){
                QVector<int> vdatalens = {14, 24, 14, 30, 48};
                QByteArray resdata(4, 0x01);  //跟旧协议补齐4个字节头，便于后面解析
                for(int i = 0; i < 5; i++){
                    resdata.append(bytearry.mid(8 + i * 64, vdatalens.at(i)));
                }
                bytearry.clear();
                bytearry = resdata;
            }

            memcpy(&sval, bytearry.data() + 4, 2);
            ival = sval;
            ui->scrollAreaWidgetContents->setFrqParm(ival);
            memcpy(&fvalx, bytearry.data() + 6, 4);
            memcpy(&fvaly, bytearry.data() + 10, 4);
            memcpy(&fvalz, bytearry.data() + 14, 4);
            ui->scrollAreaWidgetContents->setLevelArmParm(fvalx, fvaly, fvalz);
            memcpy(&fvalx, bytearry.data() + 18, 4);
            memcpy(&fvaly, bytearry.data() + 22, 4);
            memcpy(&fvalz, bytearry.data() + 26, 4);
            ui->scrollAreaWidgetContents->setAnteParm(fvalx, fvaly, fvalz);
            memcpy(&fvalx, bytearry.data() + 30, 4);
            memcpy(&fvaly, bytearry.data() + 34, 4);
            memcpy(&fvalz, bytearry.data() + 38, 4);
            ui->scrollAreaWidgetContents->setAfterWheelParm(fvalx, fvaly, fvalz);
            memcpy(&fvalx, bytearry.data() + 42, 4);
            memcpy(&fvaly, bytearry.data() + 46, 4);
            memcpy(&fvalz, bytearry.data() + 50, 4);
            ui->scrollAreaWidgetContents->setAngDevParm(fvalx, fvaly, fvalz);
            if(isolddev){
                int ival = bytearry[55];
                siteChangeo2n(ival, sitex, sitey);
            }else{
                sitex = bytearry[54];
                sitey = bytearry[55];
            }
            ui->scrollAreaWidgetContents->setSitParm(sitex, sitey);

            memcpy(&sval, bytearry.data() + 56, 2);
            ival = sval;
            ui->scrollAreaWidgetContents->setBiasTimeParm(ival);

            //新接口
            if(bytearry.size() == 134){
                QVector<float> fgnssinit;
                memcpy(&fvalx, bytearry.data() + 58, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 62, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 66, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 70, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 74, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 78, 4);
                fgnssinit.append(fvalx);
                memcpy(&fvalx, bytearry.data() + 82, 4);
                fgnssinit.append(fvalx);
                qDebug()<<"fgnssinit:"<<fgnssinit;
                ui->scrollAreaWidgetContents->setGnssInitParm(fgnssinit);

                memcpy(&dvalx, bytearry.data() + 86, 8);
                memcpy(&dvaly, bytearry.data() + 94, 8);
                memcpy(&dvalz, bytearry.data() + 102, 8);
                ui->scrollAreaWidgetContents->setGyroCaliParm(dvalx, dvaly, dvalz);

                memcpy(&dvalx, bytearry.data() + 110, 8);
                memcpy(&dvaly, bytearry.data() + 118, 8);
                memcpy(&dvalz, bytearry.data() + 126, 8);
                ui->scrollAreaWidgetContents->setAddiCaliParm(dvalx, dvaly, dvalz);
            }
        }

    }

    m_bIsUpdating = false;
    m_oseriPage->setUpgStatus(false);
    //不重启串口，避免有些参数设置重新生成文件不合适的情况
    //m_oseriPage->restartPort();
}


void CFirmUpgPage::siteChangen2o(int newx, int newy, int& old){
    //只有24种组合是有效组合
    if(newx == 0 && newy == 0){
        old = 1;
        return;
    }

    if(newx == 0 && newy == 5){
        old = 2;
        return;
    }

    if(newx == 1 && newy == 3){
        old = 3;
        return;
    }

    if(newx == 1 && newy == 2){
        old = 4;
        return;
    }

    if(newx == 0 && newy == 6){
        old = 5;
        return;
    }

    if(newx == 0 && newy == 4){
        old = 6;
        return;
    }

    if(newx == 1 && newy == 1){
        old = 7;
        return;
    }

    if(newx == 1 && newy == 7){
        old = 8;
        return;
    }

    if(newx == 2 && newy == 3){
        old = 9;
        return;
    }

    if(newx == 2 && newy == 2){
        old = 10;
        return;
    }

    if(newx == 3 && newy == 0){
        old = 11;
        return;
    }

    if(newx == 3 && newy == 6){
        old = 12;
        return;
    }

    if(newx == 2 && newy == 1){
        old = 13;
        return;
    }

    if(newx == 2 && newy == 7){
        old = 14;
        return;
    }

    if(newx == 3 && newy == 5){
        old = 15;
        return;
    }

    if(newx == 3 && newy == 4){
        old = 16;
        return;
    }

    if(newx == 4 && newy == 0){
        old = 17;
        return;
    }

    if(newx == 4 && newy == 5){
        old = 18;
        return;
    }

    if(newx == 5 && newy == 3){
        old = 19;
        return;
    }

    if(newx == 5 && newy == 2){
        old = 20;
        return;
    }

    if(newx == 4 && newy == 6){
        old = 21;
        return;
    }

    if(newx == 4 && newy == 4){
        old = 22;
        return;
    }

    if(newx == 5 && newy == 1){
        old = 23;
        return;
    }

    if(newx == 5 && newy == 7){
        old = 24;
        return;
    }

    old = -1;
}

void CFirmUpgPage::siteChangeo2n(int old, int &newx, int &newy){
    switch (old) {
    case 1:
        newx = 0;
        newy = 0;
        break;
    case 2:
        newx = 0;
        newy = 5;
        break;
    case 3:
        newx = 1;
        newy = 3;
        break;
    case 4:
        newx = 1;
        newy = 2;
        break;
    case 5:
        newx = 0;
        newy = 6;
        break;
    case 6:
        newx = 0;
        newy = 4;
        break;
    case 7:
        newx = 0;
        newy = 0;
        break;
    case 8:
        newx = 1;
        newy = 7;
        break;
    case 9:
        newx = 2;
        newy = 3;
        break;
    case 10:
        newx = 2;
        newy = 2;
        break;
    case 11:
        newx = 3;
        newy = 0;
        break;
    case 12:
        newx = 3;
        newy = 6;
        break;
    case 13:
        newx = 2;
        newy = 1;
        break;
    case 14:
        newx = 2;
        newy = 07;
        break;
    case 15:
        newx = 3;
        newy = 5;
        break;
    case 16:
        newx = 3;
        newy = 4;
        break;
    case 17:
        newx = 4;
        newy = 0;
        break;
    case 18:
        newx = 4;
        newy = 6;
        break;
    case 19:
        newx = 5;
        newy = 3;
        break;
    case 20:
        newx = 5;
        newy = 2;
        break;
    case 21:
        newx = 4;
        newy = 5;
        break;
    case 22:
        newx = 4;
        newy = 4;
        break;
    case 23:
        newx = 5;
        newy = 1;
        break;
    case 24:
        newx = 5;
        newy = 7;
        break;
    default:
        newx = 0;
        newy = 0;
        break;
    }
}


void CFirmUpgPage::on_bt_Issuall_clicked()
{
    QByteArray cmdByte;
    QByteArray parmsByte;
    int ival, isec, iold = 0;
    float fx,fy,fz;
    double dx,dy,dz;
    unsigned short sval;
    QVector<float> vfparms;
    char cval;
    bool isolddev = ui->rb_isolddev->isChecked();

    qDebug()<<"on_bt_Issuall_clicked";

    if(!parmsPortConnect()){
        return;
    }

    if(!permitPwdCheck(99)){
        return;
    }

    cmdByte.append(0xAA);
    cmdByte.append(0xF3);
    if(isolddev){
        parmsByte.resize(54);
    }else{
        parmsByte.resize(130);
    }
    ui->scrollAreaWidgetContents->getFrqParm(ival);
    sval = ival;
    memcpy(parmsByte.data(), &sval, 2);
    ui->scrollAreaWidgetContents->getLevelArmParm(fx,fy,fz);
    memcpy(parmsByte.data() + 2, &fx, 4);
    memcpy(parmsByte.data() + 6, &fy, 4);
    memcpy(parmsByte.data() + 10, &fz, 4);
    ui->scrollAreaWidgetContents->getAnteParm(fx,fy,fz);
    memcpy(parmsByte.data() + 14, &fx, 4);
    memcpy(parmsByte.data() + 18, &fy, 4);
    memcpy(parmsByte.data() + 22, &fz, 4);
    ui->scrollAreaWidgetContents->getAfterWheelParm(fx,fy,fz);
    memcpy(parmsByte.data() + 26, &fx, 4);
    memcpy(parmsByte.data() + 30, &fy, 4);
    memcpy(parmsByte.data() + 34, &fz, 4);
    ui->scrollAreaWidgetContents->getAngDevParm(fx,fy,fz);
    memcpy(parmsByte.data() + 38, &fx, 4);
    memcpy(parmsByte.data() + 42, &fy, 4);
    memcpy(parmsByte.data() + 46, &fz, 4);
    ui->scrollAreaWidgetContents->getSitParm(ival,isec);
    siteChangen2o(ival, isec, iold);
    if(iold == -1){
        QMessageBox::critical(this, tr("Error"), tr("The coordinate combination is invalid, please reset the combination!"));
        return;
    }
    if(!isolddev){
        cval = ival;
        parmsByte[50] = cval;
        cval = isec;
        parmsByte[51] = cval;
    }else{
        parmsByte[50] = 0x00;
        cval = iold;
        parmsByte[51] = cval;
    }
    ui->scrollAreaWidgetContents->getBiasTimeParm(ival);
    memcpy(parmsByte.data() + 52 , &ival, 2);

    if(!isolddev){
        vfparms.clear();
        ui->scrollAreaWidgetContents->getGnssInitParm(vfparms);
        memcpy(parmsByte.data() + 54, &vfparms.at(0), 4);
        memcpy(parmsByte.data() + 58, &vfparms.at(1), 4);
        memcpy(parmsByte.data() + 62, &vfparms.at(2), 4);
        memcpy(parmsByte.data() + 66, &vfparms.at(3), 4);
        memcpy(parmsByte.data() + 70, &vfparms.at(4), 4);
        memcpy(parmsByte.data() + 74, &vfparms.at(5), 4);
        memcpy(parmsByte.data() + 78, &vfparms.at(6), 4);

        ui->scrollAreaWidgetContents->getGyroCaliParm(dx, dy, dz);
        memcpy(parmsByte.data() + 82, &dx, 8);
        memcpy(parmsByte.data() + 90, &dy, 8);
        memcpy(parmsByte.data() + 98, &dz, 8);

        ui->scrollAreaWidgetContents->getAddiCaliParm(dx, dy, dz);
        memcpy(parmsByte.data() + 106, &dx, 8);
        memcpy(parmsByte.data() + 114, &dy, 8);
        memcpy(parmsByte.data() + 122, &dz, 8);
    }

    setButtonStatus(true);

    //获取串口服务操作对象
    m_bIsUpdating = true;

    //发送下发参数信号
    ui->te_textinfo->setTextColor(QColor(Qt::black));
    ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdByte) + " " + tr("Message information") + ":"));
    ui->te_textinfo->append(cmdByte.toHex(' ').toUpper() + " | " + parmsByte.toHex(' ').toUpper());
    m_oseriPage->setUpgStatus(true);
    emit sigParmsUpdate(cmdByte, parmsByte, isolddev);

}

void CFirmUpgPage::on_bt_ParmRead_clicked()
{
    QByteArray cmdByte;
    QByteArray parmsByte;
    QVector<QByteArray> vParmsData;
    cmdByte.append(0xAA);
    cmdByte.append(0xF4);

    if(!parmsPortConnect()){
        return;
    }

    setButtonStatus(true);

    bool isolddev = ui->rb_isolddev->isChecked();
    qDebug()<<"on_bt_ParmRead_clicked:"<<isolddev;

    if(isolddev){
        onlyCmdInform(cmdByte);
    }else{
        //必须是2的整数倍的命令，若是实际不够用0x000x00补齐
        parmsByte.append(0xAA);  //数据输出频率
        parmsByte.append(0x14);
        parmsByte.append(0xAA);  //GNSS杆臂参数
        parmsByte.append(0x18);
        parmsByte.append(0xAA);  //天线安装角度
        parmsByte.append(0x19);
        parmsByte.append(0xAA);  //后轮轴中心位置矢量
        parmsByte.append(0x1A);
        parmsByte.append(0xAA);  //角度安装偏差
        parmsByte.append(0x1B);
        parmsByte.append(0xAA);  //用户坐标轴
        parmsByte.append(0x1E);
        parmsByte.append(0xAA);  //静态测零偏时间
        parmsByte.append(0x25);
        parmsByte.append(0xAA);  //GNSS初始值
        parmsByte.append(0x21);
        parmsByte.append(0xAA);  //陀螺标定因数
        parmsByte.append(0x35);
        parmsByte.append(0xAA);  //加计标定因数
        parmsByte.append(0x36);

        qDebug()<<"parmsByte:"<<parmsByte.toHex(' ');

        ui->te_textinfo->setTextColor(QColor(Qt::black));
        ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdByte) + " " + tr("Message information") + ":"));
        ui->te_textinfo->append(cmdByte.toHex(' ').toUpper() + " | " + parmsByte.toHex(' ').toUpper());

        m_oseriPage->setUpgStatus(true);
        emit sigParmsUpdate(cmdByte, parmsByte, isolddev);
    }

}

void CFirmUpgPage::on_bt_FixParm_clicked()
{
    QByteArray cmdByte;

    if(!parmsPortConnect()){
        return;
    }

    setButtonStatus(true);

    cmdByte.append(0xAA);
    cmdByte.append(0xF1);

    onlyCmdInform(cmdByte);
}

void CFirmUpgPage::onlyCmdInform(QByteArray cmdbyte){

    QByteArray parmsByte;
    bool isolddev = ui->rb_isolddev->isChecked();

    ui->te_textinfo->setTextColor(QColor(Qt::black));
    ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdbyte) + " " + tr("Message information") + ":"));
    ui->te_textinfo->append(cmdbyte.toHex(' ').toUpper());

    m_oseriPage->setUpgStatus(true);
    emit sigParmsUpdate(cmdbyte, parmsByte, isolddev);
}

bool CFirmUpgPage::parmsPortConnect(){
    if(m_sSeriser == NULL){
        m_sSeriser = m_oseriPage->getServByPortN();
        if(m_sSeriser != NULL){
            qDebug()<<"connect sigParmsUpdate";
            connect(this, &CFirmUpgPage::sigParmsUpdate, m_sSeriser, &CSseriService::slotParmsUpdate);
            connect(this, &CFirmUpgPage::sigMultilParmsUpdate, m_sSeriser, &CSseriService::slotMultilParmsUpdate);
            connect(m_sSeriser, &CSseriService::sigParmsEnd, this, &CFirmUpgPage::slotParmsEnd);
            return true;
        }else{
            qDebug()<<"not connect sigParmsUpdate";
            QMessageBox::critical(this, tr("Info"), tr("Please open the serial port first!"));
            return false;
        }
    }

    if(m_sSeriser == NULL){
        QMessageBox::critical(this, tr("Info"), tr("Please open the serial port first!"));
        return  false;
    }
}

void CFirmUpgPage::parmsPortDisconnect(){
    if(!m_bIsUpdating){
        return;
    }
    disconnect(this, &CFirmUpgPage::sigParmsUpdate, m_sSeriser, &CSseriService::slotParmsUpdate);
    disconnect(m_sSeriser, &CSseriService::sigParmsEnd, this, &CFirmUpgPage::slotParmsEnd);
    disconnect(this, &CFirmUpgPage::sigMultilParmsUpdate, m_sSeriser, &CSseriService::slotMultilParmsUpdate);
    m_sSeriser = NULL;
    qDebug()<<"parmsPortDisconnect";
}

void CFirmUpgPage::on_bt_defaultparm_clicked()
{
    //恢复默认参数
    QByteArray cmdByte;
    if(!parmsPortConnect()){
        return;
    }

    if(!permitPwdCheck(99)){
        return;
    }

    setButtonStatus(true);

    cmdByte.append(0xAA);
    cmdByte.append(0xF2);

    onlyCmdInform(cmdByte);
}

QString CFirmUpgPage::getNameByCmd(QByteArray &cmd){
    for (int i = 0; i < parmCommands.size();i++) {
        if(parmCommands.at(i).fc == static_cast<uint8_t>(cmd[0]) && parmCommands.at(i).sc == static_cast<uint8_t>(cmd[1])){
            return m_parmNames.at(i);
        }

    }
}

void CFirmUpgPage::on_bt_backview_clicked()
{
    QTabWidget *parenwid = static_cast<QTabWidget *>(parentWidget()->parentWidget());
    parenwid->setCurrentIndex(0);
}

void CFirmUpgPage::setButtonStatus(bool stat){
    ui->bt_Issu->setDisabled(stat);
    ui->bt_Issuall->setDisabled(stat);
    ui->bt_FixParm->setDisabled(stat);
    ui->bt_ParmRead->setDisabled(stat);
    ui->bt_getfirmvir->setDisabled(stat);
    ui->bt_defaultparm->setDisabled(stat);
    ui->bt_startupdate->setDisabled(stat);
    ui->bt_calibration->setDisabled(stat);
    ui->bt_warmsupple->setDisabled(stat);
}

void CFirmUpgPage::on_comboBox_currentIndexChanged(int index)
{

}

void CFirmUpgPage::on_bt_warmsupple_clicked()
{
    QByteArray cmdByte;
    int index = ui->comboBox->currentIndex();
    if(index != 2){
        return;
    }

    if(!parmsPortConnect()){
        return;
    }

    if(!permitPwdCheck(99)){
        return;
    }

    setButtonStatus(true);

    cmdByte.append(0xAA);
    cmdByte.append(0x34);

    ui->te_textinfo->setTextColor(QColor(Qt::black));
    ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdByte) + " " + tr("Message information") + ":"));
    for(QByteArray arr:m_vByteParms){
        ui->te_textinfo->append(cmdByte.toHex(' ').toUpper() + " | " + arr.toHex(' ').toUpper());
    }

    m_oseriPage->setUpgStatus(true);
    emit sigMultilParmsUpdate(cmdByte, m_vByteParms, false);
}

void CFirmUpgPage::on_bt_calibration_clicked()
{
    QByteArray cmdByte;
    QByteArray parmsByte;
    int unitsize = 8;
    int parmsize = m_vMatrixparms.size();

    int index = ui->comboBox->currentIndex();
    if(index != 1){
        return;
    }

    if(!permitPwdCheck(99)){
        return;
    }

    //参数标定
    cmdByte.append(0xAA);
    cmdByte.append(0x30);
    unitsize = 8;
    parmsByte.resize(parmsize * unitsize);

    for (int i = 0; i < m_vMatrixparms.size(); i++) {
        double dval = m_vMatrixparms.at(i);
        //qDebug()<<"change to float value:"<<QString("%1").arg(dval, 20, 'f', 16);
        memcpy(parmsByte.data() + ( i * unitsize), &dval, unitsize);
    }

    if(!parmsPortConnect()){
        return;
    }

    setButtonStatus(true);

    //获取串口服务操作对象
    m_bIsUpdating = true;

    //发送下发参数信号
    ui->te_textinfo->setTextColor(QColor(Qt::black));
    ui->te_textinfo->append(tr("Configuration") + QString(" %1 ").arg(getNameByCmd(cmdByte) + " " + tr("Message information") + ":"));
    ui->te_textinfo->append(cmdByte.toHex(' ').toUpper() + " | " + parmsByte.toHex(' ').toUpper());
    m_oseriPage->setUpgStatus(true);
    emit sigParmsUpdate(cmdByte, parmsByte, false);
}

bool CFirmUpgPage::permitPwdCheck(int id){
    switch (id) {
    //设置debug模式，设置波特率，设置输出频率，设置输出数据，设置SDK存储不需要密码认证
    case -2:
    case -3:
    case -11:
    case -14:
    case -17:
        return true;
    default:
        break;
    }
    CInputDialog inputdlg;
    return inputdlg.exec();
}
