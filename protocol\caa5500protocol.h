﻿#ifndef CAA5500PROTOCOL_H
#define CAA5500PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

#define IMU_ONEDATACOUNT 13

class Caa5500Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit Caa5500Protocol(QObject *parent = nullptr);
    ~Caa5500Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    QStringList typeOneDataCalc(const char *);
    QStringList typeTwoDataCalc(const char *);
    QStringList typeThreeDataCalc(const char *);
    QStringList typeFourDataCalc(const char *);
    QStringList typeFiveDataCalc(const char *);
    QStringList typeSixDataCalc(QByteArray msg, bool &isLoss, bool &isCheckFail);
    QStringList typeSevenDataCalc(const char *);
    QStringList typeEightDataCalc(const char *);
    QStringList typeNineDataCalc(const char *);
    QStringList typeA0DataCalc(const char *);
    QString getStatString(int istat);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Struaa55head{
        unsigned short head;
        unsigned short len;         //packet all length
        unsigned short type;        //data type  1: imu  2: cacv 3: pps 4:gnss 5:magnetometer 6:gd watch
    }Struaa55head;
    typedef struct _scha63x_cacv {
        float cxx;
        float cxy;
        float cxz;
        float cyx;
        float cyy;
        float cyz;
        float czx;
        float czy;
        float czz;
        float bxx;
        float bxy;
        float bxz;
        float byx;
        float byy;
        float byz;
        float bzx;
        float bzy;
        float bzz;
    } scha63x_cacv;
    typedef struct _imuorgonedata {
        double timestamp;
        short acc_x;
        short acc_y;
        short acc_z;
        short gyro_x;
        short gyro_y;
        short gyro_z;
        short temp_due;
        short temp_uno;
    } imuorgonedata_t;
    typedef struct gdwrxdatainfo {
        unsigned short  navi_test_info_counter;
        unsigned int	gnssInfo_gpsweek;
        unsigned int 	gnssInfo_gpssecond;
        unsigned char   navi_test_info_pps_en;
        unsigned char   navi_test_info_gps_valid;
        unsigned char   ppsDelay;
        float   navi_test_info_gyroX;
        float   navi_test_info_gyroY;
        float   navi_test_info_gyroZ;
        float   navi_test_info_sensor_temp;
        float   navi_test_info_accelY;
        float   navi_test_info_accelX;
        float   navi_test_info_accelZ;
        double  gnssInfo_Lon;
        double  gnssInfo_Lat;
        float   gnssInfo_Altitude;
        float   gnssInfo_ve;
        float   gnssInfo_vn;
        float   gnssInfo_vu;
        float   gnssInfo_Pitch;
        float   gnssInfo_Roll;
        float   gnssInfo_Heading;
        unsigned char   gnssInfo_PositioningState;
        unsigned int    gnssInfo_rtkStatus;
        unsigned char   gnssInfo_StarNum;
        unsigned int    canInfo_counter;
        float   canInfo_data_WheelSpeed_Front_Left;
        float   canInfo_data_WheelSpeed_Front_Right;
        float   canInfo_data_WheelSpeed_Back_Left;
        float   canInfo_data_WheelSpeed_Back_Right;
        float   canInfo_data_WheelSteer;
        float   canInfo_data_OdoPulse_1;
        float   canInfo_data_OdoPulse_2;
        float   canInfo_data_Gear;
        unsigned char Adj_Nav_Standard_flag;
        double  Adj_gnssAtt_from_vehicle2_0;
        double  Adj_gnssAtt_from_vehicle2_1;
        double  Adj_gnssAtt_from_vehicle2_2;
        double  Adj_acc_off_0;
        double  Adj_acc_off_1;
        double  Adj_acc_off_2;
        double  Adj_gyro_off_0;
        double  Adj_gyro_off_1;
        double  Adj_gyro_off_2;
        float   gnss_trackTrue;
        unsigned int    result_Nav_Status;
        unsigned char   result_Nav_Standard_flag;
        unsigned char   result_imuSelect;
        unsigned char   result_memsType;
        unsigned char   result_use_gps_flag;
        unsigned char   result_fusion_source;
        unsigned int	gnssInfo_headingStatus;
        unsigned int    gnssInfo_gpssecond982;
        float fog0;
        double  longitude;
        double  latitude;
        float   altitude;
        float   ve;
        float   vn;
        float   vu;
        float   Pitch;
        float   Roll;
        float   Heading;
    } gdwrxdata_t;
    typedef struct _imudata {
        unsigned int heartick;
        unsigned int wintick;
        double tt;
        float temperature_due;
        float temperature_uno;
        float acc_x;
        float acc_y;
        float acc_z;
        float gyro_x;
        float gyro_y;
        float gyro_z;
    } imudata_t;
    typedef struct gnessrxdatahead {
        unsigned char sync0;
        unsigned char sync1;
        unsigned char sync2;
        unsigned char cpuidle;

        unsigned short messageid;
        unsigned short messagelength;

        unsigned char timeref;
        unsigned char timestatus;

        unsigned short wn;
        unsigned int   ms;
        unsigned int   res00;

        unsigned char version;
        unsigned char leapsec;
        unsigned short delayms;
    } gnessrxdatahead_t;
    typedef struct gnessrxcontent {
        char gnss[4];
        unsigned char length;

        unsigned char year;
        unsigned char month;
        unsigned char day;
        unsigned char hour;
        unsigned char minute;
        unsigned char second;
        unsigned char postype;  //rtkstatus;
        unsigned char headingstatus;
        unsigned char numgpssta;
        unsigned char numdbssta;
        unsigned char numglosta;

        float   baselineN;
        float   baselineE;
        float   baselineU;
        float   baselineNstd;
        float   baselineEstd;
        float   baselineUstd;

        float   headling;
        float   pitch;
        float   roll;
        float   speed;
        float   velocityofnorth;
        float   velocityofeast;
        float   velocityofup;
        float   xigemavx;
        float   xigemavy;
        float   xigemavz;

        double  lat;
        double  lon;
        double  alt;
        double  ecefx;
        double  ecefy;
        double  ecefz;

        float  xigemalat;
        float  xigemalon;
        float  xigemaalt;
        float  xigemaecefx;
        float  xigemaecefy;
        float  xigemaecefz;

        double  baselat;
        double  baselon;
        double  basealt;
        double  seclat;
        double  seclon;
        double  secalt;

        int     gpsweeksecond;
        float   deffage;
        float   speedheading;
        float   undulation;
        float   remainfloat3;
        float   remainfloat4;

        unsigned char   numgalsta;
        unsigned char   speedtype;
        unsigned char   remainchar3;
        unsigned char   remainchar4;

        unsigned int    crc;
    } gnessrxcontent_t;
    typedef struct _Vector3f {
        float x;
        float y;
        float z;
    } Vector3f;
    typedef struct _Vector4f {
        float w;
        float x;
        float y;
        float z;
    } Vector4f;
    typedef struct _VimufIn {
        uint32_t	 timestamp;
        Vector3f CelebrationAcceleration;
        Vector3f CoordinateCelebrationGyrol;
        Vector3f OriginalMagnetometer;
        Vector3f CelebrationMagnetometer;
        Vector3f Angularvelocity;
        Vector4f Quaternion;
        Vector3f Eulerangle;
        Vector3f LinearAcceleration;
        float Temperature;
    } VimufIn;
    typedef struct _imuorgdata {
        imuorgonedata_t onedata[IMU_ONEDATACOUNT];
    } imuorgdata_t;
    typedef struct _driversettings {
        char projectname[16];
        char mcutype;
        char datatype;
        int imuframe;
        int gnssframe;
        int canframe;
        int ppsframe;
        int gdwframe;
        int magnetframe;
        int pressureframe;
        int opticalgryoframe;
        int imu2600hzframe;
        int ins912fpgaframe;
        int turntableaccgyroframe;
        int gdwframe912;
        int earlynavoutframe;
        int earlynavoutframeT;
        int reserved[14];
    } driversettings_t;
    typedef struct gdwrxdata912info {
        uint16_t datalength;
        uint16_t     selftestingcode;
        uint16_t     fpgaversion;
        uint16_t		watchversion;
        uint16_t     Tgears;
        float   Tflwheelspeed;
        float   Tfrwheelspeed;
        float   Tblwheelspeed;
        float   Tbrwheelspeed;
        uint16_t     Tcaninfocounter;
        int32_t     fogx;
        int32_t     fogy;
        int32_t     fogz;
        int16_t     fogtemperaturex;
        int16_t     fogtemperaturey;
        int16_t     fogtemperaturez;
        uint32_t   accelerometerx;
        uint32_t   accelerometery;
        uint32_t   accelerometerz;
        int16_t     accelerometertemp;
        uint16_t     reserve1e;
        uint16_t     reserve1f;
        uint16_t     Reserve20;
        uint16_t     gnssweek;
        uint32_t     millisecondofweek;
        uint32_t     secondofweek;
        uint32_t     ppsdelay10ns;
        uint16_t     gpsstarnumber;
        uint16_t     rtkstatus;
        uint16_t     speedstatus;
        uint16_t     truenorthtrack[3];
        float	northvelocity;
        float   eastvelocity;
        float   upvelocity;
        uint16_t     positionstatus;
        uint16_t     directionoflat;
        double  latitude;
        uint16_t     directionoflon;
        double  longitude;
        double  altitude;
        uint16_t     Headingstate;
        uint32_t     baselength;
        float   roll;
        float   pitch;
        float   yaw;
        uint16_t gears;
        uint16_t caninfocounter;
        float   flwheelspeed;
        float   frwheelspeed;
        float   blwheelspeed;
        float   brwheelspeed;
        float	timeprecisionZ;
        float	verticalprecZ;
        float	horizontalprecZ;
        float	northprecisionZ;
        float	eastprecisionZ;
        float	endheightangleZ;
        uint16_t checksum;
        uint16_t frameindex;
        //The following are the results of the algorithm
        double	Alongitude;
        double	Alatitude;
        float	Aaltitude;
        float	Ave;
        float	Avn;
        float	Avu;
        float	Apitch;
        float	Aroll;
        float	Aheading;
        uint16_t		checksumA;
    } gdwrxdata912_t;
    typedef struct _Struaa5501{
        Struaa55head st5500h;
        int packet;
        int packetT;
        imuorgdata_t imudata;
        unsigned short checksum;
    }Struaa5501;                    //imuorgdatasendtopc_t
    typedef struct _Struaa5502 {
        Struaa55head st5500h;
        scha63x_cacv cacv;
        unsigned short checksum;
    } Struaa5502;                    //rxscha63xcacv_t
    typedef struct _Struaa5503 {
        Struaa55head st5500h;
        unsigned int ppscount;
        double tt;
        int packet;
        int packetT;
        unsigned short checksum;
    } Struaa5503;                     //gnessppsTX_t
    typedef struct _Struaa5504 {
        Struaa55head st5500h;
        gnessrxdatahead_t   head;
        gnessrxcontent_t    data;
        float tt;
        int packet;
        int packetT;
        unsigned short checksum;
    } Struaa5504;                     //gnessrxdatainfoTX
    typedef struct _Struaa5505 {
        Struaa55head st5500h;
        double tt;
        int packet;
        int packetT;
        VimufIn	magnetdata;
        unsigned char checksum;
        unsigned char tail;
    } Struaa5505;                      //rxonemagnetinfo_t
    typedef struct _Struaa5506 {
        Struaa55head st5500h;
        gdwrxdata_t  gdwdata;
        float tt;
        int packet;
        int packetT;
        unsigned short checksum;
    } Struaa5506;                      //gdwrxdataTX_t
    typedef struct _Struaa5507 {
        Struaa55head st5500h;
        driversettings_t settings;
        unsigned short checksum;
    } Struaa5507;                      //driversettingstopc_t
    typedef struct _Struaa5508 {
        Struaa55head st5500h;
        float tt;
        int packet;
        int packetT;
        imudata_t  data;
        unsigned char checksum;
        unsigned char tail;
    } Struaa5508;                     //rxoneimuinfo_t
    typedef struct _Struaa5509 {
        Struaa55head st5500h;
        gdwrxdata912_t  gdwdata;
        float tt;
        int packet;
        int packetT;
        unsigned short checksum;
    } Struaa5509;                      //gdwrxdata912TX_t
    typedef struct _Struaa5a0 {
        unsigned short head;
        unsigned char len;
        unsigned char num;
        unsigned char cmd;
        unsigned int packetn;
        unsigned char status;
        unsigned char exstatus;
        unsigned short month;
        unsigned short day;
        unsigned short hour;
        unsigned short minutes;
        unsigned short second;
        unsigned short missec;
        unsigned char inershaftstlow;
        unsigned char inershaftsthigh;
        float inersit;
        float inerspeed;
        unsigned char reser[10];
        unsigned char outershaftstlow;
        unsigned char outershaftsthigh;
        float outersit;
        float outerspeed;
        unsigned char checksum;
        unsigned char tail;
    } Struaa55a0;                      //gdwrxdata912TX_t
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
private:
    bool m_bNeedWrite = true;

private:
};

#endif // CAA5500PROTOCOL_H
