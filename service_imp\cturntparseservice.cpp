﻿#include "cturntparseservice.h"
#include "ctlhtools.h"
#include <QDebug>

CTurnTParseService::CTurnTParseService(QObject *parent) : QObject(parent)
{

}

bool CTurnTParseService::axleParse(int cmd_type, const QStringList &sitem, QList<ES_PAIR> &Cmdlist){
    //命令类型非法
    if(cmd_type > 2){
        qDebug()<<"invalid cmd_type";
        return false;
    }

    qDebug()<<"params:"<<cmd_type<<sitem;

    //延时命令
    if(cmd_type == 2){
        if(sitem.at(1) != "UntilReach" && !CTlhTools::isNumeric(sitem.at(1))){
            qDebug()<<"invalid parasm:"<<cmd_type;
            return false;
        }
        if(sitem.size() == 3 && !CTlhTools::isValidPrefix(sitem.at(2))){
            qDebug()<<"invalid ys filename:"<<sitem.at(2);
            return false;
        }
        Cmdlist.append(ES_PAIR(E_DELY, QStringList(sitem.begin() + 1, sitem.end())));
        return true;
    }

    //位置 速率 摇摆 找零 停车
    if(sitem.at(1) == "Pos"){
        if(sitem.size() < 5 || !CTlhTools::isfloatNumeric(sitem.at(2)) || !CTlhTools::isfloatNumeric(sitem.at(3)) || !CTlhTools::isfloatNumeric(sitem.at(4))){
            return false;
        }

        if(cmd_type == 0){
            Cmdlist.append(ES_PAIR(E_INPOS, QStringList(sitem.begin() + 2, sitem.end())));
        }else{
            Cmdlist.append(ES_PAIR(E_OUTPOS, QStringList(sitem.begin() + 2, sitem.end())));
        }
        return true;
    }else if(sitem.at(1) == "Speed"){
        if(sitem.size() < 4 || !CTlhTools::isfloatNumeric(sitem.at(2)) || !CTlhTools::isfloatNumeric(sitem.at(3))){
            return false;
        }
        if(cmd_type == 0){
            Cmdlist.append(ES_PAIR(E_INSPEED, QStringList(sitem.begin() + 2, sitem.end())));
        }else{
            Cmdlist.append(ES_PAIR(E_OUTSPEED, QStringList(sitem.begin() + 2, sitem.end())));

        }
        return true;
    }else if(sitem.at(1) == "Sway"){
        if(sitem.size() < 4 || !CTlhTools::isfloatNumeric(sitem.at(2)) || !CTlhTools::isfloatNumeric(sitem.at(3))){
            return false;
        }
        if(cmd_type == 0){
            Cmdlist.append(ES_PAIR(E_INSWAY, QStringList(sitem.begin() + 2, sitem.end())));
        }else{
            Cmdlist.append(ES_PAIR(E_OUTSWAY, QStringList(sitem.begin() + 2, sitem.end())));
        }
        return true;
    }else if(sitem.at(1) == "Fz"){
        if(cmd_type == 0){
            Cmdlist.append(ES_PAIR(E_INFZ, QStringList("")));
        }else{
            Cmdlist.append(ES_PAIR(E_OUTFZ, QStringList("")));
        }
        return true;
    }else if(sitem.at(1) == "Stop"){
        if(cmd_type == 0){
            Cmdlist.append(ES_PAIR(E_INSTOP, QStringList("")));
        }else{
            Cmdlist.append(ES_PAIR(E_OUTSTOP, QStringList("")));
        }
        return true;
    }else{
        return false;
    }
}

bool CTurnTParseService::turnTableTextParse(const QString &text, QList<ES_PAIR> &Cmdlist){
    QStringList severycmd = text.split("\n");
    qDebug()<<"severycmd:"<<severycmd<<text;
    for(QString str:severycmd){
        //qDebug()<<"str:"<<str<<str.mid(0,1)<<str.right(1);
        str.replace("\r", "");
        str = str.trimmed();
        //qDebug()<<"str:"<<str;
        if(!str.startsWith("#") || !str.endsWith(";")){
            continue;
        }
        str.replace(";", "");
        QStringList sitem = str.split(",");
        qDebug()<<sitem.size();
        if(sitem.at(0) == "#Inn"){
            if(!axleParse(0, sitem, Cmdlist)){
                return  false;
            }
        }else if(sitem.at(0) == "#Out"){
            if(!axleParse(1, sitem, Cmdlist)){
                return  false;
            }
        }else if(sitem.at(0) == "#YS"){
            if(!axleParse(2, sitem, Cmdlist)){
                return false;
            }
        }else{
            return false;
        }
    }
    qDebug()<<"turnmap size:"<<Cmdlist.size();
    return true;
}


