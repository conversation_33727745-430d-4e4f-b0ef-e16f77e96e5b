#ifndef CCOMMPAGE_H
#define CCOMMPAGE_H

#include <QWidget>
#include <QMap>
#include <QPointer>
#include <QTimer>
#include <QTimerEvent>
#include "csseriservice.h"
#include "ctcpservice.h"
#include "icommservice.h"
#include "cprotoparamdata.h"
#include "coutlineparservice.h"

QT_BEGIN_NAMESPACE
namespace Ui { class CCommPage; }
QT_END_NAMESPACE

/*
* 通讯页面类 - 统一管理串口和TCP通讯
*/
class CCommPage : public QWidget
{
    Q_OBJECT

public:
    explicit CCommPage(QWidget *parent = nullptr);
    ~CCommPage();

    enum CommType {
        COMM_SERIAL = 0,
        COMM_TCP = 1
    };

    void needDisableCommParm(bool benable);
    void openOrCloseCommPort();
    void findMsgHeadAuto(const QByteArray &barr, QByteArrayList &headlist);
    void ReadTextEditData(const QByteArray &data);
    void commWriteData();
    bool getStatusByConfig(QString config);
    QStringList getAvilaCom();
    void showEvent(QShowEvent *event) override;
    void setCommPageStatus();
    void setProtoParam(CProtoParamData *p){
        m_pparam = p;
    }
    QPointer<ICommService> getServByConfig(QString config);
    QPointer<ICommService> getServByConfig();
    void setUpgStatus(bool);
    void restartPort();
    void catPackData(bool isNeedClear);

private slots:
    void on_bt_CommOpen_clicked();
    void on_bt_CommClose_clicked();
    void on_bt_CommClear_clicked();
    void on_bt_CommSend_clicked();
    void on_cb_CommType_currentIndexChanged(int index);
    void on_cb_PortN_currentTextChanged(const QString &text);
    void on_cb_BaudR_currentTextChanged(const QString &text);
    void on_cb_TcpIP_currentTextChanged(const QString &text);
    void on_cb_TcpPort_currentTextChanged(const QString &text);

private:
    Ui::CCommPage *ui;
    bool m_bIsHex;
    QMap<QString, QPointer<ICommService>> m_commSerMap;
    void timerEvent(QTimerEvent *e) override;
    void closeEvent(QCloseEvent *event) override;
    bool m_IsOpen;
    QByteArray m_CommBuff;
    int m_checkSize;
    CProtoParamData *m_pparam;
    bool m_bIsAutoStart;   //离线解析状态
    QString m_sWorkFile;
    QByteArray m_lRecvshowArr;
    COutLineParService *m_olser;
    QStringList m_sComLists;
    bool m_isInValid;
    CommType m_currentCommType;

    void initUI();
    void updateCommTypeUI();
    QString getCurrentConfig();
    ICommService* createCommService(CommType type);

public slots:
    //设置当前通讯配置
    void slotSetConfig(const QString &config);
    //设置当前波特率
    void slotSetBaudR(const QString &text);
    //设置TCP IP
    void slotSetTcpIP(const QString &text);
    //设置TCP端口
    void slotSetTcpPort(const QString &text);

signals:
    //通讯状态同步信号
    void sigUpgMCommP(const QString &config, const QString &param, const bool opstat);
    //通讯打开信号
    void sigCommOpen(const QString config, const int optype, const QString filepre);
    //通讯插拔信号
    void sigPortDataChange(const QStringList &comlist);
    //数据接收信号
    void sigDataReceived(const QByteArray &data);
    //TCP客户端连接信号
    void sigTcpClientConnected(const QString &clientInfo);
    //TCP客户端断开信号
    void sigTcpClientDisconnected(const QString &clientInfo);
    //TCP错误信号
    void sigTcpError(const QString &error);
    //通讯写入信号
    void SigCommWrite(const QByteArray &arr, int &len);
};

#endif // CCOMMPAGE_H
