﻿QTabBar::tab{
        font-family:"Arial Black";
        font-size:12px;
        font-weight:bold;
        color:#446600;
        border-top-left-radius:5px;
        border-top-right-radius:5px;
        min-width:80px;
        min-height:20px;
        padding:5px;
        border:1px solid #777;
}

QTabBar::tab::selected{
        background-color:rgb(242, 159, 178);
}

QTabBar::tab::!selected{
        background-color:rgb(255, 244, 255);
}

QLabel#keylab{
       font-family:"Microsoft YaHei";
       font-size:14px;
       color:#BDC8E2;
       background-color:#2E3648;
}

QLabel#valuelab{
       font-family:"Microsoft YaHei";
       font-size:14px;
       color:#2e2eff;
       background-color:#d5f9b3;
}

QLabel#labfloss{
       font-family:"Microsoft YaHei";
       font-size:16px;
       color:#f30079;
       background-color:#fffee0;
}

QLabel#labcheck{
       font-family:"Microsoft YaHei";
       font-size:16px;
       color:#f30079;
       background-color:#fffee0;
}

QLabel#labtime{
       font-family:"Microsoft YaHei";
       font-size:16px;
       color:#FFFFFF;
       background-color:#a8a8a8;
}

QLabel#labfreq{
       font-family:"Microsoft YaHei";
       font-size:16px;
       color:#4B0082;
       background-color:#fffee0;
}

QLabel#labexcept{
       font-family:"Microsoft YaHei";
       font-size:16px;
       color:#f30079;
       background-color:#fffee0;
}

QLabel#lb_rollview,#lb_pitchview,#lb_yawview{
    background-color: transparent;
}

QLabel#lb_port,QLabel#lb_baurd{
    font-weight:bold;
    font-family:"Arial";
    font-size:14px;
    color:#855E42;
}

QComboBox {
    background-color: #C0D9D9;
    border: 1px solid #ccc;
    padding: 5px;
    border-radius: 5px;
    font-size: 14px;
}

QComboBox::drop-down {
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #eeeeee;
    width: 20px;
    height: 20px;
}

QComboBox::drop-down:hover {
    background-color: #e0e0e0;
}

QComboBox QAbstractItemView {
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #ffffff;
}

QPushButton {
    background-color: #f0fff0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
    min-width: 60px;
}

QPushButton::hover {
    background-color: #eee0e0;
}

QPushButton::pressed {
    background-color: #d0d0d0;
}

QLabel::indicator {
    width: 20px;
    height: 20px;
    background-color: #e0e0e0;
    border: 1px solid #ccc;
    border-radius: 5px;
}

QLabel#lab_motionst2{
    font-family:"Arial Black";
    font-size:16px;
    font-weight:bold;
    border-radius: 20px;
}

QLabel#lab_motionst1{
    font-family:"Arial Black";
    font-size:16px;
    font-weight:bold;
    border-radius: 20px;
}

QLabel#lab_instat,#lab_outstat{
    border: 0px;
    min-width: 300px;
    min-height: 300px;
    background-color: transparent;
}

QLabel#lab_scriptinfo{
    border: 0px;
}


CLightLabel{
    border: 0px;
    background-color: transparent;
    min-height: 30px;
}

#tab_CustomerView QLabel{
    border: 0px;
    font-family:"微软雅黑";
    color:#8B4513;
    font-size: 17px;
}

.QLabel {
    background-color: #f0f8ff;
    border: 1px solid #cd853f;
    border-radius: 1px;
    min-height: 30px;
    padding: 0px;
    font-size: 12px;
    font-family:Calibri;
}

QLabel#lb_pich{
    border: 1px solid #BFEFFF;
    border-radius: 2px;
    font-size: 16px;
    font-weight:bold;
    color: #F0F8FF;
    background-color: transparent;
}

QLabel#lb_yaw{
    border: 1px solid #BFEFFF;
    border-radius: 2px;
    font-size: 16px;
    font-weight:bold;
    color: #F0F8FF;
    background-color: transparent;
}

QLabel#lb_roll{
    border: 1px solid #BFEFFF;
    border-radius: 2px;
    font-size: 16px;
    font-weight:bold;
    color: #F0F8FF;
    background-color: transparent;
}

QCheckBox {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
}

QCheckBox::indicator:checked {
    background-color: #9f5f9f;
    border-color: #333333;
}

QCheckBox::indicator:pressed {
    background-color: #d0d0d0;
}

QRadioButton {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 5px;
    font-size: 14px;
}

QRadioButton::indicator {
    width: 20px;
    height: 20px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 10px;
}

QRadioButton::indicator:checked {
    background-color: #a67d3d;
    border-color: #333333;
}

QRadioButton::indicator:pressed {
    background-color: #d0d0d0;
}

QToolButton {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 1px;
    font-size: 14px;
}

QToolButton::hover {
    background-color: #e0e0e0;
}

QToolButton::pressed {
    background-color: #d0d0d0;
}

QLineEdit {
   background-color: #f0f0f0;
   border: 1px solid #ccc;
   border-radius: 5px;
   padding: 5px;
   font-size: 14px;
   font-family: "Arial";
}

#tab_CustomerView QLineEdit{
    font-family:"微软雅黑";
    font-size: 16px;
}

QLineEdit::hover {
   background-color: #e0e0e0;
}

QLineEdit::pressed {
   background-color: #d0d0d0;
}

QGroupBox {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
}

QGroupBox::title {
    background-color: #FFFACD;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px;
    font-size: 14px;
}

QProgressBar {
    border: 2px solid grey;
    border-radius: 5px;
    background-color: #CDCDCD;
    text-align: center;
}

QPlainTextEdit{
    background: #f0f0f0;
    font: 12pt "Arial";
}

QTextEdit{
    background: #f0f0f0;
    font: 12pt "Arial";
}

QSpinBox{
    color: #42426F;
    background-color: #C0D9D9;
    border: 1px solid #527F76;
    border-radius: 4px;
}

QTimeEdit{
    color: #42426F;
    background-color: #C0D9D9;
    border: 1px solid #527F76;
    border-radius: 4px;
}

QDialog{
    background-color: #E9C2A6;
    border-radius:10px;
    border: 2px solid #8E2323;
}

QListView{
    background-color:#FFF8DC;
}

QListView::item:selected{
    color:#DCDCDC;
    background:#383838;
}

QFrame#fm_navstatus {
    border:0px;
    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #F3CFB7, stop: 0.8 #F1C0A0);
}


QFrame#btframe {
    border:2px;
    background-color: transparent;
}

QGraphicsView{
    border:2px;
    background-color: transparent;
}

.QTabWidget{
     border: 0px;
}

QGroupBox#gb_navstat{
    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 0, stop: 0 #FBD7E3, stop: 1 #ABECEA);
}

#CAboutDlg QLabel{
    font-family:"Microsoft YaHei";
    border: 0px;
    font-size: 14px;
}

#CAboutDlg #lb_about{
    font-size: 16px;
    font-weight:bold;
}

#CAboutDlg #lb_desc{
    font-weight:bold;
}

#CAboutDlg #lb_ccompany{
    font-weight:bold;
}

#CAboutDlg #lb_ecompany{
    font-weight:bold;
}

QStackedWidget{
    background-color: transparent;
}

CFlyViewPage{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
    stop:0 #542e87,
    stop:0.33 #2E2249,
    stop:0.63 #2E2249,
    stop:1 #542e87);
}




