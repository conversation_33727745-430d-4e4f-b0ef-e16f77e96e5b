#ifndef CTCPSERVICE_H
#define CTCPSERVICE_H

#include "icommservice.h"
#include <QTcpServer>
#include <QTcpSocket>
#include <QThread>
#include <QPointer>
#include <QTimer>
#include <QTimerEvent>
#include "cresolvingtask.h"

/*
* TCP业务处理类
*/
class CTcpService : public ICommService
{
    Q_OBJECT
public:
    explicit CTcpService(QObject *parent = nullptr);
    ~CTcpService();
    
    // 实现接口函数
    bool CommInit(const QString &serverIP, const QString &serverPort, const QString &config3,
                 const QString &config4, const QString &config5, const QString &config6, 
                 CProtoParamData *parm) override;
    void commWriteData() override;
    void commClose() override;
    bool commWrite(QByteArray arr) override;
    void editWrite(QByteArray arr) override;
    bool isWorking() override;
    QByteArray getCurrentBuff() override;
    void clearBuff() override;
    void startTimeWork() override;
    void timerEvent(QTimerEvent *event) override;
    bool startWork() override;
    void stopWork() override;
    void setUpdateMode(bool mode = true) override;
    QByteArray doParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev) override;
    
    // TCP特有的函数
    bool startTcpServer();
    void stopTcpServer();
    QStringList getConnectedClients();

private:
    QString m_sServerIP;
    QString m_sServerPort;
    QPointer<QThread> m_workthread;
    QPointer<QTcpServer> m_tcpServer;
    QPointer<QTcpSocket> m_currentClient;
    QList<QTcpSocket*> m_clientList;
    bool m_IsOpen;
    QByteArray m_TcpBuff;
    int m_checkSize;
    QPointer<CResolvingTask> m_resolvTask;
    bool m_bIsWorking;
    CProtoParamData *m_pparm;
    int m_timeStatus;
    int m_icatRound;
    int m_iTimeOut;
    int m_iIntvTimes;
    bool m_bIsUpdating;
    int m_TimerId;
    QByteArray m_bEnd;

public:
    static bool m_isReadSD;

public slots:
    void slotCommOpenOrClose(const QString config, const int optype, const QString fileprefix) override;
    void commReadData(void) override;
    void slotCommWrite(const char * arr, int &len) override;
    void slotStartUpdate(QString filename) override;
    
    // TCP特有的槽函数
    void slotNewConnection();
    void slotClientDisconnected();
    void slotTcpError(QAbstractSocket::SocketError error);

signals:
    void sigTcpError(QString error);
    void sigClientConnected(QString clientInfo);
    void sigClientDisconnected(QString clientInfo);
};

#endif // CTCPSERVICE_H
