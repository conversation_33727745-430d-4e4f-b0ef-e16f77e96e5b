﻿#include "cprotoparamdialog.h"
#include "ui_cprotoparamdialog.h"
#include <QDebug>
#include "cconfigmanager.h"

CProtoParamDialog::CProtoParamDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CProtoParamDialog)
{
    ui->setupUi(this);
    m_pParm = new CProtoParamData;
    QHBoxLayout *layout = new QHBoxLayout;
    m_scrollArea = new QScrollArea;
    layout->addWidget(m_scrollArea);
    ui->mapconfig_page->setLayout(layout);
    //m_scrollArea->resize(ui->mapconfig_page->width(), ui->mapconfig_page->height());
    m_mconfWid = new CMapConfigWidget(m_scrollArea);
    //m_mconfWid->resize(ui->mapconfig_page->width(), m_mconfWid->height());
    m_scrollArea->setWidget(m_mconfWid);
}

CProtoParamDialog::~CProtoParamDialog()
{
    delete ui;
}

void CProtoParamDialog::on_bt_saveparm_clicked()
{
    FileParmSt *filest = CProtoParamData::getFileParmSt();
    QMap<QString, QString> &timemap = ui->timer_page->getTimerParams();
    m_pParm->initParam(timemap);
    QMap<QString, QString> &devimap = ui->device_page->getDeviceParams();
    m_pParm->initParam(devimap);
    m_pParm->informParamSig();
    QVector<int> vi = ui->filefield_page->selectedItemsIndex();
    QMap<QString, QString> &divisor = ui->filefield_page->getDivisorMap();
    qDebug()<<"selected:"<<vi;
    CProtoParamData::setProtocolField(vi.at(0), vi.mid(1, vi.size()));
    CProtoParamData::setProtocolDivisor(vi.at(0), divisor);
    filest->isNeedDat = ui->filefield_page->getisNeedDat();
    filest->datType = ui->filefield_page->getDatType();
    filest->isNeedCsv = true;
    if(!ui->filefield_page->getFileDir().isEmpty()){
        filest->savedir = ui->filefield_page->getFileDir();
    }
    m_mconfWid->getMapParms(CProtoParamData::getMapParmSt());
    G_CONFIG.saveConfig();
    close();
}

void CProtoParamDialog::on_bt_cancelparm_clicked()
{
    close();
}

void CProtoParamDialog::on_bt_deviceconf_clicked()
{
    ui->statck_config->setCurrentIndex(0);
}

void CProtoParamDialog::on_bt_timerconf_clicked()
{
    ui->statck_config->setCurrentIndex(1);
}

void CProtoParamDialog::on_bt_ffieldconf_clicked()
{
    ui->statck_config->setCurrentIndex(2);
    FileParmSt *filest = CProtoParamData::getFileParmSt();
    ui->filefield_page->setisNeedDat(filest->isNeedDat);
    ui->filefield_page->setFileDir(filest->savedir);

}

void CProtoParamDialog::showEvent(QShowEvent *){
    //qDebug()<<m_mconfWid->size()<<ui->mapconfig_page->size();
    //m_mconfWid->setFixedWidth(m_scrollArea->widget()->x());
    //qDebug()<<m_mconfWid->size()<<ui->mapconfig_page->size();
}

void CProtoParamDialog::on_bt_mapconfig_clicked()
{
     ui->statck_config->setCurrentIndex(3);
}
