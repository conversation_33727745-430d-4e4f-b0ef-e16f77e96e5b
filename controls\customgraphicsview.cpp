﻿#include "customgraphicsview.h"
#include <QtMath>
#include <QDebug>

CustomGraphicsView::CustomGraphicsView(QWidget *parent) : QGraphicsView(parent)
{
    borderOutColorStart = QColor(255, 255, 255);
    borderOutColorEnd = QColor(166, 166, 166);

    borderInColorStart = QColor(166, 166, 166);
    borderInColorEnd = QColor(255, 255, 255);

    //QGraphicsScene *scene_top = new QGraphicsScene;
    //QPixmap pixmap_top(":/img/bsj911top01.png");
    //pixmapItem_top = new QGraphicsPixmapItem(pixmap_top);
    //pixmapItem_top->setTransformOriginPoint(pixmap_top.width() / 2, pixmap_top.height() / 2);
    //pixmapItem_top->setTransformationMode(Qt::SmoothTransformation);
    //scene_top->addItem(pixmapItem_top);
    //
    //setScene(scene_top);

    m_spdegRotate = 0;


}

void CustomGraphicsView::drawBackground(QPainter *painter, const QRectF &rect) {
    QGraphicsView::drawBackground(painter, rect);
    //painter->scale(1, 1);
    painter->setRenderHint(QPainter::Antialiasing); // 开启抗锯齿
    //qDebug()<<"width:"<<width()<<height()<<sceneRect();
    int woffset = (sceneRect().width() - width()) / 2 + sceneRect().x();
    int hoffset = (sceneRect().height() - height()) / 2 + sceneRect().y();
    //painter->drawLine(woffset,hoffset, width() + woffset, height() + hoffset);
    //painter->drawLine(width() + woffset, hoffset , woffset, height() + hoffset);
    int radius = qMin(width()/2, height()/2) + 40;

    painter->translate(width()/2 + woffset,height()/2 + hoffset);
    painter->scale(0.7, 0.7);

    int roundrate = qRound(m_spdegRotate);

    drawBorderOut(painter, radius, m_startsite - roundrate);
    DrawSmallScale(*painter,radius, 41, m_startsite + 120 - roundrate, 1.5);//刻度线
    QStringList sldata;
    //for (int i = 0; i < 24; i++) {
    //    if(m_spdegRotate > i * 15){
    //        if(i)
    //        sldata.append(QString::number(i * 15));
    //    }
    //}
    sldata.append(QString::number(roundrate - 30));
    sldata.append(QString::number(roundrate - 15));
    sldata.append(QString::number(roundrate));
    sldata.append(QString::number(roundrate + 15));
    sldata.append(QString::number(roundrate + 30));
    DrawDigital(*painter,radius * 0.95, 5, m_startsite + 120 - roundrate, sldata, 15);//刻度数字
    //DrawPointer(*painter,radius * 0.9, m_spdegRotate, m_startsite + 360);//指针

}

QString CustomGraphicsView::genAltitudeDigital(int i){
    if(i < 11){
        return QString::number(i);
    }else{
        return QString::number(i - 20);
    }
}

void CustomGraphicsView::drawBorderOut(QPainter *painter, int radius, int offset )
{
    painter->save();
    QPen pen;
    pen.setWidth(3);
    //pen.setColor(QColor(31,26,190));
    pen.setColor(Qt::white);
    painter->setPen(pen);
    QRectF rectangle(- radius - 4, - radius - 4, radius * 2 + 8, radius * 2 + 8); // 椭圆的矩形区域
    //int startAngle = 0 * 16; // 起始角度（以1/16度为单位）
    //int spanAngle = 360 * 16; // 弧线跨越的角度（以1/16度为单位）
    int startAngle = (offset + 60) * 16; // 起始角度（以1/16度为单位）
    int spanAngle = 60 * 16; // 弧线跨越的角度（以1/16度为单位）
    painter->drawArc(rectangle, startAngle, spanAngle); // 绘制弧线
    //painter->drawRect(rectangle);
    painter->restore();
}

void CustomGraphicsView::DrawDigital(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle)
{
    //设置画笔，画笔默认NOPEN
    QFont font;
    painter.setPen(Qt::red);
    font.setFamily("Arial");
    font.setPointSize(16);
    font.setBold(true);

    if(sldata.size() < pointnum){
        return;
    }

    for(int i=0;i<pointnum;++i){
        QPointF point(0,0);
        painter.save();
        if(i == 2){
            painter.setPen(QPen(QColor(255, 213, 13)));
        }else{
            painter.setPen(QPen(QColor(240, 10, 123)));
        }
        painter.setFont(font);
        QFontMetricsF fm = QFontMetricsF(painter.font());
        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
        painter.translate(point.x(),-point.y());
        painter.rotate((90 - startsite) + i*angle);
        painter.drawText(-25, 0, 50, 20,Qt::AlignCenter,sldata.at(i));
        painter.restore();
    }
    //还原画笔
    painter.setPen(Qt::NoPen);
}

void CustomGraphicsView::DrawSmallScale(QPainter& painter,int radius, int pointnum, int startsite , float angle)
{
    //组装点的路径图
    QPainterPath pointPath_small;
    pointPath_small.moveTo(-2,-2);
    pointPath_small.lineTo(2,-2);
    pointPath_small.lineTo(2,4);
    pointPath_small.lineTo(-2,4);


    QPainterPath pointPath_big;
    pointPath_big.moveTo(-2,-2);
    pointPath_big.lineTo(2,-2);
    pointPath_big.lineTo(2,10);
    pointPath_big.lineTo(-2,10);

    QPen pen;
    pen.setWidth(1);
    pen.setColor(Qt::black);
    painter.setPen(pen);

    //绘制刻度点
    for(int i=0;i<pointnum;i+=2){
        QPointF point(0,0);
        painter.save();
        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
        painter.translate(point.x(),-point.y());
        //qDebug()<<"translate:"<<point;
        painter.rotate( (90 - startsite) + i*angle);

        painter.setBrush(Qt::white);

        if(i%5 == 0)
        {
            if(i == 20){
                painter.setBrush(Qt::green);
            }
            painter.drawPath(pointPath_big);//绘画大刻度

        }else
        {
            painter.drawPath(pointPath_small);//绘画小刻度
        }
        painter.restore();
    }
}

//绘制指针
void CustomGraphicsView::DrawPointer(QPainter& painter,int radius, float degRotate, float startsite)
{
    //组装点的路径图
    QPainterPath pointPath;
    pointPath.moveTo(10,0);
    pointPath.lineTo(1,-radius);
    pointPath.lineTo(-1,-radius);
    pointPath.lineTo(-10,0);
    pointPath.arcTo(-10,0,20,20,180,180);
    QPainterPath inRing;
    inRing.addEllipse(-5,-5,10,10);
    painter.save();

    //计算并选择绘图对象坐标
    qDebug()<<"degRotate - startsite:"<<degRotate<<startsite;
    painter.rotate(degRotate - startsite);
    painter.setBrush(QColor(Qt::red));
    painter.drawPath(pointPath.subtracted(inRing));
    painter.restore();

}

