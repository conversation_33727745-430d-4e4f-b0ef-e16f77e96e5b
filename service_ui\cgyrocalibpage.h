﻿#ifndef CGYROCALIBPAGE_H
#define CGYROCALIBPAGE_H

#include <QWidget>
#include <QItemDelegate>
#include <QLineEdit>
#include <QTableWidgetItem>
#include <QListWidgetItem>
#include <QStandardItem>
#include <QtConcurrent>
#include <QFuture>
#include <QFutureWatcher>

#define LIST_ITEM_SIZE 10
#define LIST_STABLE_SIZE 36000
#define MAX_TURN_SPEED 500    //转台最大转速
#define THRESHOLD_BUFF_LEN 5  //阈值或者分辨率BUFF长度

/*
*  陀螺标定页面
*/
class CListItemDelegate:public QItemDelegate{
public:
    QWidget * createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    void setEditorData(QWidget *editor, const QModelIndex &index) const override;
    void setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const override;
    void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
};

namespace Ui {
class CGyroCalibPage;
}

class CGyroCalibPage : public QWidget
{
    Q_OBJECT

public:
    typedef struct _GyrocalData_{
        QString saxi;
        QVector<double> vspeed;
        QVector<double> vpostive;
        QVector<double> vpastive;
    }GyrocalData;

public:
    explicit CGyroCalibPage(QWidget *parent = nullptr);
    ~CGyroCalibPage();
    void initTableWidget();
    double calculateZeroBias(const double dval,const double calfactor);
    void cZeroBiasStable();
    void calGyrocalifactor();
    void showCaliAvgData();
    void calGyroLimited();
    void calGyroResolustion();
    void calGyroFactorRepeat();
    void calGyrocalibr();
    void calAccelCalibr();
    void calBiasStab();
    void calBiasRepeat();
    void calBias();

private slots:

    void on_cb_calgoal_currentIndexChanged(int index);

    void on_bt_openfile_clicked();

    void on_bt_sartcal_clicked();
    void on_bt_reset_clicked();

    void on_cb_unit_currentIndexChanged(int index);

    void on_le_calfactorparam_textChanged(const QString &arg1);

public slots:

    void slotDataShow(const QStringList datakeys, const QVector<double> dataValues, const QString sportN, const int protoindex);
    void slotReadFileEnd();
    void slotDestory();

private:
    Ui::CGyroCalibPage *ui;
    int m_iCurrentIndex;
    QVector<QTableWidgetItem *> m_vTableItems;
    QVector<QListWidgetItem *> m_vListItems;
    QVector<QStandardItem *> m_vStandItems;
    double m_dRtdatas[LIST_ITEM_SIZE];
    double m_dStdatas[LIST_STABLE_SIZE * 2];
    int m_iStCurrentPos;
    QList<double> *m_vdData;
    QList<QString> *m_vsSpeed;
    QList<QString> *m_vsFilename;
    QVector<double> m_vgyrocalifactor;
    QVector<double> m_vbiasstab;
    QList<GyrocalData *> m_lGyroData;
    double m_dGyroFactor;
    QVector<QList<double> *> m_vdlists;
    bool m_bIsRealEnd;
    int m_timerId;
    QVector<double> m_vbisass;
    QFutureWatcher<double> *m_watcher;
    QFuture<double> m_future;
    double m_allDataVal = 0.0;   //存储所有数据用于计算平均值
    qint64 m_iallSize = 0;   //所有记录数

};

#endif // CGYROCALIBPAGE_H
