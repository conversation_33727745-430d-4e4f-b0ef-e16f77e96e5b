﻿#include "tlhv1window.h"
#include "ui_tlhv1window.h"
#include <QDebug>
#include <QIcon>
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cfirmupgpage.h"
#include "csubpagemanager.h"
#include "crevoctrpage.h"
#include "cdataanapage.h"
#include "cgyrocalibpage.h"
#include "cequipmentpage.h"
#include "cfirmupgpage.h"
#include "ccustomerviewpage.h"
#include <QMessageBox>
#include "cconfigmanager.h"
#include "clightlabel.h"
#include "cinputdialog.h"

TlhV1Window::TlhV1Window(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::TlhV1Window)
{
    ui->setupUi(this);
    qDebug()<<"TlhV1Window:"<<QThread::currentThreadId();
    QWidget *tab_xyDisplay = new QWidget;
    QWidget *tab_yzDisplay = new QWidget;
    QWidget *tab_3dDisplay = new QWidget;
    CRevoCtrPage *tab_RevoCtr = new CRevoCtrPage;
    CDriTestPage *tab_DriTest = new CDriTestPage;
    CDriveTestPage *tab_DriveTest = new CDriveTestPage;
    QWidget *tab_Magnet = new QWidget;
    CDataAnaPage *tab_DataAna = new CDataAnaPage;
    //QWidget *tab_AlgDebug = new QWidget;
    CFirmUpgPage *tab_FirmUpg = new CFirmUpgPage;
    CGyroCalibPage *tab_GyroCalib = new CGyroCalibPage;
    CEquipmentPage *tab_Equipment = new CEquipmentPage;
    CCustomerViewPage *tab_CustomerView = new CCustomerViewPage;
    m_seriPage = new CcserialPage();
    m_commPage = new CCommPage();
    tab_xyDisplay->setObjectName("tab_xyDisplay");
    tab_yzDisplay->setObjectName("tab_yzDisplay");
    tab_3dDisplay->setObjectName("tab_3dDisplay");
    tab_RevoCtr->setObjectName("tab_RevoCtr");
    tab_DriTest->setObjectName("tab_DriTest");
    tab_DriveTest->setObjectName("tab_DriveTest");
    tab_Magnet->setObjectName("tab_Magnet");
    tab_DataAna->setObjectName("tab_DataAna");
    //tab_AlgDebug->setObjectName("tab_AlgDebug");
    tab_FirmUpg->setObjectName("tab_FirmUpg");
    tab_GyroCalib->setObjectName("tab_GyroCalib");
    tab_Equipment->setObjectName("tab_Equipment");
    tab_CustomerView->setObjectName("tab_CustomerView");

    ui->tab_OriData->setObjectName("tab_OriData");

    ui->tabWidget->setTabShape(QTabWidget::Rounded);
    ui->tabWidget->setWindowFlags(Qt::FramelessWindowHint);

    //ui->tabWidget->addTab(tab_xyDisplay,"X-Y");
    //ui->tabWidget->addTab(tab_yzDisplay,"Y-Z");
    //ui->tabWidget->addTab(tab_3dDisplay,"3 D");
    ui->tabWidget->addTab(tab_CustomerView,tr("UserView"));
    ui->tabWidget->addTab(tab_DriTest,tr("SingleDebug"));
    ui->tabWidget->addTab(tab_DriveTest,tr("MultiDebug"));
    //ui->tabWidget->addTab(tab_Magnet,tr(u8"磁力计"));
    ui->tabWidget->addTab(tab_DataAna,tr("DataView"));
    //ui->tabWidget->addTab(tab_AlgDebug,tr(u8"算法调试"));
    ui->tabWidget->addTab(tab_FirmUpg,tr("Firmparam"));
    m_worker = G_CONFIG.getValue("System.Worker").toString();
    if(m_worker == "DEV"){
        ui->tabWidget->addTab(tab_RevoCtr,tr(u8"转台控制"));
        ui->tabWidget->addTab(tab_GyroCalib,tr(u8"陀螺标定"));
    }else{
        ui->ac_calimode->menuAction()->setVisible(false);
    }

    m_lang = G_CONFIG.getValue("System.LANG").toString();
    //ui->tabWidget->addTab(tab_Equipment,tr(u8"设备调试"));

    ui->tabWidget->removeTab(ui->tabWidget->indexOf(ui->tab_OriData));

    CSubPageManager::GetInstance()->RegisterPage("tab_xyDisplay", tab_xyDisplay);
    CSubPageManager::GetInstance()->RegisterPage("tab_yzDisplay", tab_yzDisplay);
    CSubPageManager::GetInstance()->RegisterPage("tab_3dDisplay", tab_3dDisplay);
    CSubPageManager::GetInstance()->RegisterPage("tab_RevoCtr", tab_RevoCtr);
    CSubPageManager::GetInstance()->RegisterPage("tab_DriTest", tab_DriTest);
    CSubPageManager::GetInstance()->RegisterPage("tab_DriveTest", tab_DriveTest);
    CSubPageManager::GetInstance()->RegisterPage("tab_Magnet", tab_Magnet);
    CSubPageManager::GetInstance()->RegisterPage("tab_DataAna", tab_DataAna);
    //CSubPageManager::GetInstance()->RegisterPage("tab_AlgDebug", tab_AlgDebug);
    CSubPageManager::GetInstance()->RegisterPage("tab_FirmUpg", tab_FirmUpg);
    CSubPageManager::GetInstance()->RegisterPage("tab_GyroCalib", tab_GyroCalib);
    CSubPageManager::GetInstance()->RegisterPage("tab_Equipment", tab_Equipment);
    CSubPageManager::GetInstance()->RegisterPage("tab_CustomerView", tab_CustomerView);

    QString errinfo;
    if(!G_CONFIG.loadConfig(errinfo)){
        QMessageBox::critical(this, tr(u8"加载配置文件失败"), errinfo);
        return;
    }

    QFile file(":/qss/window_style.qss");
    if(!file.open(QIODevice::ReadOnly)){
        qDebug()<<"open style file fail:"<<file.errorString();
        return;
    }
    QByteArray style_arr = file.readAll();
    setStyleSheet(style_arr);

    qDebug()<<"objectName:"<<ui->tab_OriData->objectName();

    m_seriPage->setStyleSheet(style_arr);

    tab_RevoCtr->setSeriPage(m_seriPage);

    tab_FirmUpg->setSeriPage(m_seriPage);

    m_paramDlg = new CProtoParamDialog(this);

    m_seriPage->setProtoParam(m_paramDlg->getProtoParam());
    m_commPage->setProtoParam(m_paramDlg->getProtoParam());

    QStringList comlist = m_seriPage->getAvilaCom();
    ui->cb_mPortN->addItems(comlist);
    tab_DriTest->setSeriComs(comlist);
    tab_Equipment->setSeriComs(comlist);

    //创建采集记录窗口
    m_recordDlg = new CCollectRecordDialog(this);

    connect(ui->cb_mPortN, QOverload<const QString &>::of(&QComboBox::currentTextChanged), m_seriPage, &CcserialPage::slotSetPortN);
    connect(ui->cb_mBaudR, QOverload<const QString &>::of(&QComboBox::currentTextChanged), m_seriPage, &CcserialPage::slotSetBaudR);
    connect(ui->cb_mPortN, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &TlhV1Window::slotPortChage);
    connect(m_seriPage, &CcserialPage::sigPortDataChange, this, [=](const QStringList &comlist){
        QString currentport = ui->cb_mPortN->currentText();
        ui->cb_mPortN->hidePopup();
        ui->cb_mPortN->clear();
        ui->cb_mPortN->addItems(comlist);
        ui->cb_mPortN->setCurrentText(currentport);
        tab_DriTest->setSeriComs(comlist);
    });

    connect(m_seriPage, &CcserialPage::sigUpgMUartP, this, &TlhV1Window::slotSetUartP);
    connect(m_commPage, &CCommPage::sigUpgMCommP, this, &TlhV1Window::slotSetUartP);
    connect(tab_CustomerView, &CCustomerViewPage::sigShowSelf, this, [=]{
        ui->tabWidget->setCurrentWidget(tab_CustomerView);
        tab_CustomerView->on_bt_showmap_clicked();

    });

    ui->tabWidget->tabBar()->setVisible(false);

    connect(ui->tabWidget, &QTabWidget::currentChanged, this, [=](int index){
        if(index == 0){
            ui->tabWidget->tabBar()->setVisible(false);
        }
    });

    QPixmap pixmap=QPixmap(":/img/appbackground.jpeg").scaled(this->size());
    QPalette palette;
    //设置主窗口背景图片
    palette.setBrush(QPalette::Window,QBrush(pixmap));
    //设置主窗口背景颜色
    //palette.setColor(QPalette::Window,QColor(255, 150, 30));
    this->setPalette(palette);

    qDebug()<<"configtest"<<G_CONFIG.getValue("SeriCache.Com").value<QString>();
    G_CONFIG.setValue("Default-Seri.Com", "COM3");

    m_abDlog = NULL;

    //ui->bt_mSeriOpen->setText(tr(u8"打开"));

    setWindowTitle(tr("I-NAV Platform") + VERSION);
}


void TlhV1Window::slotPortChage(const QString &tport){
    qDebug()<<"current change";
    ui->cb_mPortN->setCurrentText(tport);
    //此通讯已经处于工作状态，开启按钮变为"关闭", 波特率无法修改
    bool isWorking = false;
    if(m_seriPage && m_seriPage->getStatusByPortN(tport)){
        isWorking = true;
    }
    if(m_commPage && m_commPage->getStatusByConfig(tport)){
        isWorking = true;
    }

    if(isWorking){
        ui->bt_mSeriOpen->setText(tr("Close"));
        ui->cb_mBaudR->setDisabled(true);
    }else{
        ui->bt_mSeriOpen->setText(tr("Open"));
        ui->cb_mBaudR->setDisabled(false);
    }

    if(m_seriPage){
        m_seriPage->setSeripageStatus();
    }
    if(m_commPage){
        m_commPage->setCommPageStatus();
    }
    //ui->bt_mSeriOpen->setDisabled(false);
}

void TlhV1Window::slotSetUartP(const QString &tport, const QString tbaud, const bool opstat){
    ui->cb_mPortN->setCurrentText(tport);
    ui->cb_mBaudR->setCurrentText(tbaud);
    //此通讯已经处于工作状态，开启按钮变为"关闭", 波特率无法修改
    bool isWorking = false;
    if(m_seriPage && m_seriPage->getStatusByPortN(tport)){
        isWorking = true;
    }
    if(m_commPage && m_commPage->getStatusByConfig(tport)){
        isWorking = true;
    }

    qDebug()<<"getStatusByConfig"<<isWorking<<opstat;
    if(isWorking){
        ui->bt_mSeriOpen->setText(tr("Close"));
        ui->cb_mBaudR->setDisabled(true);
    }else{
        ui->bt_mSeriOpen->setText(tr("Open"));
        ui->cb_mBaudR->setDisabled(false);
    }

    ui->bt_mSeriOpen->setDisabled(opstat);
    //ui->bt_mSeriOpen->setDisabled(false);
}


void TlhV1Window::on_bt_mSeriOpen_clicked()
{
    if(m_commPage == NULL){
            m_commPage = new CCommPage();
            m_commPage->setProtoParam(m_paramDlg->getProtoParam());
    }

    ui->bt_mSeriOpen->setDisabled(true);
    if(ui->bt_mSeriOpen->text() == tr("Open")){
        m_commPage->openOrCloseCommPort();
        QString currentConfig = ui->cb_mPortN->currentText(); // 默认使用串口配置
        if(!m_commPage->getStatusByConfig(currentConfig)){
            QMessageBox::critical(this, tr("Error"), tr("Communication opening failed, please check if the configuration is correct!"));
            ui->bt_mSeriOpen->setDisabled(false);
            return;
        }
        ui->bt_mSeriOpen->setText(tr("Close"));
    }else{

        m_commPage->openOrCloseCommPort();
        ui->bt_mSeriOpen->setText(tr("Open"));

    }
    ui->bt_mSeriOpen->setDisabled(false);
}


TlhV1Window::~TlhV1Window()
{
    delete ui;
}


void TlhV1Window::on_toolButton_clicked()
{
    if(ui->widget_2->isVisible()){
        ui->widget_2->setVisible(false);
        ui->toolButton->setIcon(QIcon(":/img/toleft1.ico"));
    }else{
        ui->widget_2->setVisible(true);
        ui->toolButton->setIcon(QIcon(":/img/toright1.ico"));
    }
}

void TlhV1Window::on_bt_mParamset_clicked()
{
    m_paramDlg->show();
}

void TlhV1Window::on_bt_mSerialSet_clicked()
{
    if(m_commPage == NULL){
         m_commPage = new CCommPage();
         m_commPage->setProtoParam(m_paramDlg->getProtoParam());
    }
    m_commPage->show();

}

void TlhV1Window::on_bt_collectrecord_clicked()
{
    m_recordDlg->show();
}

void TlhV1Window::slotShowStatus(int showtype, QStringList key, QStringList val){

}

void TlhV1Window::showEvent(QShowEvent *e){
    //ui->tabWidget->tabBar()->setVisible(false);
}

void TlhV1Window::on_action_1_triggered()
{
    ui->tabWidget->tabBar()->setVisible(true);
    ui->tabWidget->setCurrentIndex(4);
}

void TlhV1Window::on_action_2_triggered()
{
    ui->tabWidget->tabBar()->setVisible(true);
    ui->tabWidget->setCurrentIndex(3);
}

void TlhV1Window::on_action_5_triggered()
{
    ui->tabWidget->tabBar()->setVisible(true);
    ui->tabWidget->setCurrentIndex(1);
}

void TlhV1Window::on_about_triggered()
{
    if(m_abDlog == NULL){
        m_abDlog = new CAboutDlg(this);
    }
    m_abDlog->show();
}

void TlhV1Window::on_action_7_triggered()
{
    ui->tabWidget->tabBar()->setVisible(true);
    ui->tabWidget->setCurrentIndex(6);
}

void TlhV1Window::on_action_6_triggered()
{
    ui->tabWidget->tabBar()->setVisible(true);
    ui->tabWidget->setCurrentIndex(5);
}

void TlhV1Window::on_ac_viewmode_triggered()
{
    ui->tabWidget->tabBar()->setVisible(false);
    ui->tabWidget->setCurrentIndex(0);
}

void TlhV1Window::on_action_8_triggered()
{
    if(m_lang == "EN"){
        return;
    }
    //ui->ac_calimode->menuAction()->setVisible(false);
    G_CONFIG.setValue("System.LANG", "EN");
    restartApplication();
}

void TlhV1Window::on_action_triggered()
{
    if(m_lang == "CN"){
        return;
    }
    //ui->ac_calimode->menuAction()->setVisible(true);
    G_CONFIG.setValue("System.LANG", "CN");
    restartApplication();
}

void TlhV1Window::restartApplication()
{
    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(nullptr, tr("Info"), tr("The program is about to restart!"),
                                   QMessageBox::Yes | QMessageBox::No);
    // 根据用户的选择判断反馈状态
    if (reply != QMessageBox::Yes)
    {
        QMessageBox::information(this, tr("Info"), tr("The program will switch languages on the next startup!"));
        return;
    }

    // 获取当前应用程序的路径
    QString program = QCoreApplication::applicationFilePath();
    // 获取当前应用程序的命令行参数
    QStringList arguments = QCoreApplication::arguments();
    // 使用QProcess启动自身程序
    QProcess::startDetached(program, arguments);
    // 退出当前应用程序，英文切中文由于英文模式下地图地址不可用，会导致程序无法立即退出
    QCoreApplication::quit(); // 退出程序
}


void TlhV1Window::on_action_3_triggered()
{
    CInputDialog inputdlg;
    if(inputdlg.exec()){
        ui->ac_calimode->menuAction()->setVisible(true);
        ui->tabWidget->addTab(CSubPageManager::GetInstance()->GetObjectbyName("tab_RevoCtr"), tr(u8"转台控制"));
        ui->tabWidget->addTab(CSubPageManager::GetInstance()->GetObjectbyName("tab_GyroCalib"), tr(u8"陀螺标定"));
    }
}

void TlhV1Window::on_actionFile_Tools_triggered()
{
    QFile file("AfiletoGyroData.exe");
    if(!file.exists()){
        QMessageBox::critical(this, tr("Error"), tr("Can not find exe file!"));
        return;
    }
    QString programPath = "AfiletoGyroData.exe";
    QProcess::startDetached(programPath);
}

void TlhV1Window::on_actionMessage_Tools_triggered()
{
    QFile file("MyTools.exe");
    if(!file.exists()){
        QMessageBox::critical(this, tr("Error"), tr("Can not find exe file!"));
        return;
    }
    QString programPath = "MyTools.exe";
    QProcess::startDetached(programPath);
}

void TlhV1Window::on_actionCalibration_Tool_triggered()
{
    QFile file("AlgorithmTool.exe");
    if(!file.exists()){
        QMessageBox::critical(this, tr("Error"), tr("Can not find exe file!"));
        return;
    }
    QString workdir = QDir::currentPath();
    QString programPath = "AlgorithmTool.exe " + workdir;
    QProcess::startDetached(programPath);
}
