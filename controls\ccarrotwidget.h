﻿#ifndef CCARROTWIDGET_H
#define CCARROTWIDGET_H

#include <QWidget>
#include <QGraphicsPixmapItem>

namespace Ui {
class CCarRotWidget;
}

/*
* 仪表视图车体姿态整体控件
*/
class CCarRotWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CCarRotWidget(QWidget *parent = nullptr);
    ~CCarRotWidget();

private:
    Ui::CCarRotWidget *ui;

    QGraphicsPixmapItem *pixmapItem_top;
    QGraphicsPixmapItem *pixmapItem_side;
    QGraphicsPixmapItem *pixmapItem_back;
    qreal rotationAngle;
};

#endif // CCARROTWIDGET_H
