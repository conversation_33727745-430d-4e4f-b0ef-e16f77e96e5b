﻿#include "cgyrocalibpage.h"
#include "ui_cgyrocalibpage.h"
#include <QStandardItemModel>
#include <QDir>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>
#include <QPalette>
#include "Cali.h"
#include "cloadfiletaskdlg.h"

QWidget *CListItemDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    QLineEdit *editor=new QLineEdit(parent);
    editor->setFrame(false);
    return editor;
}

void CListItemDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    QLineEdit *edit = static_cast<QLineEdit*>(editor);
    edit->setText(index.data(Qt::DisplayRole).toString());
}

void CListItemDelegate::setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const
{
    QLineEdit *edit = static_cast<QLineEdit*>(editor);
    model->setData(index,edit->text(),Qt::DisplayRole);
}
//option更新由 index 指定的项目的编辑器
void CListItemDelegate::updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    QRect rect =  option.rect;
    editor->setGeometry(rect);
}


CGyroCalibPage::CGyroCalibPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CGyroCalibPage)
{

    ui->setupUi(this);
    initTableWidget();
    m_iCurrentIndex = 0;
    m_iStCurrentPos = 0;
    ui->bt_sartcal->setDisabled(true);
    m_bIsRealEnd = false;
    m_timerId = -1;
    m_vdData = NULL;
}

CGyroCalibPage::~CGyroCalibPage()
{
    delete ui;
}

void CGyroCalibPage::initTableWidget()
{
    ui->tableWidget->verticalHeader()->setDefaultSectionSize(25);
    //设置行高
    ui->tableWidget->setRowCount(30);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->horizontalHeader()->setStretchLastSection(true);

    ui->tableWidget->setColumnCount(3);
    QStringList headText;
    headText << QString::fromLocal8Bit("序号")
             << QString::fromLocal8Bit("文件名")
             << QString::fromLocal8Bit("文件大小");

    ui->tableWidget->setHorizontalHeaderLabels(headText);

    ui->tableWidget->setColumnWidth(1, 240);

    QStandardItemModel *model = new QStandardItemModel(ui->lv_repeatfactor);
    CListItemDelegate *citemdg = new CListItemDelegate;
    ui->lv_repeatfactor->setItemDelegate(citemdg);
    ui->lv_repeatfactor->setSpacing(0);
    ui->lv_repeatfactor->setStyleSheet("border: 0px");

    // 向模型添加数据项
    for (int i = 0; i < 20; ++i) {
        QStandardItem *item = new QStandardItem(QString(""""));
        m_vStandItems.append(item);
        item->setFont(QFont("新宋体", 14));
        item->setSizeHint(QSize(20, 23));
        model->appendRow(item);
    }
    // 将模型设置给QListView
    ui->lv_repeatfactor->setModel(model);

}

void CGyroCalibPage::showCaliAvgData(){
    if(m_iCurrentIndex == 3){
        return;
    }
    for(QTableWidgetItem *twi:m_vTableItems){
        delete twi;
    }
    m_vTableItems.clear();

    ui->tableWidget->setColumnCount(5);
    QStringList headText;
    headText << QString::fromLocal8Bit("序号") << QString::fromLocal8Bit("轴") \
             << QString::fromLocal8Bit("转速") << QString::fromLocal8Bit("均值") \
             << QString::fromLocal8Bit("文件名");

    ui->tableWidget->setHorizontalHeaderLabels(headText);

    ui->tableWidget->setColumnWidth(1, 40);

    for (int i = 0; i < m_vsSpeed->size(); i++) {
        ui->tableWidget->setRowHeight(i, 24);

        QTableWidgetItem *itemNum = new QTableWidgetItem(QString::number(i + 1));
        QTableWidgetItem *itemAixName = new QTableWidgetItem("x");
        QTableWidgetItem *itemTurnSpeed = new QTableWidgetItem(m_vsSpeed->at(i));
        QTableWidgetItem *itemAvg = new QTableWidgetItem(QString::number(m_vdData->at(i), 0, 4));
        QTableWidgetItem *itemFile = new QTableWidgetItem(m_vsFilename->at(i));

        m_vTableItems.push_back(itemNum);
        m_vTableItems.push_back(itemAixName);
        m_vTableItems.push_back(itemTurnSpeed);
        m_vTableItems.push_back(itemAvg);
        m_vTableItems.push_back(itemFile);

        itemNum->setTextAlignment(Qt::AlignCenter);
        itemAixName->setTextAlignment(Qt::AlignCenter);
        itemTurnSpeed->setTextAlignment(Qt::AlignCenter);
        itemAvg->setTextAlignment(Qt::AlignCenter);
        itemFile->setTextAlignment(Qt::AlignCenter);

        ui->tableWidget->setItem(i, 0, itemNum);
        ui->tableWidget->setItem(i, 1, itemAixName);
        ui->tableWidget->setItem(i, 2, itemTurnSpeed);
        ui->tableWidget->setItem(i, 3, itemAvg);
        ui->tableWidget->setItem(i, 4, itemFile);
    }
}

void CGyroCalibPage::on_cb_calgoal_currentIndexChanged(int index)
{
    //标度因数、零偏稳定性
    m_iCurrentIndex = ui->cb_calgoal->currentIndex();
    //qDebug()<<"reapeat:"<<m_vgyrocalifactor<<m_vbiasstab<<index<<m_vStandItems.size();
    if(index == 0 || index == 3 || index == 7 || index == 6 || index == 8){
        ui->lb_repeatfactor->setVisible(true);
        ui->lv_repeatfactor->setVisible(true);
        int items_num = m_vStandItems.size();
        int valus_num = 0;
        int min_num = 0;
        for (QStandardItem *it:m_vStandItems) {
            it->setText("");
        }

        if(index == 0 || index == 6){
            valus_num = m_vgyrocalifactor.size();
            min_num = items_num < valus_num ? items_num : valus_num;
            for(int i = 0; i < min_num; i++){
                m_vStandItems.at(i)->setText(QString::number(m_vgyrocalifactor.at(i), 0, 4));
            }
        }else{
            valus_num = m_vbisass.size();
            min_num = items_num < valus_num ? items_num : valus_num;
            for(int i = 0; i < min_num; i++){
                m_vStandItems.at(i)->setText(QString::number(m_vbisass.at(min_num - i - 1), 0, 8));
            }
        }
        switch (index) {
        case 8:
            m_vbisass.clear();
            break;
        default:
            break;
        }
    }else{
        ui->lb_repeatfactor->setVisible(false);
        ui->lv_repeatfactor->setVisible(false);
    }

    if(index > 5){
        ui->bt_sartcal->setDisabled(false);
    }
}

void CGyroCalibPage::on_bt_openfile_clicked()
{
    QString currentDir = QDir::currentPath();
    QStringList dofilelist;
    QStringList m_slWorkFile = QFileDialog::getOpenFileNames(this, QString::fromLocal8Bit("打开数据文件"), currentDir, tr("Csv files (*.csv)"));
    if(m_slWorkFile.size() == 0){
        return;
    }

    qDebug()<<"m_slWorkFile"<<m_slWorkFile;

    for(int i = 0; i < m_slWorkFile.size(); i++){

        QString filename = m_slWorkFile.at(i);
        QFileInfo finfo(filename);
        //if(filename.isEmpty() || !finfo.fileName().startsWith("w")){
        if(filename.isEmpty() || filename.indexOf('_') == -1){
            QMessageBox::critical(this, QString::fromLocal8Bit("打开文件"), QString::fromLocal8Bit("文件名不合规:") + filename);
            return;
        }
        dofilelist.append(filename);
    }

    for(QTableWidgetItem *twi:m_vTableItems){
        delete twi;
    }

    m_vTableItems.clear();

    ui->tableWidget->setColumnCount(3);
    QStringList headText;
    headText << QString::fromLocal8Bit("序号")
             << QString::fromLocal8Bit("文件名")
             << QString::fromLocal8Bit("文件大小");

    ui->tableWidget->setHorizontalHeaderLabels(headText);

    for (int i = 0;i< dofilelist.size();i++) {
        QFile file(dofilelist.at(i));
        QTableWidgetItem *itemNum = new QTableWidgetItem(QString::number(i + 1));
        QTableWidgetItem *itemFileName = new QTableWidgetItem(dofilelist.at(i));
        QTableWidgetItem *itemFileSize = new QTableWidgetItem(QString::number(file.size()));

        m_vTableItems.push_back(itemNum);
        m_vTableItems.push_back(itemFileName);
        m_vTableItems.push_back(itemFileSize);

        ui->tableWidget->setColumnWidth(1, 240);

        itemNum->setTextAlignment(Qt::AlignCenter);
        itemFileName->setTextAlignment(Qt::AlignCenter);
        itemFileSize->setTextAlignment(Qt::AlignCenter);

        ui->tableWidget->setItem(i, 0, itemNum);
        ui->tableWidget->setItem(i, 1, itemFileName);
        ui->tableWidget->setItem(i, 2, itemFileSize);
    }

    qDebug()<<"dofilelist"<<dofilelist;

    CLoadFileTaskDlg *lfdlg = new CLoadFileTaskDlg(this);
    connect(lfdlg, &CLoadFileTaskDlg::sigReadFileEnd, this, &CGyroCalibPage::slotReadFileEnd);
    m_vsSpeed = new QList<QString>;
    m_vdData = new QList<double>;
    m_vsFilename = new QList<QString>;

    int icurrentpos = ui->cb_calgoal->currentIndex();
    qDebug()<<"cb_calgoal index:"<<icurrentpos;
    QVector<int> fiedlindx;
    //坐标轴 0-x轴 1-y轴 2-z轴
    int iaixs = ui->cb_aixs->currentIndex();

    switch (icurrentpos) {
    case 0:
    case 1:
    case 2:
    case 4:
    case 5:
    case 8:
        lfdlg->loadFileMulDetailAvg(dofilelist, iaixs, m_vsSpeed, m_vdData, m_vsFilename);
        break;
    case 3:
        fiedlindx.clear();
        fiedlindx.append(iaixs);
        m_vdData->clear();
        m_vdlists.clear();
        m_vdlists.append(m_vdData);
        //lfdlg->loadFileDetailAvg(dofilelist.at(0), fiedlindx, ifrq * iavg, m_vdlists);
        //由算法去做平滑
        lfdlg->loadFileDetail(dofilelist.at(0), fiedlindx, m_vdlists);
        break;
    case 6:
        return;
    case 7:
        return;
    default:
        return;
    }
    qDebug()<<"m_vdSpeed222:";
    lfdlg->show();

}

void CGyroCalibPage::slotReadFileEnd(){
    showCaliAvgData();
    ui->bt_sartcal->setDisabled(false);
}

void CGyroCalibPage::calGyrocalifactor(){
    qDebug()<<"slotReadFileEnd:"<<m_vsSpeed->toVector()<<m_vdData->toVector()<<m_vsFilename->toVector();
    //将计算后的均值数据按正负角度归类
    int pos = -1;
    bool isafter = false;
    double cspeed = 0.0;
    double fr = 0.0;
    GyrocalData *gdata = new GyrocalData;
    if(m_vsSpeed->size() != 0){
        for (int i = 0;i < m_vsSpeed->size(); i++) {
            //qDebug()<<"m_vdSpeed"<<m_vsSpeed->at(i)<<m_vsSpeed->at(i).toDouble();
            gdata->saxi = QFileInfo(m_vsFilename->at(i)).fileName().at(1);
            if(m_vsSpeed->at(i).compare("before") == 0){
                isafter = true;
                //gdata->vspeed.append(0.0);
                cspeed = 0.0;
                pos = m_vsSpeed->lastIndexOf("after");
            }else if(m_vsSpeed->at(i).toDouble() > 0){
                isafter = false;
                cspeed = m_vsSpeed->at(i).toDouble();
                gdata->vspeed.append(cspeed);
                pos = m_vsSpeed->indexOf(QString::number(-cspeed));
            }else{
                continue;
            }

            if(pos == -1){
                continue;
            }

            //0角度值不需要参与计算，仅用来计算FR
            if(cspeed == 0.0){
                fr = (m_vdData->at(i) + m_vdData->at(pos)) / 2;
            }else{
                gdata->vpostive.append(m_vdData->at(i));
                gdata->vpastive.append(m_vdData->at(pos));
            }
            //qDebug()<<gdata->vspeed.at(i)<<gdata->vpastive.at(i)<<gdata->vpostive.at(i)<<pos;
        }
        m_lGyroData.append(gdata);
    }
    delete m_vsSpeed;
    m_vsSpeed = nullptr;
    delete m_vdData;
    m_vdData = nullptr;
    delete m_vsFilename;
    m_vsFilename = nullptr;
    //调用标定算法进行计算
    //qDebug()<<"califactor0:"<<gdata->vspeed<<gdata->vspeed.size()<<gdata->vpostive<<gdata->vpastive<<fr;
    //double califactor = ::Gyro_Scale(gdata->vspeed.data(), gdata->vspeed.size(), gdata->vpostive.data(), gdata->vpastive.data(), fr);
    //double dsp[5] = {0.1, 0.5, 1, 5, 0};
    //double dpo[5] = {29897, 149469, 298831, 1.49483e+06, 51};
    //double dpa[5] = {-30009, -149636, -299018, -1.49499e+06, -22};
    double califactor = ::Gyro_Scale(gdata->vspeed.data(), gdata->vspeed.size() , gdata->vpostive.data(), gdata->vpastive.data(), fr);
    //double califactor = ::Gyro_Scale(dsp, 5, dpo, dpa, fr);
    //回显数据到页面
    //qDebug()<<"califactor:"<<califactor;
    m_vgyrocalifactor.append(califactor);

    int item_num = m_vStandItems.size();
    int value_num = m_vgyrocalifactor.size();
    int min_num = value_num < item_num ? value_num :item_num;
    for (int i = 0;i < item_num;i++) {
        m_vStandItems.at(i)->setText("");
    }

    for(int i = 0; i < min_num; i++){
        m_vStandItems.at(i)->setText(QString::number(m_vgyrocalifactor.at(value_num - 1 - i), 'f', 4));
    }


    double califactornlin = ::Gyro_Scale_Nonlin(gdata->vspeed.data(), gdata->vspeed.size(), gdata->vpostive.data(), \
                                                gdata->vpastive.data(), fr, MAX_TURN_SPEED);
    double califactorasym = ::Gyro_Scale_Asym(gdata->vspeed.data(), gdata->vspeed.size(), gdata->vpostive.data(), \
                                              gdata->vpastive.data(), fr);

    m_dGyroFactor = califactor;
    ui->le_calfactor->setText(QString::number(califactor, 'f', 8));
    ui->le_calfactorparam->setText(QString::number(califactor, 'f', 8));
    ui->le_calfactorNoLine ->setText(QString::number(califactornlin, 0, 4) + " ppm");
    ui->le_calfactorNoAsy->setText(QString::number(califactorasym, 0, 4)+ " ppm");

}

void CGyroCalibPage::calGyroLimited(){
    m_dGyroFactor = ui->le_calfactorparam->text().toDouble();
    double angle = 0.0;
    for (int i = 0;i < m_vsSpeed->size();i++) {
        if(angle < m_vsSpeed->at(i).toDouble()){
            angle = m_vsSpeed->at(i).toDouble();
        }
    }

    int ireslen = THRESHOLD_BUFF_LEN;
    double dres[THRESHOLD_BUFF_LEN] = {0.0};
    //qDebug()<<m_vdData->toVector();
    //qDebug()<<m_vsSpeed->toVector();
    ui->le_threshold->setAutoFillBackground(true);
    QPalette palette = ui->le_threshold->palette();
    if(::Cal_Threshold(m_vdData->toVector().data(), m_vdData->size() , m_dGyroFactor, angle, dres, &ireslen) == 1){
        palette.setColor(QPalette::WindowText, Qt::green);
    }else{
        palette.setColor(QPalette::WindowText, Qt::red);
    }
    ui->le_threshold->setPalette(palette);
    qDebug()<<"dres:"<<dres[0]<<dres[1]<<angle;
    //计算结果输出数组，依次为所计算的阈值和判断比率，单位分别是deg/s和%
    ui->le_threshold->setText(QString::number(dres[0] * 3600, 0, 4) + "/" + QString::number(dres[1] * 100, 0, 1));
}


void CGyroCalibPage::calGyroResolustion(){
    m_dGyroFactor = ui->le_calfactorparam->text().toDouble();
    double angle = 0.0;
    for (int i = 0;i < m_vsSpeed->size();i++) {
        if(angle < m_vsSpeed->at(i).toDouble()){
            angle = m_vsSpeed->at(i).toDouble();
        }
    }

    int ireslen = THRESHOLD_BUFF_LEN;
    double dres[THRESHOLD_BUFF_LEN] = {0.0};
    //输入的角度要是相对于45°的偏差，例如43,45,47,实际给算法分别就是-2,0,2
    Cal_Resolution(m_vdData->toVector().data(), m_vdData->size() , m_dGyroFactor, angle - 45, dres, &ireslen);
    //计算结果输出数组，依次为所计算的分辨率和判断比率，单位分别deg/s和%
    ui->le_resolura->setText(QString::number(dres[0] * 3600, 0, 4) + "/" + QString::number(dres[1] * 100, 0, 1));
}


void CGyroCalibPage::on_bt_sartcal_clicked()
{
    m_iCurrentIndex = ui->cb_calgoal->currentIndex();
    //重复性因数计算无需重新加载数据
    if((m_iCurrentIndex != 6 && m_iCurrentIndex != 7) && (m_vdData == nullptr || m_vdData->size() == 0)){
        QMessageBox::critical(this, QString::fromLocal8Bit("操作失败"), QString::fromLocal8Bit("无数据，无法执行对应操作！"));
        return;
    }
    switch (m_iCurrentIndex) {
    case 0:
        //标定因数 标定因数非线性度 标定因数不对称
        calGyrocalifactor();
        break;
    case 1:
        //阈值
        calGyroLimited();
        break;
    case 2:
        //分辨率
        calGyroResolustion();
        break;
    case 3:
        //零偏稳定性
        calBiasStab();
        break;
    case 4:
        //陀螺仪标定
        calGyrocalibr();
        break;
    case 5:
        //加速度计标定
        calAccelCalibr();
        break;
    case 6:
        //标定因数重复
        calGyroFactorRepeat();
        break;
    case 7:
        //零偏重复
        calBiasRepeat();
        break;
    case 8:
        //零偏
        calBias();
        break;
    default:
        break;
    }
    ui->bt_sartcal->setDisabled(true);
}

void CGyroCalibPage::calBias(){
    double dscalefactor = ui->le_calfactorparam->text().toDouble();
    qDebug()<<"calBial:"<<dscalefactor;
    int iunit = 1;//°/s
    if(ui->cb_unit->currentIndex() == 0){
        iunit = 3600;//°/h
    }
    //零偏=采集一段时间的数据求平均值，然后除以标度因数
    double bias = 0.0;
    for (int i = 0;i < m_vdData->size();i++) {
        double avgval[1];
        avgval[0] = m_vdData->at(i);
        bias = ::Bias(avgval, 1, dscalefactor == 0 ? 1.0 : dscalefactor);
        m_vbisass.append(bias * iunit);
    }

    int item_num = m_vStandItems.size();
    int value_num = m_vbisass.size();
    int min_num = value_num < item_num ? value_num :item_num;
    for (int i = 0;i < item_num;i++) {
        m_vStandItems.at(i)->setText("");
    }

    double totalbias = 0.0;
    for(int i = 0; i < min_num; i++){
        m_vStandItems.at(i)->setText(QString::number(m_vbisass.at(min_num - i - 1), 'f', 8));
        totalbias += m_vbisass.at(i);
    }

    ui->le_zerobias->setText(QString::number(totalbias/min_num, 'f', 8));
}

void CGyroCalibPage::calGyrocalibr(){
    qDebug()<<"slotReadFileEnd:"<<m_vsSpeed->toVector()<<m_vdData->toVector()<<m_vsFilename->toVector();
    //将计算后的均值数据按正负角度归类
    int pos = -1;
    bool isafter = false;
    double cspeed = 0.0;
    double fr = 0.0;
    GyrocalData *gdatax = new GyrocalData;
    GyrocalData *gdatay = new GyrocalData;
    GyrocalData *gdataz = new GyrocalData;
    GyrocalData *gdata = NULL;

    if(m_vsSpeed->size() != 0){
        for (int i = 0;i < m_vsSpeed->size(); i++) {
            //qDebug()<<"m_vdSpeed"<<m_vsSpeed->at(i)<<m_vsSpeed->at(i).toDouble();
            QString aix = QFileInfo(m_vsFilename->at(i)).fileName().at(1);
            if(aix == "x"){
                gdata = gdatax;
            }else if(aix == "y"){
                gdata = gdatay;
            }else if(aix == "z"){
                gdata = gdataz;
            }else{
                continue;
            }
            gdata->saxi = QFileInfo(m_vsFilename->at(i)).fileName().at(1);
            if(m_vsSpeed->at(i).compare("before") == 0){
                isafter = true;
                gdata->vspeed.append(0.0);
                cspeed = 0.0;
                pos = m_vsSpeed->indexOf("after");
                while(QFileInfo(m_vsFilename->at(pos)).fileName().at(1) != aix){
                    pos = m_vsSpeed->indexOf("after", pos + 1);
                }
            }else if(m_vsSpeed->at(i).toDouble() > 0){
                isafter = false;
                cspeed = m_vsSpeed->at(i).toDouble();
                //qDebug()<<cspeed;
                gdata->vspeed.append(cspeed);
                pos = m_vsSpeed->indexOf(QString::number(-cspeed));
                while(QFileInfo(m_vsFilename->at(pos)).fileName().at(1) != aix){
                    pos = m_vsSpeed->indexOf(QString::number(-cspeed), pos + 1);
                }
            }else{
                continue;
            }

            if(pos == -1){
                continue;
            }

            gdata->vpostive.append(m_vdData->at(i));
            gdata->vpastive.append(m_vdData->at(pos));

            if(cspeed == 0.0){
                fr = (m_vdData->at(i) + m_vdData->at(pos)) / 2;
            }
            //qDebug()<<gdata->vspeed.at(i)<<gdata->vpastive.at(i)<<gdata->vpostive.at(i)<<pos;
        }
    }
    delete m_vsSpeed;
    m_vsSpeed = nullptr;
    delete m_vdData;
    m_vdData = nullptr;
    delete m_vsFilename;
    m_vsFilename = nullptr;
    //调用标定算法进行计算
    qDebug()<<"califactor0:"<<gdata->vspeed<<gdata->vspeed.size()<<gdata->vpostive<<gdata->vpastive<<fr;
    //double califactor = ::Gyro_Scale(gdata->vspeed.data(), gdata->vspeed.size(), gdata->vpostive.data(), gdata->vpastive.data(), fr);
    //double dsp[5] = {0.1, 0.5, 1, 5, 0};
    //double dpo[5] = {29897, 149469, 298831, 1.49483e+06, 51};
    //double dpa[5] = {-30009, -149636, -299018, -1.49499e+06, -22};
    double sm[12] = {0.0};
    ::Gyro_Cali(gdatax->vspeed.data(), gdatax->vspeed.size(), \
                                    gdatax->vpostive.data(), gdatax->vpastive.data(), \
                                    gdatay->vpostive.data(), gdatay->vpastive.data(), \
                                    gdataz->vpostive.data(), gdataz->vpastive.data(), sm);

    //回显数据到页面

}
void CGyroCalibPage::calAccelCalibr(){

}
void CGyroCalibPage::calBiasStab(){
    m_dGyroFactor = ui->le_calfactorparam->text().toDouble();
    int ifrq = ui->cb_catfrq->currentText().toInt();
    int iavg = ui->cb_avgtime->currentText().toInt();
    qDebug()<<"calBiasStab"<<m_vdData->toVector().size()<<m_dGyroFactor;
    double dbias_stab = ::Bias_Stability(m_vdData->toVector().data(), m_vdData->size() , ifrq, iavg, m_dGyroFactor);
    ui->le_zerobiasstable->setText(QString::number(dbias_stab));
    m_vbiasstab.append(dbias_stab);

    int item_num = m_vStandItems.size();
    int value_num = m_vbiasstab.size();
    int min_num = value_num < item_num ? value_num :item_num;
    for (int i = 0;i < item_num;i++) {
        m_vStandItems.at(i)->setText("");
    }

    for(int i = 0; i < min_num; i++){
        m_vStandItems.at(i)->setText(QString::number(m_vbiasstab.at(value_num - 1 - i)));
    }
    int iunit = 1;//°/s
    if(ui->cb_unit->currentIndex() == 0){
        iunit = 3600;//°/h
    }
    ui->le_zerobias->setText(QString::number(::Bias(m_vdData->toVector().data(), m_vdData->size(), m_dGyroFactor == 0 ? 1.0 : m_dGyroFactor) * iunit));

}
void CGyroCalibPage::calBiasRepeat(){
    QVector<double> vdata;
    QString sdata;
    QString sunit = ui->cb_unit->currentText();
    for (int i = 0;i < m_vStandItems.size();i++) {
        sdata = m_vStandItems.at(i)->text();
        if(!sdata.isEmpty()){
            vdata.append(sdata.toDouble());
        }
    }

    if(vdata.size() < 2){
        QMessageBox::critical(this, QString::fromLocal8Bit("计算失败！"), QString::fromLocal8Bit(" 零偏个数少于2，无法计算其重复性"));
        return;
    }

    qDebug()<<"vdata:"<<vdata;
    double drepeat = ::Bias_Repeat(vdata.data(), vdata.size());
    ui->le_zeroreapeat->setText(QString::number(drepeat, 0, 8) + " " + sunit);
}

void CGyroCalibPage::calGyroFactorRepeat(){
    QVector<double> vdata;
    QString sdata;
    for (int i = 0;i < m_vStandItems.size();i++) {
        sdata = m_vStandItems.at(i)->text();
        if(!sdata.isEmpty()){
            vdata.append(sdata.toDouble());
        }
    }

    if(vdata.size() < 2){
        QMessageBox::critical(this, QString::fromLocal8Bit("计算失败！"), QString::fromLocal8Bit("标定因数个数少于2，无法计算其重复性"));
        return;
    }

    double drepeat = ::Scale_Repeat(vdata.data(), vdata.size());
    ui->le_calfatorrepeat->setText(QString::number(drepeat * 1000000) + " ppm");
}

/*计算零偏*/
double CGyroCalibPage::calculateZeroBias(const double dval,const double calfactor){
    return dval / calfactor;
}

/*零偏稳定性*/
void CGyroCalibPage::cZeroBiasStable(){
    double dbiasstab = 0.0;
    m_dGyroFactor = ui->le_calfactorparam->text().toDouble();
    dbiasstab = ::Bias_Stability(m_vdData->toVector().data() , m_vdData->size() , 1, 1, m_dGyroFactor);
    ui->le_zerobiasstable->setText(QString::number(dbiasstab));
}

void CGyroCalibPage::slotDataShow(const QStringList datakeys, const QVector<double> dataValues, const QString sportN, const int protoindex){
    int iaxis = ui->cb_aixs->currentIndex();
    m_bIsRealEnd = false;
    //qDebug()<<datakeys;
    //qDebug()<<dataValues;
    if(m_vListItems.size() == 0){
        for (int i = 0; i < LIST_ITEM_SIZE; ++i) {
            QListWidgetItem *lwitem = new QListWidgetItem("");
            lwitem->setFont(QFont("新宋体", 14));
            lwitem->setSizeHint(QSize(20, 23));
            m_vListItems.push_back(lwitem);
            ui->lw_rtdata->addItem(lwitem);
        }
        m_vListItems.at(0)->setData(Qt::ForegroundRole, QColor(Qt::blue));
    }
    //循环显示数据
    for (int i = 2; i < LIST_ITEM_SIZE; ++i) {
        m_vListItems.at(LIST_ITEM_SIZE - i)->setText(m_vListItems.at(LIST_ITEM_SIZE - 1 - i)->text());
        m_dRtdatas[LIST_ITEM_SIZE - i] = m_dRtdatas[LIST_ITEM_SIZE - i - 1];
    }
    m_vListItems.at(1)->setText(QString("%1").arg(dataValues.at(iaxis)));
    m_dRtdatas[1] = dataValues.at(iaxis);
    m_allDataVal += dataValues.at(iaxis);
    m_iallSize++;
    //第一个显示平均值
    m_vListItems.at(0)->setText(QString("%1").arg(m_allDataVal/m_iallSize));
    //将数据前移
    if(m_iStCurrentPos + 1 == LIST_STABLE_SIZE * 2){
        for (int i = 0;i < LIST_STABLE_SIZE; i++) {
            m_dStdatas[i] = m_dStdatas[LIST_STABLE_SIZE + i];
        }
        m_iStCurrentPos = LIST_STABLE_SIZE - 1;
    }
    m_dStdatas[++m_iStCurrentPos] = dataValues.at(iaxis);
    QVector<double> vd;
    QSet<double> sd;
    double dscalefactor = ui->le_calfactorparam->text().toDouble();
    int iunit = 1;//°/s
    if(ui->cb_unit->currentIndex() == 0){
        iunit = 3600;//°/h
    }
    //零偏=采集一段时间的数据求平均值，然后除以标度因数
    double bias = ::Bias(m_dRtdatas, 1, dscalefactor == 0 ? 1.0 : dscalefactor);
    m_vbisass.append(bias);
    ui->le_zerobias->setText(QString::number(bias * iunit));
    int len = m_iStCurrentPos < LIST_STABLE_SIZE ? m_iStCurrentPos : LIST_STABLE_SIZE;
    int offset = m_iStCurrentPos < LIST_STABLE_SIZE ? 0 : m_iStCurrentPos - LIST_STABLE_SIZE;
    //qDebug()<<"offset:"<<len<<offset<<m_iStCurrentPos;
    //数据已做平滑处理，频率和平滑时间直接填1即可
    //采集一段时间的数据，先做平滑处理，然后对平滑后的数据求标准差，然后再除以标度因数，每隔10s计算一次也就是增加10条数据计算一次
    if(len % 10 == 0){
        m_watcher = new QFutureWatcher<double>;
        m_watcher->setFuture(m_future);
        QObject::connect(m_watcher, &QFutureWatcher<double>::finished, [&]() {
            //qDebug()<<"finished";
            ui->le_zerobiasstable->setText(QString::number(m_future.result()));
            delete m_watcher;
        });
        m_future = QtConcurrent::run([=](){
            QElapsedTimer t;
            t.start();
            //qDebug()<<"QtConcurrent::run"<<dscalefactor<<len;
            double result = ::Bias_Stability(m_dStdatas + offset, len , 1, 1, dscalefactor);
            //qDebug()<<"QtConcurrent::run result"<<result<<t.elapsed();
            return result;
        });
    }
}

void CGyroCalibPage::on_bt_reset_clicked()
{
    //清理上次导入或者计算的数据
    for(QTableWidgetItem *twi:m_vTableItems){
        delete twi;
    }
    m_vTableItems.clear();

    int icurrent_index = ui->cb_calgoal->currentIndex();
    if(icurrent_index == 0 && m_vgyrocalifactor.size() > 0){
        m_vgyrocalifactor.pop_back();
        on_cb_calgoal_currentIndexChanged(0);
    }

    if(icurrent_index == 3 && m_vbiasstab.size() > 0){
        m_vbiasstab.pop_back();
        on_cb_calgoal_currentIndexChanged(3);
    }
}

void CGyroCalibPage::slotDestory(){
    int iunit = 1;//°/s
    if(ui->cb_unit->currentIndex() == 0){
        iunit = 3600;//°/h
    }
    double dscalefactor = ui->le_calfactorparam->text().toDouble();
    double dscaletotal = 0.0;
    for(int i = 0; i < m_vbisass.size(); i++){
        dscaletotal += m_vbisass[i];
    }
    int offset = m_iStCurrentPos < LIST_STABLE_SIZE ? 0 : m_iStCurrentPos - LIST_STABLE_SIZE;
    ui->le_zerobias->setText(QString::number(dscaletotal / m_vbisass.size() * iunit));
    ui->le_zerobiasstable->setText(QString::number(::Bias_Stability(m_dStdatas + offset, m_iStCurrentPos, 1, 1, dscalefactor)));
    m_vbisass.clear();
    m_iStCurrentPos = 0;
    m_vbiasstab.clear();
    //关闭串口时将所有计数清零
    m_iallSize = 0;
    m_allDataVal = 0.0;
}

void CGyroCalibPage::on_cb_unit_currentIndexChanged(int index)
{
    //重新选择单位后可重新计算
    ui->bt_sartcal->setDisabled(false);
    m_vbisass.clear();
}

void CGyroCalibPage::on_le_calfactorparam_textChanged(const QString &arg1)
{
    //修改标度因数后可重新计算
    ui->bt_sartcal->setDisabled(false);
    m_vbisass.clear();
}
