#include <QCoreApplication>
#include <QTcpSocket>
#include <QTimer>
#include <QDebug>
#include <QHostAddress>

/*
* TCP客户端测试程序
* 用于测试TCP服务器功能
*/
class TcpTestClient : public QObject
{
    Q_OBJECT
public:
    TcpTestClient(QObject *parent = nullptr) : QObject(parent)
    {
        m_socket = new QTcpSocket(this);
        connect(m_socket, &QTcpSocket::connected, this, &TcpTestClient::onConnected);
        connect(m_socket, &QTcpSocket::disconnected, this, &TcpTestClient::onDisconnected);
        connect(m_socket, &QTcpSocket::readyRead, this, &TcpTestClient::onReadyRead);
        connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                this, &TcpTestClient::onError);
    }
    
    void connectToServer(const QString &host, quint16 port)
    {
        qDebug() << "Connecting to" << host << ":" << port;
        m_socket->connectToHost(QHostAddress(host), port);
    }
    
    void sendTestData()
    {
        if(m_socket->state() == QAbstractSocket::ConnectedState){
            // 发送测试数据 - 模拟协议数据
            QByteArray testData = QByteArray::fromHex("AA55010203040506070809");
            m_socket->write(testData);
            qDebug() << "Sent test data:" << testData.toHex(' ').toUpper();
        }
    }
    
private slots:
    void onConnected()
    {
        qDebug() << "Connected to server";
        
        // 连接成功后发送测试数据
        QTimer::singleShot(1000, this, &TcpTestClient::sendTestData);
        
        // 每5秒发送一次测试数据
        QTimer *timer = new QTimer(this);
        connect(timer, &QTimer::timeout, this, &TcpTestClient::sendTestData);
        timer->start(5000);
    }
    
    void onDisconnected()
    {
        qDebug() << "Disconnected from server";
    }
    
    void onReadyRead()
    {
        QByteArray data = m_socket->readAll();
        qDebug() << "Received data:" << data.toHex(' ').toUpper();
    }
    
    void onError(QAbstractSocket::SocketError error)
    {
        qDebug() << "Socket error:" << error << m_socket->errorString();
    }
    
private:
    QTcpSocket *m_socket;
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    TcpTestClient client;
    
    // 连接到TCP服务器
    client.connectToServer("192.168.127.254", 8080);
    
    return app.exec();
}

#include "test_tcp_client.moc"
