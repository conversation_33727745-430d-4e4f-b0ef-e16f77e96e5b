﻿#include "caboutdlg.h"
#include "ui_caboutdlg.h"
#include "cconfigmanager.h"

CAboutDlg::CAboutDlg(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CAboutDlg)
{
    ui->setupUi(this);
    QPixmap pixm(":/img/tlhlogo.png");
    QPixmap pixmt(":/img/tlhpublicAcct.png");
    QPixmap pixmp(":/img/tlhChannels.png");
    QPixmap pixmc(":/img/tlhTiktok.png");
    ui->label_2->setPixmap(pixm);
    ui->lb_Publicacct ->setPixmap(pixmp);
    ui->lb_Tiktok->setPixmap(pixmt);
    ui->lb_Channels->setPixmap(pixmc);

    ui->lb_version->setText(VERSION);
}

CAboutDlg::~CAboutDlg()
{
    delete ui;
}
