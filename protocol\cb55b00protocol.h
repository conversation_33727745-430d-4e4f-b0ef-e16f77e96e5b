﻿#ifndef CB55B00PROTOCOL_H
#define CB55B00PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

//波特率115200

class Cb55b00Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit Cb55b00Protocol(QObject *parent = nullptr);
    ~Cb55b00Protocol();

    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool preInit();
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strub55b00{
        unsigned short head;
        short fogx;
        short fogy;
        int fogz;
        short accx;
        short accy;
        short accz;
        short ftemp;
        short utemp;
        unsigned char ero;
    }Strub55b00;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN, const int protoindex);
    void sigFreqUpdate(const QStringList &datakeys, const QVector<double> dataValues, const QString &sportN, const int protoindex);
private:
    int m_icatFreqCounts;
    long m_lcatCurrCounts;
    QVector<double> m_vGyrXDatas;
    QVector<double> m_vGyrYDatas;
    QVector<double> m_vGyrZDatas;
    QVector<double> m_vCaliXDatas;
    QVector<double> m_vCaliYDatas;
    QVector<double> m_vCaliZDatas;
    QVector<double> m_vTempFDatas;
    QVector<double> m_vTempUDatas;
    double m_davggyrx;
    double m_davggyry;
    double m_davggyrz;
    double m_davgcalix;
    double m_davgcaliy;
    double m_davgcaliz;
    double m_davgtempf;
    double m_davgtempu;
    QStringList m_slFreqKeys;
};

#endif // CB55B00PROTOCOL_H
