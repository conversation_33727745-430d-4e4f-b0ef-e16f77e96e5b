﻿#include "cfilefieldconfigwidget.h"
#include "ui_cfilefieldconfigwidget.h"

#include "cprotocolfactory.h"
#include "cbaseprotocol.h"
#include "cprotoparamdata.h"
#include "ctlhtools.h"
#include <QFileDialog>
#include <QDir>
#include <QMessageBox>

CFileFieldConfigWidget::CFileFieldConfigWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CFileFieldConfigWidget)
{
    ui->setupUi(this);
    init();
}

CFileFieldConfigWidget::~CFileFieldConfigWidget()
{
    delete ui;
}

void CFileFieldConfigWidget::init(){
    QString protoname = ui->cb_protocoln->currentText();
    m_keysNames =  CProtocolFactory::getProKeys(protoname);
    for (int i = 0; i < m_keysNames.size(); ++i) {
        QListWidgetItem *lwitem = new QListWidgetItem(m_keysNames.at(i));
        lwitem->setFlags(lwitem->flags() & ~Qt::ItemIsUserCheckable);
        m_lWidItems.append(lwitem);
        lwitem->setCheckState(Qt::Unchecked);
        lwitem->setFont(QFont("新宋体", 14));
        lwitem->setSizeHint(QSize(20, 23));
        ui->lw_ffieldwid->addItem(lwitem);
    }

    connect(ui->lw_ffieldwid, &QListWidget::itemSelectionChanged, this, &CFileFieldConfigWidget::slotItemChecked);

    ui->cb_field->addItems(m_keysNames);
}

void CFileFieldConfigWidget::slotItemChecked(){
    for(QListWidgetItem *it:m_lWidItems){
        if(it->isSelected()){
            it->setCheckState(Qt::Checked);
        }else{
            it->setCheckState(Qt::Unchecked);
        }
    }

    //ui->cb_field->clear();
    //
    //QString protoname = ui->cb_protocoln->currentText();
    //QStringList keysName =  CProtocolFactory::getProKeys(protoname);
    //
    //QVector<int> vsel = selectedItemsIndex();
    ////将第一个协议索引删除
    //vsel.remove(0);
    //for(int i = 0; i < keysName.size(); i++){
    //    if(vsel.contains(i)){
    //        ui->cb_field->addItem(keysName.at(i));
    //    }
    //}

}

QVector<int> CFileFieldConfigWidget::selectedItemsIndex(){
    QVector<int> vs;
    QString protoname = ui->cb_protocoln->currentText();
    int protoindex = CBaseProtocol::getProtoIndex(protoname);
    //第一个存放协议索引，后面依次存放选择字段的位置索引
    vs.append(protoindex);
    for(int i = 0; i < m_lWidItems.size(); i++){
        if(m_lWidItems.at(i)->checkState()){
            vs.append(i);
        }
    }

    qDebug()<<"selectedItemsIndex:"<<vs;
    //QList<QListWidgetItem *> lwItem = ui->lw_ffieldwid->items();
    return vs;
}

bool CFileFieldConfigWidget::getisNeedDat(){
    if(ui->ch_needdat->checkState()){
        return true;
    }else{
        return false;
    }
}

int CFileFieldConfigWidget::getDatType(){
    return ui->cb_datType->currentIndex();
}

void CFileFieldConfigWidget::setisNeedDat(bool stat){
    if(stat){
        ui->ch_needdat->setChecked(true);
    }else{
        ui->ch_needdat->setChecked(false);
    }
}

void CFileFieldConfigWidget::on_bt_selectall_clicked()
{
    for(int i = 0; i < m_lWidItems.size(); i++){
        m_lWidItems.at(i)->setCheckState(Qt::Checked);
        m_lWidItems.at(i)->setSelected(true);
    }
}

void CFileFieldConfigWidget::on_bt_reservestat_clicked()
{
    for(int i = 0; i < m_lWidItems.size(); i++){
        Qt::CheckState cstat = m_lWidItems.at(i)->checkState();
        if(cstat == Qt::Checked){
            cstat = Qt::Unchecked;
        }else{
            cstat = Qt::Checked;
        }
        m_lWidItems.at(i)->setCheckState(cstat);
        m_lWidItems.at(i)->setSelected(cstat);
    }
}

void CFileFieldConfigWidget::on_cb_protocoln_currentTextChanged(const QString &protoname)
{
    m_keysNames =  CProtocolFactory::getProKeys(protoname);
    //qDebug()<<"keysName:"<<keysName;
    for (int i = 0; i < m_lWidItems.size();i++) {
        QListWidgetItem *lwitem = m_lWidItems.at(i);
        ui->lw_ffieldwid->removeItemWidget(lwitem);
        delete lwitem;
    }
    m_lWidItems.clear();

    for (int i = 0; i < m_keysNames.size(); ++i) {
        QListWidgetItem *lwitem = new QListWidgetItem(m_keysNames.at(i));
        m_lWidItems.append(lwitem);
        lwitem->setCheckState(Qt::Unchecked);
        lwitem->setFont(QFont("新宋体", 14));
        lwitem->setSizeHint(QSize(20, 23));
        ui->lw_ffieldwid->addItem(lwitem);
    }

    m_msFactors.clear();

    ui->cb_field->clear();
    ui->cb_field->addItems(m_keysNames);
}

void CFileFieldConfigWidget::on_bt_alterDir_clicked()
{
    QString filepath = QFileDialog::getExistingDirectory(nullptr, QObject::tr("Open Directory"), QDir::homePath());
    if (!filepath.isEmpty()) {
        // 用户选择了一个目录，处理目录
        ui->le_savedir->setText(filepath);
    }
}

QString CFileFieldConfigWidget::getFileDir(){
    return ui->le_savedir->text();
}

void CFileFieldConfigWidget::setFileDir(QString dir){
    ui->le_savedir->setText(dir);
}

void CFileFieldConfigWidget::on_bt_add_clicked()
{
    QString sdivisor = ui->le_divisor->text();
    if(!sdivisor.isEmpty() && CTlhTools::valiDivisorString(sdivisor)){
        ui->te_fielddivisors->append(ui->cb_field->currentText() + "=" + ui->le_divisor->text());
        //m_msFactors[ui->cb_field->currentText()] = ui->le_divisor->text();
    }else{
        QMessageBox::critical(this, tr("Error"), tr("The input factor format is incorrect. The two factors should be separated by a space. Example") + ":*123.1 +23.2");
    }
}

QMap<QString, QString>& CFileFieldConfigWidget::getDivisorMap(){
    return m_msFactors;
}

void CFileFieldConfigWidget::on_bt_apply_clicked()
{
    m_msFactors.clear();
    int count = 0;
    QString contents = ui->te_fielddivisors->toPlainText();
    //qDebug()<<"contents:"<<contents.toLocal8Bit().toHex(' ');
    if(!contents.isEmpty()){
        QStringList alllist = contents.split("\n");
        for (int i = 0;i < alllist.size();i++) {
            QStringList configlist = alllist.at(i).split("=");
            if(m_keysNames.indexOf(configlist.at(0)) == -1){
                QMessageBox::critical(this, u8"Error", tr("Filed") + QString("%1").arg(configlist.at(0)) + tr("not belong current protocol，please confirm data or change protocol!"));
                //qDebug()<<"names:"<<m_keysNames;
                return;
            }
            if(configlist.size() == 2 && CTlhTools::valiDivisorString(configlist.at(1))){
                m_msFactors[configlist.at(0)] = configlist.at(1);
                count++;
                //ui->te_fielddivisors->append(configlist.at(0) + ":" + configlist.at(1));
            }
        }
    }
    QMessageBox::information(this, tr("Info"), QString("%1").arg(count) + tr("records were successfully applied."));
}
