﻿#include "cdeviceconfigwidget.h"
#include "ui_cdeviceconfigwidget.h"
#include <QDebug>

CDeviceConfigWidget::CDeviceConfigWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CDeviceConfigWidget)
{
    ui->setupUi(this);
    //在Qt Designer添加这个协议有问题，改为代码添加
    ui->cb_pprototype->addItem("B55B0000");
    //QAbstractItemModel* model = ui->cb_pprototype->model();
    //// 遍历所有项
    //for (int i = 0; i < model->rowCount(); ++i) {
    //    QString text = model->index(i, 0).data().toString(); // 获取第 i 项的文本
    //    // 处理文本，例如打印
    //    qDebug()<<"CDeviceConfigWidget" << text;
    //}
}

CDeviceConfigWidget::~CDeviceConfigWidget()
{
    delete ui;
}

QMap<QString,QString>& CDeviceConfigWidget::getDeviceParams(){
    m_mParamsMap["PROTOT"] = ui->cb_pprototype->currentText();
    m_mParamsMap["ACCELT"] = ui->cb_pacceltype->currentText();
    m_mParamsMap["GYROT"] = ui->cb_pgyrotype->currentText();
    m_mParamsMap["GYRFREQ"] = ui->cb_gryfrq->currentText();
    m_mParamsMap["CATFREQ"] = ui->cb_catfrq->currentText();
    m_mParamsMap["FPGAFC"] = ui->cb_fpgaframc->currentText();
    return m_mParamsMap;
}
