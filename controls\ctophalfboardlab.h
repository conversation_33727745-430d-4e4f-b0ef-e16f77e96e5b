﻿#ifndef CTOPHALFBOARDLAB_H
#define CTOPHALFBOARDLAB_H

#include <QLabel>

/*
*  飞行视图上方标签控件
*/
class CTopHalfBoardLab : public QLabel
{
    Q_OBJECT
public:
    explicit CTopHalfBoardLab(QWidget *parent = nullptr);
    void paintEvent(QPaintEvent *) override;
    void drawTextVal(QPainter &painter);
    void setTextVal(float fval);

signals:
private:
    bool m_bisValLab;
    int m_integers;
    int m_decimals;

};

#endif // CTOPHALFBOARDLAB_H
