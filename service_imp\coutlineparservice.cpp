﻿#include "coutlineparservice.h"

COutLineParService::COutLineParService(QObject *parent) : QObject(parent)
{
    QThread *serithread = new QThread;
    serithread->start();

    m_resolvTask = new CResolvingTask;
    m_resolvTask->start();

    m_tId = startTimer(100);

    m_resolvTask->setPortN("FILE");

    moveToThread(serithread);

    m_bIsError = false;

    connect(this, &COutLineParService::sigDataRead, m_resolvTask, &CResolvingTask::slotAppendData);
}

COutLineParService::~COutLineParService(){
    m_resolvTask->requestInterruption();
    m_resolvTask->quit();
    m_resolvTask->deleteLater();
}

bool COutLineParService::fileReadData(QString filename){
    m_bIsWorking = true;
    connect(m_pparm, &CProtoParamData::sigParamChange, m_resolvTask, &CResolvingTask::slogParamChange);
    //qDebug()<<"serialReadData:"<<filename;
    m_fhandle.setFileName(filename);
    if(!m_fhandle.open(QIODevice::ReadOnly)){
        emit sigFileReadEnd(tr("File read failure:") + m_fhandle.errorString());
        m_bIsError = true;
        return false;
    }
    return true;
}

void COutLineParService::timerEvent(QTimerEvent *event){
    if(!m_fhandle.isOpen()){
        return;
    }

    //停止解析
    if(!m_bIsWorking){
        killTimer(m_tId);
        emit sigFileReadEnd(tr("File read terminated"));
        return;
    }

    QByteArray rcvdata = m_fhandle.read(READF_BUFF_LEN);
    if(rcvdata.size() == 0){
        killTimer(m_tId);
        emit sigFileReadEnd(tr("File read completed"));
        return;
    }
    emit sigDataRead(rcvdata);
}

void COutLineParService::stopWorking(){
    m_bIsWorking = false;
    deleteLater();
}
