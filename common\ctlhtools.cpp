﻿#include "ctlhtools.h"
#include <QDebug>
#include <QRegularExpression>
#include <QInputDialog>

CTlhTools::CTlhTools()
{

}

bool CTlhTools::isValidString(const QString &str, const QString &validsymbol){
    for(QChar c : str) {
        if(!c.isLetterOrNumber() && !c.isSpace() && validsymbol.indexOf(c) == -1) {
            qDebug()<<"invalid data:"<<c;
            return false; // 发现无效符号
        }
    }
    return true; //所有字符都是有效字符
}

double CTlhTools::getAverage(QVector<double> &vt){
    double totalval = 0.0;
    for(double val:vt){
        totalval += val;
    }
    //qDebug()<<"vt.size:"<<vt.size()<<totalval;
    return totalval / vt.size();
}

QString CTlhTools::char2String(char c){
    if(c == 0x00){
        return QString("");
    }else{
        return QString(c);
    }
}

unsigned char CTlhTools::sum8CheckSum(const QByteArray msg, int bpos, int endpos){
    unsigned char msgchecksum = 0;
    unsigned char calchecksum = 0;
    QByteArray test;
    msgchecksum = msg.at(msg.size() - 1);
    //字节bpos到字节size - endpos的累加和
    for (int i = bpos; i < msg.size() - endpos;i++) {
        calchecksum += (unsigned char)msg[i];
    }

    return calchecksum;
}

unsigned short CTlhTools::sum16CheckSum(const QByteArray msg, int bpos, int endpos){
    //qDebug()<<"sum16CheckSum"<<msg.size();
    uint16_t umsgchecksum = 0;
    uint16_t ucalchecksum = 0;
    ::memcpy(&umsgchecksum, msg.data() + msg.size() - 2, 2);
    for (int i = 0; i < msg.size() - 2;) {
        ucalchecksum +=  (0x00FF & msg[i]) |  (0xFF00 & (msg[i + 1] << 8));
        i += 2;
    }

    return ucalchecksum;
}

//CRC校验
unsigned char CTlhTools:: GetCrc8Check(unsigned char *ptr, int len)
{
        int i,j;
        unsigned char crc = 0x00;

        for(i = 0; i < len; i++)
        {
                crc ^= ptr[i];
                for (j = 0; j < 8; j++)
                {
                        if (crc & 0x80)
                                crc = (crc << 1) ^ 0x31;
                        else
                                crc = (crc << 1);
                }
        }

        return (crc);
}

bool CTlhTools::isDoubleZero(double value) {
    static const double epsilon = std::numeric_limits<double>::epsilon();
    return qAbs(value) < epsilon;
}

// 计算文件CRC32 值
uint32_t CTlhTools::calfile_crc32(QString &sfile) {
    uint32_t crc = 0xFFFFFFFF;
    char buffer[2 * 1024]; // 1MB 缓冲区
    size_t bytes_read;

    QFile binf(sfile);
    if(!binf.open(QIODevice::ReadOnly)){
        return 0;
    }

    while ((bytes_read = binf.read(buffer, sizeof(buffer)))){
        for (size_t i = 0; i < bytes_read; ++i) {
            crc = (crc >> 8) ^ crc32_table[(crc ^ buffer[i]) & 0xFF];
        }
    }
    binf.close();

    return crc ^ 0xFFFFFFFF;
}

bool CTlhTools::valiDivisorString(const QString& input) {
    QStringList spresult = input.split(' ');

    for(int i = 0; i < spresult.size(); i++){
        // 定义正则表达式
        QRegularExpression regex("(^[+\\-*/][0-9]+(\\.[0-9]+)?)$");

        // 使用正则表达式匹配输入字符串
        QRegularExpressionMatch match = regex.match(spresult.at(i));

        // 如果匹配成功，返回 true，否则返回 false
        if(!match.hasMatch()){
            return false;
        }
    }
    return true;
}

double CTlhTools::calDivisor(double dval, QString divisor){
    QStringList divisorlist = divisor.split(' ');
    for(QString str:divisorlist){
        if (str.isEmpty()) {
             continue;  // 跳过空字符串
        }
        char optype = str.toUtf8().at(0);
        bool ok;
        double calval = str.mid(1, str.size()).toDouble(&ok);
        if (!ok) {
             qDebug() << "Invalid number format:" << str.mid(1);
             continue;  // 跳过无效的数值
        }
        //qDebug()<<"calDivisor:"<<optype<<calval;
        switch (optype) {
        case '+':
            dval += calval;
            break;
        case '-':
            dval -= calval;
            break;
        case '*':
            dval *= calval;
            break;
        case '/':
            dval /= calval;
            break;
        default:
            break;
        }
    }
    return dval;
}

bool CTlhTools::passwordCheck(){
    //QInputDialog dialog;
    //
    //// 设置对话框的标题
    //dialog.setWindowTitle(tr("PwdInput"));
    //// 设置对话框的提示文本
    //dialog.setLabelText(tr("Please input a password:"));
    //// 设置输入框为密码模式
    //dialog.setTextEchoMode(QLineEdit::Password);
    //// 设置对话框的输入模式为文本输入
    //dialog.setInputMode(QInputDialog::TextInput);
    //
    //if (dialog.exec() == QDialog::Accepted)
    //{
    //    QString password = dialog.textValue();
    //    qDebug() << u8"输入的密码为：" << password;
    //    if(password == "TLH666888"){
    //        return true;
    //    }
    //}
    return true;
}



