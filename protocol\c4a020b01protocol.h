﻿#ifndef C4A020B01PROTOCOL_H
#define C4A020B01PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class C4A020B01Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C4A020B01Protocol(QObject *parent = nullptr);
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru4a020b{
        unsigned short head;
        unsigned short cmd;
        short gryotempx;
        short gryotempy;
        short gryotempz;
        short acceltemp;
        double gyro_x;
        double gyro_y;
        double gyro_z;
        double accel_x;
        double accel_y;
        double accel_z;
        unsigned char checksum;
    }Stru4a020b;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);

signals:

};

#endif // C4A020B01PROTOCOL_H
