﻿#ifndef CFILEFIELDCONFIGWIDGET_H
#define CFILEFIELDCONFIGWIDGET_H

#include <QWidget>
#include <QListWidgetItem>

namespace Ui {
class CFileFieldConfigWidget;
}

/*
*文件字段设置子页面
*/
class CFileFieldConfigWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CFileFieldConfigWidget(QWidget *parent = nullptr);
    ~CFileFieldConfigWidget();
    QVector<int> selectedItemsIndex();
    bool getisNeedDat();
    int getDatType();
    void setisNeedDat(bool);
    QString getFileDir();
    void setFileDir(QString);
    QMap<QString, QString>& getDivisorMap();
private slots:
    void on_bt_selectall_clicked();

    void on_bt_reservestat_clicked();

    void on_cb_protocoln_currentTextChanged(const QString &arg1);
    void on_bt_alterDir_clicked();

    void on_bt_add_clicked();

    void slotItemChecked();

    void on_bt_apply_clicked();

private:
    void init();

private:
    Ui::CFileFieldConfigWidget *ui;
    QList<QListWidgetItem *> m_lWidItems;
    QMap<QString, QString> m_msFactors;
    QStringList m_keysNames;
};

#endif // CFILEFIELDCONFIGWIDGET_H
