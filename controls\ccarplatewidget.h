﻿#ifndef CCARPLATEWIDGET_H
#define CCARPLATEWIDGET_H

#include <QLabel>
#include <QtMath>

/*
* 仪表视图仪表控件
*/
class CCarplateWidget : public QLabel
{
    Q_OBJECT

public:
    explicit CCarplateWidget(QWidget *parent = nullptr);
    ~CCarplateWidget();

    void paintEvent(QPaintEvent*);
    void resizeEvent(QResizeEvent *e);
    void DrawPoint(QPainter& painter,int radius);
    void DrawDigitalOne(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle);
    void DrawDigitalTwo(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle);
    void DrawCircle_bom(QPainter& painter,int radius);
    void DrawCircle_bom_shine(QPainter& painter,int radius);
    void DrawCircle_bom_big(QPainter& painter,int radius);
    void DrawCircle_bom_small(QPainter& painter,int radius);
    void DrawCircle_arc(QPainter& painter,int radius, float degRotate);
    void DrawCircle(QPainter& painter,int radius);
    void DrawCircle_line(QPainter& painter,int radius, int tangle);
    void DrawSmallScale(QPainter& painter,int radius, int pointnum,int startsite, float angle);
    void DrawSmallScaletwo(QPainter& painter,int radius, int pointnum,int startsite, float angle);
    void DrawUnit(QPainter& painter,int radius, QString name, QString unit);
    void DrawNum(QPainter& painter,int radius, float degRotate);
    void DrawPointer(QPainter& painter,int radius,float degRotate, int startsite);
    void DrawsecondPointer(QPainter& painter,int radius, float degRotate, int startsite);
    void DrawBackground(QPainter& painter,int radius, QColor &color);
    void drawBorderOut(QPainter *painter);
    void drawBorderIn(QPainter *painter);
    void drawBorderCenter(QPainter *painter);
    void drawSpeedPlate(QPainter &painter);
    void drawAltitudePlate(QPainter &painter);
    QString genSpeedDigital(int i);
    QString genAltitudeDigital(int i);
    void drawAltitudeHL(QPainter &painter, int radius, QString hl);

    void showspeedview(float val);
    void showheightview(float fval);

private:
    int direction;
    float m_spdegRotate = 0.0; //速度指针值
    float m_hgdegRotate = 0.0; //高度指针值
    QTimer *myTimer;
    QColor borderOutColorStart; //外边框渐变开始颜色
    QColor borderOutColorEnd;   //外边框渐变结束颜色
    QColor borderCenterColorStart; //外边框渐变开始颜色
    QColor borderCenterColorEnd;   //外边框渐变结束颜色
    QColor borderInColorStart;  //里边框渐变开始颜色
    QColor borderInColorEnd;    //里边框渐变结束颜色
    int m_iradius;
    int m_iside;
};

#endif // CCARPLATEWIDGET_H
