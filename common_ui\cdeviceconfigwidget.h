﻿#ifndef CDEVICECONFIGWIDGET_H
#define CDEVICECONFIGWIDGET_H

#include <QWidget>
#include <QMap>

namespace Ui {
class CDeviceConfigWidget;
}

/*
*设备配置设置页面
*/
class CDeviceConfigWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CDeviceConfigWidget(QWidget *parent = nullptr);
    ~CDeviceConfigWidget();
    QMap<QString,QString>& getDeviceParams();

private:
    Ui::CDeviceConfigWidget *ui;
    QMap<QString, QString> m_mParamsMap;
};

#endif // CDEVICECONFIGWIDGET_H
