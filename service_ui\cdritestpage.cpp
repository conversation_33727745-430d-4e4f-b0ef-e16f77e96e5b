﻿#include "cdritestpage.h"
#include "ui_cdritestpage.h"
#include <QDebug>
#include <QThread>
#include <QDockWidget>
#include <QTextEdit>
#include <QLCDNumber>
#include <QPainter>

CDriTestPage::CDriTestPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CDriTestPage)
{
    ui->setupUi(this);

    QLabel *lkeylab = new QLabel(tr("FLoss") + ":0", this);
    lkeylab->resize(100, 30);
    lkeylab->setObjectName("labfloss");
    m_labMap["LABFLOSS"] = lkeylab;

    QLabel *ckeylab = new QLabel(tr("ChFail") + ":0", this);
    ckeylab->resize(100, 30);
    ckeylab->setObjectName("labcheck");
    m_labMap["LABCHECK"] = ckeylab;

    QLabel *ekeylab = new QLabel(tr("Except") + ":0", this);
    ekeylab->resize(100, 30);
    ekeylab->setObjectName("labexcept");
    m_labMap["LABEXCEPT"] = ekeylab;

    m_labfreq = new QLabel(tr("Freq") + ":0", this);
    m_labfreq->resize(100, 30);
    m_labfreq->setObjectName("labfreq");

    m_combox = new QComboBox(this);
    m_combox->resize(100, 30);
    m_combox->setObjectName("combobox");

    m_lcdtime = new QLCDNumber(this);
    m_lcdtime->resize(100, 30);
    m_lcdtime->setObjectName("lcdtime");
    m_lcdtime->setDigitCount(8);



    m_ucurrtime = 0;

    startTimer(1000);

    connect(m_combox,  QOverload<const QString &>::of(&QComboBox::currentIndexChanged), this, [=](const QString &text){
        qDebug()<<"currentIndexChanged"<<m_labMap.size()<<text;
        //当需要显示的串口发生变化时，更新对应串口的参数标签名称
        m_sComboxPortN = text;
        for (int i = 0;i < m_labMap.size() ;i++) {
            if(m_valuesMap.contains(text) && m_valuesMap[text].size() >= i){
                 //qDebug()<<"currentIndexChanged"<<m_labMap.values().size()<<m_valuesMap[text].size()<<m_valuesMap[text];
                 m_labMap.values().at(i)->setText(m_valuesMap[text].at(i));
            }
        }
        //初始化一个页面显示状态
        if(!m_valuesMap.contains(text)){
             m_valuesMap[text].append(tr("FLoss") + ":0");
             m_valuesMap[text].append(tr("ChFail") + ":0");
             m_valuesMap[text].append(tr("Except") + ":0");
             //默认顺序问题
             m_labMap.values().at(0)->setText(m_valuesMap[text].at(0));
             m_labMap.values().at(2)->setText(m_valuesMap[text].at(1));
             m_labMap.values().at(1)->setText(m_valuesMap[text].at(2));
        }

        QStringList param;
        param.append(text);
        emit sigToProtocol(0, param);
    });

    //qDebug()<<"width:"<<width();

    /*QDockWidget *dWid1 = new QDockWidget(QString::fromLocal8Bit("停靠窗口1"), this);
    dWid1->setFeatures(QDockWidget::DockWidgetMovable);
    dWid1->setAllowedAreas(Qt::LeftDockWidgetArea|Qt::RightDockWidgetArea);

    QTextEdit *tedit1 = new QTextEdit();
    dWid1->setWidget(tedit1);

    QDockWidget *dWid2 = new QDockWidget(QString::fromLocal8Bit("停靠窗口2"), this);
    dWid2->setFeatures(QDockWidget::DockWidgetMovable);

    QTextEdit *tedit2 = new QTextEdit();
    dWid2->setWidget(tedit2);
    */
    m_iLabHight = 40;
}

CDriTestPage::~CDriTestPage()
{
    delete ui;
}

void CDriTestPage::slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex){
    if(m_scurrentPortN.isEmpty()){
        m_combox->setCurrentText(sportN);
    }

    if(m_sComboxPortN != sportN){
        return;
    }

    if(sportN != m_scurrentPortN || m_NaLabList.size() != datakeys.size()){
        for(QLabel *lab:m_NaLabList){
            delete lab;
        }
        for(QLabel *lab:m_VaLabList){
            delete lab;
        }
        m_NaLabList.clear();
        m_VaLabList.clear();
        int colnum = 0;
        int maxnum = max_col_num;
        //if(height()/datakeys.size() < 40){
        //若是超过了75个字段，需要重新调整每行显示的字段数目以及每个字段的label的高度
        if(datakeys.size() > (max_col_num * 4) ){
            maxnum = (datakeys.size() + 4 )/ 4;
            m_iLabHight = height() / maxnum;
        }

        for(int i = 0; i < datakeys.size(); i++){
            QLabel *keylab = new QLabel(datakeys.at(i), this);
            QLabel *valuelab = new QLabel(dataValues.at(i), this);
            keylab->move(2 + (i / maxnum * 200), 2 + ( colnum * m_iLabHight));
            keylab->resize(100,m_iLabHight);
            keylab->setObjectName("keylab");
            valuelab->move(keylab->width() + 2 + (i / maxnum * 200), 2 + (colnum * m_iLabHight));
            valuelab->setObjectName("valuelab");
            valuelab->resize(100,m_iLabHight);
            keylab->show();
            valuelab->show();
            m_NaLabList.append(keylab);
            m_VaLabList.append(valuelab);
            // 每列最多显示MAX_COL_NUM个元素，超过分列显示
            if((i + 1) % maxnum == 0){
                colnum = 0;
            }else{
                colnum++;
            }
        }
        m_scurrentPortN = sportN;
    }else{
        //qDebug()<<datakeys.size()<<dataValues.size();
        for(int i = 0; i < datakeys.size(); i++){
            m_VaLabList.at(i)->setText(dataValues.at(i));
        }

    }

    m_lcdtime->display(dataValues.at(dataValues.size() - 2));
    m_labfreq->setText(tr("Freq") + ":" + dataValues.last());

    //qDebug()<<"slotDataShow:"<<datakeys<<QThread::currentThreadId();
    //qDebug()<<"slotDataShow:"<<dataValues;

    repaint();
}

void CDriTestPage::slotStatusShow(const QStringList &statuskeys, const QStringList statusValues, const QString sportN){

    //qDebug()<<statuskeys<<statusValues;
    /*if(m_scurrentPortN.isEmpty()){
        m_combox->setCurrentText(sportN);
    }

    if(m_sComboxPortN != sportN){
        return;
    }*/

    //更新状态，例如丢帧数，校验失败数
    for(int i = 0; i < statuskeys.size(); i++){
        QString skey = statuskeys.at(i);
        if(m_labMap.contains(skey)){
            if(m_labMap[skey] != NULL){
                m_labMap[skey]->setText(statusValues.at(i));
            }
        }
    }
    //保存当前lab上的状态信息，已用于切换串口展示时同步切换状态
    m_valuesMap[sportN].clear();
    for (int i = 0;i < m_labMap.size() ;i++) {
        m_valuesMap[sportN].append(m_labMap.values().at(i)->text());
    }
    //qDebug()<<"slotStatusShow"<<m_labMap.size()<<m_valuesMap[sportN].size();

}

void CDriTestPage::setSeriComs(const QStringList &comlist){
    m_combox->clear();
    m_combox->addItems(comlist);
}

void CDriTestPage::showEvent(QShowEvent *event){
    //qDebug()<<"show width:"<<width();
    //m_labMap["LABFLOSS"]->move(width() - 100, 80);
    //m_combox->move(width() - 100, 20);
}

void CDriTestPage::resizeEvent(QResizeEvent *event){
    qDebug()<<"resize width:"<<width();
    m_lcdtime->move(width() - 120, 20);
    m_combox->move(width() - 120, 80);
    m_labMap["LABFLOSS"]->move(width() - 120, 140);
    m_labMap["LABCHECK"]->move(width() - 120, 200);
    m_labMap["LABEXCEPT"]->move(width() - 120, 260);
    //qDebug()<<m_labMap["LABFLOSS"]->objectName()<<m_labMap["LABCHECK"]->objectName()<< m_labMap["LABEXCEPT"]->objectName()<<m_labMap["LABEXCEPT"]->text();
    m_labfreq->move(width() - 120, 320);
}

void CDriTestPage::timerEvent(QTimerEvent *e){
    /*int hour = m_ucurrtime / 3600;
    int minutes = (m_ucurrtime % 3600) / 60;
    int seconds = (m_ucurrtime % 3600) % 60;
    m_ucurrtime++;
    QString stime = QString("%1:%2:%3").arg(hour, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));
    m_lcdtime->display(stime);
    */
}

