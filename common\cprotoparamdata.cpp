﻿#include "cprotoparamdata.h"
#include <QDebug>


QMap<int, ProtoFieldSt *> CProtoParamData::m_mPFieldMap;
QMap<int, bool> CProtoParamData::m_mNeedDatMap;
MapParmSt CProtoParamData::m_sMapParmst;
DevParmSt CProtoParamData::m_sDevParmst;
FileParmSt CProtoParamData::m_sFileParmst;

CProtoParamData::CProtoParamData(QObject *parent) : QObject(parent)
{
    m_mparmMap["PROTOT"] = "DEFAULT";
    m_mparmMap["ACCELT"] = "MEMS-HD6089";
    m_mparmMap["GYROT"] = "MEMS";
    m_mparmMap["TIMEOUT"] = "0";
    m_mparmMap["ROUND"] = "0";
    m_mparmMap["INTVTIME"]="0";
    m_mparmMap["GYRDRET"]="0";  //陀螺方向
    m_mparmMap["GYRFREQ"]="0";  //陀螺频率
    m_mparmMap["CATFREQ"]="0";  //采样频率
    m_mparmMap["FPGAFC"]="500";  //FPGA帧循环数
}

void CProtoParamData::initParam(QString key, QString value){
    m_mparmMap[key] = value;
}

void CProtoParamData::initParam(QMap<QString,QString> &mparms){
    for(QString key:mparms.keys()){
        m_mparmMap[key] = mparms[key];
    }
}

void CProtoParamData::informParamSig(){
    qDebug()<<"informParamSig:"<<m_mparmMap;
    emit sigParamChange(m_mparmMap);
}

void CProtoParamData::setProtocolField(int index, QVector<int> vi){
    if(!m_mPFieldMap.contains(index)){
        m_mPFieldMap[index] = new ProtoFieldSt;
    }
    m_mPFieldMap[index]->field = vi;
}

QVector<int>& CProtoParamData::getProtocolField(int index){
    QVector<int> vi(0);
    qDebug()<<"abc";
    if(m_mPFieldMap.contains(index)){
        return m_mPFieldMap[index]->field;
    }else
    {
        return vi;
    }
}

void CProtoParamData::setProtocolDivisor(int index, QMap<QString, QString> &divisor){
    QMap<QString, QString> resmap;
    if(m_mPFieldMap.contains(index)){
        m_mPFieldMap[index]->divisor = divisor;
    }
}

void CProtoParamData::getProtocolDivisor(int index, QMap<QString, QString> &divisor){
    QMap<QString, QString> resmap;
    if(m_mPFieldMap.contains(index)){
        divisor = m_mPFieldMap[index]->divisor;
    }
}

MapParmSt& CProtoParamData::getMapParmSt(){
    return m_sMapParmst;
}

DevParmSt& CProtoParamData::getDevParmSt(){
    return m_sDevParmst;
}

FileParmSt* CProtoParamData::getFileParmSt(){
    return &m_sFileParmst;
}

