﻿#include "cdeviceparmwidget.h"
#include "ui_cdeviceparmwidget.h"
#include <QDebug>
#include <QMessageBox>

CDeviceParmWidget::CDeviceParmWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CDeviceParmWidget)
{
    ui->setupUi(this);

    m_btGroup = new QButtonGroup(this);
    m_btGroup->addButton(ui->ch_debugflag, -11);
    m_btGroup->addButton(ui->ch_fogflag, -12);
    m_btGroup->addButton(ui->ch_gpsflag, -13);
    m_btGroup->addButton(ui->ch_dataflag, -14);
    m_btGroup->addButton(ui->ch_buardflag, -2);
    m_btGroup->addButton(ui->ch_frqflag, -3);
    m_btGroup->addButton(ui->ch_axiflag, -4);
    m_btGroup->addButton(ui->ch_gnssarmflag, -5);
    m_btGroup->addButton(ui->ch_antinstflag, -6);
    m_btGroup->addButton(ui->ch_gnssinitflag, -7);
    m_btGroup->addButton(ui->ch_insangleflag, -8);
    m_btGroup->addButton(ui->ch_insafterwflag, -9);
    m_btGroup->addButton(ui->ch_silenttimeflag, -10);
    m_btGroup->addButton(ui->ch_GyroCaliFlag, -15);
    m_btGroup->addButton(ui->ch_AddiCaliFlag, -16);
    m_btGroup->addButton(ui->ch_sdopcmd, -17);

    m_btGroup->setExclusive(true);

    connect(m_btGroup, QOverload<int>::of(&QButtonGroup::buttonPressed), this, [=](int id){
        switch (id) {
        case -3:
            m_btGroup->button(id)->setChecked(true);
            QMessageBox::information(this, tr("Info"), tr("Before modifying the output frequency, please confirm whether the baud rate meets the output bandwidth requirements!"));
            break;
        default:
            break;
        }
    });
}

CDeviceParmWidget::~CDeviceParmWidget()
{
    delete ui;
}

void CDeviceParmWidget::setBaurdParm(int &ibuard){
    ui->cb_baurd->setCurrentText(QString::number(ibuard));
}
void CDeviceParmWidget::getBaurdParm(int &ibuard){
    ibuard = ui->cb_baurd->currentText().toInt();
}
void CDeviceParmWidget::setFrqParm(int &ifrq){
    ui->cb_DataFrq->setCurrentText(QString::number(ifrq));
}
void CDeviceParmWidget::getFrqParm(int &ifrq){
    ifrq = ui->cb_DataFrq->currentText().toInt();
}
void CDeviceParmWidget::setSitParm(int isit, int isec){
    ui->cb_Coordinate->setCurrentIndex(isit);
    ui->cb_Axial->setCurrentIndex(isec);
}
void CDeviceParmWidget::getSitParm(int &isit, int &isec){
    //仅有24种是有效坐标组合
    //x y z       1 1 1  1 -1 -1  -1 1 -1  -1 -1 1   7 4 2 1     0 4 5 6
    //x z y       1 1 -1 1 -1 1  -1 1 1   -1 -1 -1   6 5 3 0     3 2 1 7
    //y x z       1 1 -1 1 -1 1  -1 1 1   -1 -1 -1   6 5 3 0
    //y z x       1 1  1 1 -1 -1 -1 1 -1  -1 -1  1   7 4 2 1
    //z x y       1 1  1 1 -1 -1 -1 1 -1  -1 -1  1   7 4 2 1
    //z y x       1 1 -1 1 -1 1  -1 1  1  -1 -1  -1  6 5 3 0
    isit = ui->cb_Coordinate->currentIndex();
    isec = ui->cb_Axial->currentIndex();
}
void CDeviceParmWidget::setLevelArmParm(float &ix, float &iy, float &iz){
    qDebug()<<"setLevelArmParm:"<<ix<<iy<<iz;
    ui->le_xLeverarm->setText(QString("%1").arg(ix, 4, 'f', 4));
    ui->le_yLeverarm->setText(QString("%1").arg(iy, 4, 'f', 4));
    ui->le_zLeverarm->setText(QString("%1").arg(iz, 4, 'f', 4));
}
void CDeviceParmWidget::getLevelArmParm(float &ix, float &iy, float &iz){
    ix = ui->le_xLeverarm->text().toFloat();
    iy = ui->le_yLeverarm->text().toFloat();
    iz = ui->le_zLeverarm->text().toFloat();
}
void CDeviceParmWidget::setAnteParm(float &ix, float &iy, float &iz){
    qDebug()<<"setAnteParm:"<<ix<<iy<<iz;
    ui->le_xAnteIns->setText(QString("%1").arg(ix, 4, 'f', 4));
    ui->le_yAnteIns->setText(QString("%1").arg(iy, 4, 'f', 4));
    ui->le_zAnteIns->setText(QString("%1").arg(iz, 4, 'f', 4));
}
void CDeviceParmWidget::getAnteParm(float &ix, float &iy, float &iz){
    ix = ui->le_xAnteIns->text().toFloat();
    iy = ui->le_yAnteIns->text().toFloat();
    iz = ui->le_zAnteIns->text().toFloat();
}
void CDeviceParmWidget::setGnssInitParm(QVector<float> &vParm){
    //经纬高俯仰偏航横滚航向
    ui->le_gnsspitch->setText(QString("%1").arg(vParm.at(0), 4, 'f', 4));
    ui->le_gnssyaw->setText(QString("%1").arg(vParm.at(1), 4, 'f', 4));
    ui->le_gnssroll->setText(QString("%1").arg(vParm.at(2), 4, 'f', 4));
    ui->le_gnsshead->setText(QString("%1").arg(vParm.at(3), 4, 'f', 4));
    ui->le_gnsslng->setText(QString("%1").arg(vParm.at(4), 8, 'f', 8));
    ui->le_gnsslat->setText(QString("%1").arg(vParm.at(5), 8, 'f', 8));
    ui->le_gnssalt->setText(QString("%1").arg(vParm.at(6), 8, 'f', 8));
}
void CDeviceParmWidget::getGnssInitParm(QVector<float> &vParm){
    vParm.clear();
    vParm.append(ui->le_gnsspitch->text().toDouble());
    vParm.append(ui->le_gnssyaw->text().toDouble());
    vParm.append(ui->le_gnssroll->text().toDouble());
    vParm.append(ui->le_gnsshead->text().toDouble());
    vParm.append(ui->le_gnsslng->text().toDouble());
    vParm.append(ui->le_gnsslat->text().toDouble());
    vParm.append(ui->le_gnssalt->text().toDouble());
}
void CDeviceParmWidget::setAngDevParm(float &ix, float &iy, float &iz){
    ui->le_offsetpitch->setText(QString("%1").arg(ix, 4, 'f', 4));
    ui->le_offsetyaw->setText(QString("%1").arg(iy, 4, 'f', 4));
    ui->le_offsetroll->setText(QString("%1").arg(iz, 4, 'f', 4));
}
void CDeviceParmWidget::getAngDevParm(float &ix, float &iy, float &iz){
    ix = ui->le_offsetpitch->text().toFloat();
    iy = ui->le_offsetyaw->text().toFloat();
    iz = ui->le_offsetroll->text().toFloat();
}
void CDeviceParmWidget::setAfterWheelParm(float &ix, float &iy, float &iz){
    ui->le_xvector->setText(QString("%1").arg(ix, 4, 'f', 4));
    ui->le_yvector->setText(QString("%1").arg(iy, 4, 'f', 4));
    ui->le_zvector->setText(QString("%1").arg(iz, 4, 'f', 4));
}
void CDeviceParmWidget::getAfterWheelParm(float &ix, float &iy, float &iz){
    ix = ui->le_xvector->text().toFloat();
    iy = ui->le_yvector->text().toFloat();
    iz = ui->le_zvector->text().toFloat();
}
void CDeviceParmWidget::setBiasTimeParm(int &itime){
    ui->le_zbiastime->setText(QString::number(itime));
}
void CDeviceParmWidget::getBiasTimeParm(int &itime){
    itime = ui->le_zbiastime->text().toInt();
}
void CDeviceParmWidget::setDebugFlagParm(int &iflag){
    if(iflag > 0){
        ui->cb_isdebug->setCurrentIndex(1);
    }else{
        ui->cb_isdebug->setCurrentIndex(0);
    }
}
void CDeviceParmWidget::getDebugFlagParm(int &iflag){
    if(ui->cb_isdebug->currentIndex() == 0){
        iflag = 1;
    }else{
        iflag = 0;
    }
}
void CDeviceParmWidget::setFogTypeParm(int &itype){
    ui->cb_fogtype->setCurrentIndex(itype);
}
void CDeviceParmWidget::getFogTypeParm(int &itype){
    itype = ui->cb_fogtype->currentIndex();
}
void CDeviceParmWidget::setGpsTypeParm(int &itype){
    ui->cb_gpstype->setCurrentIndex(itype);
}
void CDeviceParmWidget::getGpsTypeParm(int &itype){
    itype = ui->cb_gpstype->currentIndex();
}
void CDeviceParmWidget::setDataTypeParm(int &itype){
    ui->cb_dataouttype->setCurrentIndex(itype);
}
void CDeviceParmWidget::getDataTypeParm(int &itype){
    itype = ui->cb_dataouttype->currentIndex();
}

void CDeviceParmWidget::getGyroCaliParm(double &ix, double &iy, double &iz){
    int index = ui->cb_GyroDrect->currentIndex();
    //标度因数 + 轴向
    ix = ui->le_xGyroCali->text().toDouble() * m_iadirect[index][0];
    iy = ui->le_yGyroCali->text().toDouble() * m_iadirect[index][1];
    iz = ui->le_zGyroCali->text().toDouble() * m_iadirect[index][2];
}

void CDeviceParmWidget::setGyroCaliParm(double &ix, double &iy, double &iz){
    qDebug()<<"setGyroCaliParm:"<<ix<<iy<<iz;
    char index = 0;
    if(ix < 0){
        index |= 0x04;
        ix *= -1;
    }
    if(iy < 0){
        index |= 0x02;
        iy *= -1;
    }
    if(iz < 0){
        index |= 0x01;
        iz *= -1;
    }
    ui->cb_GyroDrect->setCurrentIndex(index);
    ui->le_xGyroCali->setText(QString("%1").arg(ix, 10, 'f', 10));
    ui->le_yGyroCali->setText(QString("%1").arg(iy, 10, 'f', 10));
    ui->le_zGyroCali->setText(QString("%1").arg(iz, 10, 'f', 10));
}

void CDeviceParmWidget::getAddiCaliParm(double &ix, double &iy, double &iz){
    int index = ui->cb_addiDrect->currentIndex();
    //标度因数 + 轴向
    ix = ui->le_xAddiCali->text().toDouble() * m_iadirect[index][0];
    iy = ui->le_yAddiCali->text().toDouble() * m_iadirect[index][1];
    iz = ui->le_zAddiCali->text().toDouble() * m_iadirect[index][2];
}

void CDeviceParmWidget::setAddiCaliParm(double &ix, double &iy, double &iz){
    char index = 0;
    if(ix < 0){
        index |= 0x04;
        ix *= -1;
    }
    if(iy < 0){
        index |= 0x02;
        iy *= -1;
    }
    if(iz < 0){
        index |= 0x01;
        iz *= -1;
    }
    qDebug()<<"index:"<<(int)index;
    ui->cb_addiDrect->setCurrentIndex(index);
    ui->le_xAddiCali->setText(QString("%1").arg(ix, 4, 'f', 4));
    ui->le_yAddiCali->setText(QString("%1").arg(iy, 4, 'f', 4));
    ui->le_zAddiCali->setText(QString("%1").arg(iz, 4, 'f', 4));
}

void CDeviceParmWidget::getSdCardOpParm(uint8_t &ivf, uint8_t &ivp){
    ivf = ui->cb_sdfiletype->currentIndex() + 1;
    ivp = ui->cb_foptype->currentIndex() + 1;
}

int CDeviceParmWidget::getCheckedParm(){
    return m_btGroup->checkedId();
}

