﻿#include "cotherprotocol.h"

COtherProtocol::COtherProtocol(QObject *parent) : CBaseProtocol(parent)
{

}

bool COtherProtocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead.clear();
    m_uMsgLen = -1;
    return true;
}

bool COtherProtocol::preInit(){
    CBaseProtocol::preInit();
    return true;
}

void COtherProtocol::paseMsg(const QByteArray msg){
    if(m_bIsNeedDat){
        //qDebug()<<"write dat"<<msg.toHex(' ');
        writeDatFile("other",msg);
    }
}
