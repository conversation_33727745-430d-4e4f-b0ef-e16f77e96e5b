﻿#ifndef CUSTOMGRAPHICSVIEW_H
#define CUSTOMGRAPHICSVIEW_H

#include <QObject>
#include <QGraphicsView>
#include <QGraphicsPixmapItem>

/*
*  仪表视图车体姿态子视图控件
*/
class CustomGraphicsView : public QGraphicsView
{
    Q_OBJECT
public:
    explicit CustomGraphicsView(QWidget *parent = nullptr);

    void drawBackground(QPainter *painter, const QRectF &rect) override;

    void drawBorderOut(QPainter *painter, int radius, int offset);
    void DrawSmallScale(QPainter& painter,int radius, int pointnum, int startsite , float angle);
    void DrawPointer(QPainter& painter,int radius, float degRotate, float startsite);
    void DrawDigital(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle);
    QString genAltitudeDigital(int i);
    void initStartSite(int startsite){
        m_startsite = startsite;
    }
    void setDegRotate(float degrotate){
        m_spdegRotate = degrotate;
    }

signals:
private:
    QColor borderOutColorStart; //外边框渐变开始颜色
    QColor borderOutColorEnd;   //外边框渐变结束颜色
    QColor borderInColorStart;  //里边框渐变开始颜色
    QColor borderInColorEnd;    //里边框渐变结束颜色
    float m_spdegRotate = 0.0;
    int m_startsite = 0.0;

};

#endif // CUSTOMGRAPHICSVIEW_H
