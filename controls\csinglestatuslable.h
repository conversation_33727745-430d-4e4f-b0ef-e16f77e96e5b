﻿#ifndef CSINGLESTATUSLABLE_H
#define CSINGLESTATUSLABLE_H

#include <QObject>
#include <QLabel>

/*
* 未使用
*/
class CSingleStatusLable : public QLabel
{
    Q_OBJECT
public:
    explicit CSingleStatusLable(QWidget *parent = nullptr);
    void paintEvent(QPaintEvent *) override;
    void drawText(QPainter *p);
    void drawPixmap(QPainter *p);

signals:
private:
    bool m_state;
    QPixmap  m_pixOn;
    QPixmap  m_pixOff;
    QString m_text;
    QFont m_textFont;
    QColor m_textColor;

};

#endif // CSINGLESTATUSLABLE_H
