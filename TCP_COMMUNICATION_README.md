# TCP通讯功能说明

## 概述

本项目已成功添加TCP通讯功能，现在支持串口和TCP两种通讯方式，可以实现上位机与下位机之间的正常交互。

## 主要功能

### 1. 通讯方式支持
- **串口通讯**: 原有的串口通讯功能保持不变
- **TCP通讯**: 新增TCP服务器功能，IP地址为***************

### 2. 统一接口设计
- 创建了`ICommService`通讯接口基类
- `CSseriService`串口服务类继承自该接口
- `CTcpService`TCP服务类继承自该接口
- 上层应用可以透明地使用不同的通讯方式

### 3. 新增文件说明

#### 接口文件
- `service_imp/icommservice.h` - 通讯服务接口基类定义
- `service_imp/ctcpservice.h` - TCP服务类头文件
- `service_imp/ctcpservice.cpp` - TCP服务类实现

#### UI文件
- `service_ui/ccommpage.h` - 统一通讯页面头文件
- `service_ui/ccommpage.cpp` - 统一通讯页面实现
- `service_ui/ccommpage.ui` - 统一通讯页面UI设计

#### 测试文件
- `test_tcp_client.cpp` - TCP客户端测试程序

## 使用方法

### 1. 启动TCP服务器
1. 运行主程序
2. 点击"串口设置"按钮（现在是通讯设置）
3. 在通讯类型下拉框中选择"TCP"
4. 设置TCP IP地址（默认：***************）
5. 设置TCP端口（默认：8080）
6. 点击"打开"按钮启动TCP服务器

### 2. 客户端连接
- 下位机或其他客户端可以连接到指定的IP地址和端口
- 支持多客户端同时连接
- 服务器会自动处理客户端的连接和断开

### 3. 数据交互
- 所有原有的协议处理逻辑保持不变
- 数据解析、参数配置、升级、温度补偿、标定等功能完全支持TCP通讯
- 数据格式和协议与串口通讯完全一致

## 技术特点

### 1. 架构设计
- 采用接口抽象设计，便于扩展其他通讯方式
- 保持原有代码结构，最小化修改影响
- 支持多线程处理，确保通讯稳定性

### 2. TCP服务器特性
- 支持多客户端并发连接
- 自动处理客户端连接状态
- 提供连接状态监控和错误处理
- 支持数据缓冲和流控制

### 3. 兼容性
- 完全兼容原有串口通讯功能
- 协议处理逻辑无需修改
- UI界面支持动态切换通讯方式

## 配置说明

### TCP服务器配置
- **默认IP地址**: ***************
- **默认端口**: 8080
- **支持的客户端数量**: 无限制（受系统资源限制）
- **数据传输模式**: 二进制/文本（与串口一致）

### 网络要求
- 确保防火墙允许指定端口的TCP连接
- 客户端需要能够访问服务器IP地址
- 建议在局域网环境下使用以确保稳定性

## 测试方法

### 1. 使用测试客户端
```bash
# 编译测试客户端
qmake test_tcp_client.cpp
make

# 运行测试客户端
./test_tcp_client
```

### 2. 使用网络工具
- 可以使用telnet、netcat等工具连接测试
- 例如：`telnet 192.168.************`

### 3. 功能验证
1. 启动主程序并开启TCP服务器
2. 使用客户端连接到服务器
3. 发送测试数据验证数据接收
4. 检查协议解析是否正常工作
5. 验证参数配置、升级等功能

## 故障排除

### 常见问题
1. **TCP服务器启动失败**
   - 检查IP地址是否正确
   - 确认端口未被其他程序占用
   - 检查防火墙设置

2. **客户端连接失败**
   - 验证网络连通性
   - 确认IP地址和端口配置正确
   - 检查服务器是否正在运行

3. **数据传输异常**
   - 检查数据格式是否正确
   - 验证协议头是否匹配
   - 确认网络稳定性

### 调试信息
- 程序会在控制台输出详细的调试信息
- 包括连接状态、数据收发、错误信息等
- 可以通过日志分析问题原因

## 扩展说明

### 添加其他通讯方式
1. 继承`ICommService`接口
2. 实现所有虚函数
3. 在`CCommPage`中添加对应的创建逻辑
4. 更新UI界面支持新的通讯类型

### 协议扩展
- 现有协议处理框架完全支持TCP通讯
- 可以在不修改通讯层的情况下添加新协议
- 协议工厂模式支持动态协议选择

## 总结

TCP通讯功能的添加实现了项目的扩展目标，在保持原有功能完整性的同时，提供了更灵活的通讯选择。通过统一的接口设计，确保了代码的可维护性和可扩展性。
