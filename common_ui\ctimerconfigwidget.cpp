﻿#include "ctimerconfigwidget.h"
#include "ui_ctimerconfigwidget.h"

CTimerConfigWidget::CTimerConfigWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CTimerConfigWidget)
{
    ui->setupUi(this);
    //只要修改时间，则表示需要使用定时采集
    connect(ui->te_ToutTime, &QTimeEdit::timeChanged, this, [=](){
       ui->cb_useTimer->setChecked(true);
    });
}

CTimerConfigWidget::~CTimerConfigWidget()
{
    delete ui;
}

QMap<QString,QString>& CTimerConfigWidget::getTimerParams(){
    if(ui->cb_useTimer->isChecked()){
        //qDebug()<<QString::number(ui->te_ToutTime->time().msecsSinceStartOfDay());
        m_mParamsMap["TIMEOUT"] = QString::number(ui->te_ToutTime->time().msecsSinceStartOfDay());
        m_mParamsMap["ROUND"] = ui->sb_round->text();
        m_mParamsMap["INTVTIME"] = ui->sb_intvtime->text();
    }else{
        m_mParamsMap["TIMEOUT"] = "0";
    }
    return m_mParamsMap;
}
