﻿#include "caa5500protocol.h"
#include <QDebug>
#include "cdritestpage.h"
#include "crevoctrpage.h"
#include "csubpagemanager.h"

Caa5500Protocol::Caa5500Protocol(QObject *parent) : CBaseProtocol(parent)
{
   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);
}

Caa5500Protocol::~Caa5500Protocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool Caa5500Protocol::setProtoLength(const QByteArray &barr){
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CRevoCtrPage *revpage = static_cast<CRevoCtrPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_RevoCtr"));
    if(dripage != NULL){
       qDebug()<<"connect Caa5500Protocol";
       connect(this, &Caa5500Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
    }
    //若是长度不够，返回报文长度唯一，重新查找报文
    if(barr.size() < 5){
        m_uMsgLen = 1;
        return false;
    }
    unsigned char type = barr.at(4);
    qDebug()<<"type:"<<barr.at(4)<<type;
    switch (type) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
        qDebug()<<"type 6";
        m_bMsgHead = barr.mid(0, 4);
        m_uMsgLen = barr.at(2) + (barr.at(3) << 8);
        connect(this, &Caa5500Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        break;
    case 7:
    case 8:
    case 9:
    case 10:
        m_bMsgHead = barr.mid(0, 4);
        m_uMsgLen = barr.at(2) + (barr.at(3) << 8);
        break;
    case 160:
        m_bMsgHead = barr.mid(0, 2);
        m_uMsgLen = barr.at(2);
        connect(this, &Caa5500Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &Caa5500Protocol::sigDataUpdate, revpage, &CRevoCtrPage::slotDataShow);
        break;
    default:
        break;
    }
    qDebug()<<"setProtoLength:"<<m_uMsgLen<<m_bMsgHead.toHex(' ');

    if(m_uMsgLen > 256){
        return false;
    }

    return true;
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(4)<<barr.at(5);
}

void Caa5500Protocol::paseMsg(const QByteArray msg){
    QStringList dataValues;
    unsigned char *dataParse = NULL;
    unsigned char type = msg[4];
    //qDebug()<<"type:"<<type;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < m_uMsgLen){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    bool isLoss = false;
    bool isCheckFail = false;
    switch (type) {
    case 1:
        dataValues = typeOneDataCalc(msg.data());
        break;
    case 2:
        dataValues = typeTwoDataCalc(msg.data());
        break;
    case 3:
        dataValues = typeThreeDataCalc(msg.data());
        break;
    case 4:
        dataValues = typeFourDataCalc(msg.data());
        break;
    case 5:
        dataValues = typeFiveDataCalc(msg.data());
        break;
    case 6:
        dataValues = typeSixDataCalc(msg, isLoss, isCheckFail);
        break;
    case 7:
        dataValues = typeSevenDataCalc(msg.data());
        break;
    case 8:
        dataValues = typeEightDataCalc(msg.data());
        break;
    case 9:
    case 10:
        dataValues = typeNineDataCalc(msg.data());
        break;
    case 160:
        m_bNeedWrite = false;
        dataValues = typeA0DataCalc(msg.data());
    default:
        break;
    }
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;

    if(isLoss){
        m_iLossFrameSum++;
        setFrameErr(E_FRAME_LOSS);
        if(m_bIsNeedShowL){
            emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") + QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
            m_bIsNeedShowL = false;
        }
    }

    if(isCheckFail){
        qDebug()<<QTime::currentTime().toString()<<"sum8CheckSum error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    //转台数据无需写文件
    if(m_bNeedWrite){
        writeCvsFile("AA55", m_slProtoKeys, dataValues);
    }

    if(m_bIsNeedShow){
        //qDebug()<<"m_slProtoKeys:"<<m_slProtoKeys.size()<<dataValues.size()<<m_bIsNeedKeys<<dataValues.at(0);
        if(m_slProtoKeys.size() != dataValues.size()){
            return;
        }
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        if(m_bIsNeedKeys){
            emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        }else{
            emit sigDataUpdate(QStringList(), dataValues, m_sPortN, m_iProtoIndex);
        }
        m_bIsNeedShow = false;
    }

}

QStringList Caa5500Protocol::typeOneDataCalc(const char *data){
    Struaa5501 *dataParse = new Struaa5501;
    ::memcpy(dataParse, data, m_uMsgLen);
    Struaa5501 *st_aa5501 = (Struaa5501 *)data;
    double ress_kpa = 0.0;
    double temperature = 0.0;
    double x = 0, y = 0, z = 0;
    double gyro_x = 0, gyro_y = 0, gyro_z = 0;
    for (int j = 0; j < IMU_ONEDATACOUNT; j++) {
        ress_kpa += st_aa5501->imudata.onedata[j].temp_due;
        temperature += st_aa5501->imudata.onedata[j].temp_uno;
        x += st_aa5501->imudata.onedata[j].acc_x;
        y += st_aa5501->imudata.onedata[j].acc_y;
        z += st_aa5501->imudata.onedata[j].acc_z;

        gyro_x += st_aa5501->imudata.onedata[j].gyro_x;
        gyro_y += st_aa5501->imudata.onedata[j].gyro_y;
        gyro_z += st_aa5501->imudata.onedata[j].gyro_z;
    }

    ress_kpa = ress_kpa / IMU_ONEDATACOUNT;
    temperature = temperature / IMU_ONEDATACOUNT;
    x /= IMU_ONEDATACOUNT;
    y /= IMU_ONEDATACOUNT;
    z /= IMU_ONEDATACOUNT;
    gyro_x /= IMU_ONEDATACOUNT;
    gyro_y /= IMU_ONEDATACOUNT;
    gyro_z /= IMU_ONEDATACOUNT;

    double acc_x_comp, acc_y_comp, acc_z_comp;
    double gyro_x_comp, gyro_y_comp, gyro_z_comp;
    double acc_x = x / 4905.0, acc_y = y / 4905.0, acc_z = z / 4905.0;
    double gyro_x1 = gyro_x / 160.0, gyro_y1 = gyro_y / 160.0, gyro_z1 = gyro_z / 80.0;

    QStringList values;
    return values;

}

QStringList Caa5500Protocol::typeTwoDataCalc(const char *data){
    Struaa5502 *dataParse = new Struaa5502;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;

}
QStringList Caa5500Protocol::typeThreeDataCalc(const char *data){
    Struaa5503 *dataParse = new Struaa5503;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}
QStringList Caa5500Protocol::typeFourDataCalc(const char *data){
    Struaa5504 *dataParse = new Struaa5504;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}
QStringList Caa5500Protocol::typeFiveDataCalc(const char *data){
    Struaa5505 *dataParse = new Struaa5505;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}
QStringList Caa5500Protocol::typeSixDataCalc(QByteArray msg, bool &isLoss, bool &isCheckFail){
    Struaa5506 *dataParse = new Struaa5506;
    ::memcpy(dataParse, msg.data(), m_uMsgLen);

    unsigned char calsum = CTlhTools::sum8CheckSum(msg, 0, 2);
    unsigned char checksum = msg.data()[msg.size() - 2];
    if(calsum != checksum){
        qDebug()<<"typeSixDataCalc check sum:"<<calsum<<checksum;
        isCheckFail = true;
    }

    //qDebug()<<"packnum:"<<dataParse->packetT;
    if(m_iFramIndx != -1 && (m_iFramIndx + 1 != dataParse->packetT)){
        qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN;
        qDebug()<< m_sMsgCache.toHex();
        qDebug()<< msg.toHex();

        isLoss = true;
        m_sMsgCache.clear();
        m_sMsgCache.append(msg);
    }
    m_iFramIndx = dataParse->packetT;

    QStringList lvalues;
    if(m_slProtoKeys.size() == 0){
        //m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("计数:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("gps周:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("gps秒:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("pps_en:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("gps是否有效:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("pps延迟:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("陀螺X:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("陀螺Y:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("陀螺Z:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("温度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("加计X:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("加计Y:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("加计Z:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("经度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("纬度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("高度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("东向速度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("北向速度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("天向速度:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("俯仰:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("横滚:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("航向:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("定位状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("rtk状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("卫星数:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("can计数:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("左前轮速:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("右前轮速:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("左后轮速:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("右后轮速:"));
        m_slProtoKeys.append("wheelSteer:");
        m_slProtoKeys.append("0doPulse_1:");
        m_slProtoKeys.append("0doPulse_2:");
        m_slProtoKeys.append("Gear:");
        m_slProtoKeys.append("Standard_flag:");
        m_slProtoKeys.append(QString::fromLocal8Bit("天线安装角0:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("天线安装角1:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("天线安装角2:"));
        m_slProtoKeys.append("acc_off_0:");
        m_slProtoKeys.append("acc_off_1:");
        m_slProtoKeys.append("acc_off_2:");
        m_slProtoKeys.append("gyro_off_0:");
        m_slProtoKeys.append("ayro_off_1:");
        m_slProtoKeys.append("ayro_off_2:");
        m_slProtoKeys.append("trackTrue:");
        m_slProtoKeys.append("nav_status:");
        m_slProtoKeys.append("nav_standardF:");
        m_slProtoKeys.append("imuSelect:");
        m_slProtoKeys.append("memsType:");
        m_slProtoKeys.append("use_gps_flag:");
        m_slProtoKeys.append("fusion_source:");
        m_slProtoKeys.append(QString::fromLocal8Bit("航向状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("gps秒_982:"));
        m_slProtoKeys.append("fog0:");
        m_slProtoKeys.append("alg_Lon:");
        m_slProtoKeys.append("alg_Lat:");
        m_slProtoKeys.append("alg_Alt:");
        m_slProtoKeys.append("alg_ve:");
        m_slProtoKeys.append("alg_vn:");
        m_slProtoKeys.append("alg_vu:");
        m_slProtoKeys.append("alg_Pitch:");
        m_slProtoKeys.append("alg_Roll:");
        m_slProtoKeys.append("alg Heading:");
    }

    //lvalues.append(QString::number(dataParse->packetT));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_counter));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_gpsweek));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_gpssecond));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_pps_en));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_gps_valid));
    lvalues.append(QString::number(dataParse->gdwdata.ppsDelay));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_gyroX, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_gyroY, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_gyroZ, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_sensor_temp));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_accelY, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_accelX, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.navi_test_info_accelZ, 'f', 6));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Lon, 'f', 8));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Lat, 'f', 8));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Altitude, 'f', 4));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_ve));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_vn));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_vu));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Pitch));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Roll));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_Heading));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_PositioningState));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_rtkStatus));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_StarNum));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_counter));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_WheelSpeed_Front_Left));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_WheelSpeed_Front_Right));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_WheelSpeed_Back_Left));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_WheelSpeed_Back_Right));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_WheelSteer));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_OdoPulse_1));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_OdoPulse_2));
    lvalues.append(QString::number(dataParse->gdwdata.canInfo_data_Gear));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_Nav_Standard_flag));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gnssAtt_from_vehicle2_0));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gnssAtt_from_vehicle2_1));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gnssAtt_from_vehicle2_2));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_acc_off_0));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_acc_off_1));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_acc_off_2));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gyro_off_0));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gyro_off_1));
    lvalues.append(QString::number(dataParse->gdwdata.Adj_gyro_off_2));
    lvalues.append(QString::number(dataParse->gdwdata.gnss_trackTrue));
    lvalues.append(QString::number(dataParse->gdwdata.result_Nav_Status));
    lvalues.append(QString::number(dataParse->gdwdata.result_Nav_Standard_flag));
    lvalues.append(QString::number(dataParse->gdwdata.result_imuSelect));
    lvalues.append(QString::number(dataParse->gdwdata.result_memsType));
    lvalues.append(QString::number(dataParse->gdwdata.result_use_gps_flag));
    lvalues.append(QString::number(dataParse->gdwdata.result_fusion_source));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_headingStatus));
    lvalues.append(QString::number(dataParse->gdwdata.gnssInfo_gpssecond982));
    lvalues.append(QString::number(dataParse->gdwdata.fog0));
    lvalues.append(QString::number(dataParse->gdwdata.longitude, 'f', 8));
    lvalues.append(QString::number(dataParse->gdwdata.latitude, 'f', 8));
    lvalues.append(QString::number(dataParse->gdwdata.altitude, 'f', 4));
    lvalues.append(QString::number(dataParse->gdwdata.ve));
    lvalues.append(QString::number(dataParse->gdwdata.vn));
    lvalues.append(QString::number(dataParse->gdwdata.vu));
    lvalues.append(QString::number(dataParse->gdwdata.Pitch));
    lvalues.append(QString::number(dataParse->gdwdata.Roll));
    lvalues.append(QString::number(dataParse->gdwdata.Heading));

    return lvalues;
}
QStringList Caa5500Protocol::typeSevenDataCalc(const char *data){
    Struaa5507 *dataParse = new Struaa5507;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}
QStringList Caa5500Protocol::typeEightDataCalc(const char *data){
    Struaa5508 *dataParse = new Struaa5508;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}
QStringList Caa5500Protocol::typeNineDataCalc(const char *data){
    Struaa5509 *dataParse = new Struaa5509;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    return values;
}

QStringList Caa5500Protocol::typeA0DataCalc(const char *data){
    //qDebug()<<"typeA0DataCalc";
    Struaa55a0 *dataParse = new Struaa55a0;
    ::memcpy(dataParse, data, m_uMsgLen);
    QStringList values;
    if(m_slProtoKeys.size() == 0){
        m_slProtoKeys.clear();
        m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("温箱门:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("强电上电:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("授权状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴位置校验:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴位置校验:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("日期时间:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴运动状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴使能:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴到位:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴正常:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴锁紧:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴超上限:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴超下限:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴超速:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴超差:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴驱动故障:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴找零:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴电压超限:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴位置:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("内轴速率:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴运动状态:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴使能:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴到位:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴正常:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴锁紧:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴超上限:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴超下线:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴超速:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴超差:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴驱动故障:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴找零:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴电压超限:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴位置:"));
        m_slProtoKeys.append(QString::fromLocal8Bit("外轴速率:"));
    }

    //1-帧序号
    values.append(QString::number(dataParse->packetn));
    //2-温箱门状态
    if((dataParse->status & 0x01) == 1){
        values.append(QString::fromLocal8Bit("关"));
    }else{
        values.append(QString::fromLocal8Bit("开"));
    }

    //3-强电上电状态
    if((dataParse->status & 0x02) >> 1 == 1){
        values.append(QString::fromLocal8Bit("关"));
    }else{
        values.append(QString::fromLocal8Bit("开"));
    }

    //4-授权状态
    if((dataParse->status & 0x80) >> 7 == 1){
        values.append(QString::fromLocal8Bit("成功"));
    }else{
        values.append(QString::fromLocal8Bit("无授权"));
    }

    //5-内轴位置校验
    if((dataParse->exstatus & 0x01) == 1){
        values.append(QString::fromLocal8Bit("错误"));
    }else{
        values.append(QString::fromLocal8Bit("正常"));
    }

    //6-外轴位置校验
    if((dataParse->exstatus & 0x04) >> 3 == 1){
        values.append(QString::fromLocal8Bit("错误"));
    }else{
        values.append(QString::fromLocal8Bit("正常"));
    }

    //7-日期时间
    values.append(QString("%1:%2:%3").arg(dataParse->hour).arg(dataParse->minutes).arg(dataParse->second));

    // 处理低字节
    //8-内轴运动状态
    //values.append(QString::number(dataParse->inershaftstlow & 0xF8));
    values.append(getStatString(dataParse->inershaftstlow & 0x1F));
    //values.append(QString::number(dataParse->inershaftstlow, 16));
    //9-内轴使能状态
    //qDebug()<<"inershaftstlow"<<dataParse->inershaftstlow<<dataParse->inershaftsthigh;
    if((dataParse->inershaftstlow & 0x20) >> 5 == 1){
        values.append(QString::fromLocal8Bit("断开"));    //是
    }else{
        values.append(QString::fromLocal8Bit("使能"));
    }

    //10-内轴到位状态
    if((dataParse->inershaftstlow & 0x40) >> 6 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //11-内轴是否正常状态
    if((dataParse->inershaftstlow & 0x80) >> 7 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //处理高字节
    //12-内轴锁紧状态
    if((dataParse->inershaftsthigh & 0x01) == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //13-内轴上限状态
    if((dataParse->inershaftsthigh & 0x02) >> 1 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //14-内轴下限状态
    if((dataParse->inershaftsthigh & 0x04) >> 2 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //15-内轴超速状态
    if((dataParse->inershaftsthigh & 0x08) >> 3 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //16-内轴超差状态
    if((dataParse->inershaftsthigh & 0x10) >> 4 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //17-内轴驱动故障状态
    if((dataParse->inershaftsthigh & 0x20) >> 5 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //18-内轴找零状态
    if((dataParse->inershaftsthigh & 0x40) >> 6 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //19-内轴电压超限状态
    if((dataParse->inershaftsthigh & 0x80) >> 7 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //20-内轴位置
    values.append(QString::number(dataParse->inersit, 'f', 4));
    //21-内轴速率
    values.append(QString::number(dataParse->inerspeed, 'f', 4));

    // 处理低字节
    //22-运动状态
    values.append(getStatString(dataParse->outershaftstlow & 0x1F));
    //23-内轴使能状态
    if((dataParse->outershaftstlow & 0x20) >> 5 == 1){
        values.append(QString::fromLocal8Bit("断开"));    //是
    }else{
        values.append(QString::fromLocal8Bit("使能"));
    }

    //24-内轴到位状态
    if((dataParse->outershaftstlow & 0x40) >> 6 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //25-内轴是否正常状态
    if((dataParse->outershaftstlow & 0x80) >> 7 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //处理高字节
    //26-内轴锁紧状态
    if((dataParse->outershaftsthigh & 0x01) == 1){
        values.append("#FF0000");
    }else{

        values.append("#228B22");
    }

    //27-内轴上限状态
    if((dataParse->outershaftsthigh & 0x02) >> 1 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //28-内轴下限状态
    if((dataParse->outershaftsthigh & 0x04) >> 2 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //29-外轴超速状态
    if((dataParse->outershaftsthigh & 0x08) >> 3 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //30-外轴超差状态
    if((dataParse->outershaftsthigh & 0x10) >> 4 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //31-外轴驱动故障状态
    if((dataParse->outershaftsthigh & 0x20) >> 5 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //32-外轴找零状态
    if((dataParse->outershaftsthigh & 0x40) >> 6 == 1){
        values.append("#228B22");
    }else{
        values.append("#FF0000");
    }

    //33-外轴电压超限状态
    if((dataParse->outershaftsthigh & 0x80) >> 7 == 1){
        values.append("#FF0000");
    }else{
        values.append("#228B22");
    }

    //34-外轴位置
    values.append(QString::number(dataParse->outersit, 'f', 4));
    //35-外轴速率
    values.append(QString::number(dataParse->outerspeed, 'f', 4));

    return values;
}

QString Caa5500Protocol::getStatString(int istat){
    switch (istat) {
    case 0:
        return QString::fromLocal8Bit("空闲");
    case 2:
        return QString::fromLocal8Bit("静止");
    case 3:
        return QString::fromLocal8Bit("位置");
    case 4:
        return QString::fromLocal8Bit("速率");
    case 5:
        return QString::fromLocal8Bit("摇摆");
    case 6:
        return QString::fromLocal8Bit("找零");
    case 8:
        return QString::fromLocal8Bit("相对位置");
    case 20:
        return QString::fromLocal8Bit("停车");
    default:
        return QString::fromLocal8Bit("空闲");
    }
}
