﻿#ifndef CTIMERCONFIGWIDGET_H
#define CTIMERCONFIGWIDGET_H

#include <QWidget>
#include <QMap>

namespace Ui {
class CTimerConfigWidget;
}

/*
*定时设置子页面
*/
class CTimerConfigWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CTimerConfigWidget(QWidget *parent = nullptr);
    ~CTimerConfigWidget();
    QMap<QString,QString>& getTimerParams();

private:
    Ui::CTimerConfigWidget *ui;
    QMap<QString, QString> m_mParamsMap;
};

#endif // CTIMERCONFIGWIDGET_H
