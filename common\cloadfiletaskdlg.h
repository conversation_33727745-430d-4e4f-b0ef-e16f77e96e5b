﻿#ifndef CLOADFILETASK_H
#define CLOADFILETASK_H

#include <QObject>
#include <QThread>
#include <QProgressBar>
#include <QDialog>
#include <QDebug>
#include <QProgressDialog>

#define SHENZHEN_LT 22.697   //深圳纬度


/*
*文件加载任务类
*/
class CLoadFileTask:public QObject{

    Q_OBJECT
public:
    CLoadFileTask(QObject *parent = nullptr) : QObject(parent){
        m_bStop = false;
    }
    void stopLoad(){
        m_bStop = true;
    }
public slots:
    /*取单个文件中指定字段的值*/
    void slotLoadCvsFile(const QString &filename, const QVector<int> vindex, QVector<QList<double> *> dlist);
    /*取单个文件中指定字段的平均值*/
    void slotLoadFileAvg(const QString &filename,const QVector<int> vindex, int ifrq, QVector<QList<double> *> dlist);
    /*取多个个文件中同一个字段的平均值*/
    void slotLoadMulFileAvg(const QStringList &filenamelist,const int dataindex, QList<QString> *vdspeeds, \
                         QList<double> *vddata, QList<QString> *vsFilename);
signals:
    /*数据加载进度条更新信号*/
    void sigUpgValue(int value);
private:
    bool m_bStop;
};

class CCalculateTask:public QObject{

    Q_OBJECT
public:
    CCalculateTask(QObject *parent = nullptr) : QObject(parent){
        m_bStop = false;
    }
    void stopLoad(){
        m_bStop = true;
    }
public slots:
    //陀螺仪标度因数相关计算
    void slotBiasRelation(QVector<double> &w, QVector<double> &F_plus, QVector<double> &F_minus, double &dreslut);
    //陀螺仪零偏因相关计算
    void slotBiasRelation(QVector<double> &w, double K,double &dreslut);
    //陀螺仪标定函数计算
    void slotGyroCali();
    //加速度计标定函数计算
    void slotAccCali();
signals:
    void sigUpgValue(int value);
private:
    bool m_bStop;
};

/*文件加载对话框*/
class CLoadFileTaskDlg : public QDialog
{
    Q_OBJECT
public:
    explicit CLoadFileTaskDlg(QWidget *parent = nullptr);
    ~CLoadFileTaskDlg(){
    }
    /*加载文件*/
    void loadFileDetail(const QString &filename,const QVector<int> vindex, QVector<QList<double> *> strlist){
        qDebug()<<"loadFileDetail";
        emit sigLoadCvsFile(filename, vindex, strlist);
    }
    /*加载文件*/
    void loadFileDetail(const QString &filename,const int index, QList<double> *dlist){
        qDebug()<<"loadFileDetail";
        QVector<int> vi;
        vi.append(index);
        QVector<QList<double> *> vqd;
        vqd.append(dlist);
        emit sigLoadCvsFile(filename, vi, vqd);
    }
    /*加载文件并计算平均值*/
    void loadFileDetailAvg(const QString &filename,const QVector<int> vindex, int ifrq, QVector<QList<double> *> dlist){
        emit sigLoadFileAvg(filename, vindex, ifrq, dlist);
    }

    /*加载文件并计算平均值*/
    void loadFileMulDetailAvg(const QStringList &filenamelist,const int dataindex, QList<QString> *vdspeeds, QList<double> *vddata, QList<QString> *vsFilename){
        qDebug()<<"loadFileMulDetail";
        emit sigLoadMulFileAvg(filenamelist, dataindex, vdspeeds, vddata, vsFilename);
    }

private:
    QThread *m_thloadthread;
    QProgressBar *m_pgLoadProc;
    CLoadFileTask *m_LoadTask;

signals:
    /*加载文件信号*/
    void sigLoadCvsFile(const QString &filename,const QVector<int> vindex, QVector<QList<double> *> dlist);
    /*加载多个文件信号*/
    void sigLoadMulFileAvg(const QStringList &filenamelist,const int dataindex, QList<QString> *vdspeeds, QList<double> *vddata, QList<QString> *vsFilename);
    /*加载文件 并计算平均值信号*/
    void sigLoadFileAvg(const QString &filename,const QVector<int> vindex, int ifrq, QVector<QList<double> *> dlist);
    /*文件读取结束信号*/
    void sigReadFileEnd();

};

#endif // CLOADFILETASK_H
