#include "csubpagemanager.h"

CSubPageManager *CSubPageManager::m_Instance = NULL;

CSubPageManager::CSubPageManager(QObject *parent) : QObject(parent)
{

}

CSubPageManager *CSubPageManager::GetInstance(){
    if(m_Instance == NULL){
        m_Instance = new CSubPageManager();
    }

    return m_Instance;

}

void CSubPageManager::RegisterPage( QString pageName,QWidget *pageObj){
    if(!m_PageMager.contains(pageName)){
         m_PageMager[pageName] = pageObj;
    }
}

QWidget* CSubPageManager::GetObjectbyName( QString pageName){
    if(m_PageMager.contains(pageName)){
        return m_PageMager[pageName];
    }else{
        return NULL;
    }

}
