﻿#ifndef TLHV1WINDOW_H
#define TLHV1WINDOW_H

#include <QMainWindow>
#include "ccserialpage.h"
#include "cprotoparamdialog.h"
#include "ccollectrecorddialog.h"
#include <QMap>
#include "caboutdlg.h"
#include <QTimer>

QT_BEGIN_NAMESPACE
namespace Ui { class TlhV1Window; }
QT_END_NAMESPACE


/*
*主窗口类
*/
class TlhV1Window : public QMainWindow
{
    Q_OBJECT

public:
    TlhV1Window(QWidget *parent = nullptr);
    ~TlhV1Window();
    void showEvent(QShowEvent *e);
    /*重启程序*/
    void restartApplication();

private slots:
    void on_bt_mSeriOpen_clicked();

    void on_toolButton_clicked();

    void slotPortChage(const QString &tport);

    void on_bt_mParamset_clicked();

    void on_bt_mSerialSet_clicked();
    void on_bt_collectrecord_clicked();

    void slotShowStatus(int showtype, QStringList key, QStringList val);

    void on_action_1_triggered();

    void on_action_2_triggered();

    void on_action_5_triggered();

    void on_about_triggered();

    void on_action_7_triggered();

    void on_action_6_triggered();

    void on_ac_viewmode_triggered();

    void on_action_8_triggered();

    void on_action_triggered();

    void on_action_3_triggered();

    void on_actionFile_Tools_triggered();

    void on_actionMessage_Tools_triggered();

    void on_actionCalibration_Tool_triggered();

public slots:
    void slotSetUartP(const QString &tport, const QString tbaud, const bool opstat);

private:
    Ui::TlhV1Window *ui;
    CcserialPage * m_seriPage;
    CProtoParamDialog *m_paramDlg;
    CCollectRecordDialog *m_recordDlg;
    CAboutDlg *m_abDlog;
    QString m_lang;
    QString m_worker;
};
#endif // TLHV1WINDOW_H
