#ifndef ICOMMSERVICE_H
#define ICOMMSERVICE_H

#include <QObject>
#include <QByteArray>
#include "cprotoparamdata.h"

/*
* 通讯服务接口基类
*/
class ICommService : public QObject
{
    Q_OBJECT
public:
    explicit ICommService(QObject *parent = nullptr);
    virtual ~ICommService();
    
    // 纯虚函数，子类必须实现
    virtual bool CommInit(const QString &config1, const QString &config2, const QString &config3,
                         const QString &config4, const QString &config5, const QString &config6, 
                         CProtoParamData *parm) = 0;
    virtual void commWriteData() = 0;
    virtual void commClose() = 0;
    virtual bool commWrite(QByteArray arr) = 0;
    virtual void editWrite(QByteArray arr) = 0;
    virtual bool isWorking() = 0;
    virtual QByteArray getCurrentBuff() = 0;
    virtual void clearBuff() = 0;
    virtual void startTimeWork() = 0;
    virtual bool startWork() = 0;
    virtual void stopWork() = 0;
    virtual void setUpdateMode(bool mode = true) = 0;
    virtual QByteArray doParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev) = 0;

public slots:
    virtual void slotCommOpenOrClose(const QString config, const int optype, const QString fileprefix) = 0;
    virtual void commReadData(void) = 0;
    virtual void slotCommWrite(const char * arr, int &len) = 0;
    virtual void slotStartUpdate(QString filename) = 0;

signals:
    void sigDataRead(const QByteArray &barr);
    void sigUpdateProgress(int progress);
    void sigUpdateFinish(bool success, QString message);
};

#endif // ICOMMSERVICE_H
