﻿#ifndef CRESOLVINGTASK_H
#define CRESOLVINGTASK_H

#include <QObject>
#include <QThread>
#include "cbaseprotocol.h"
#include <QMutex>
#include <QMap>
#include <QDebug>

#define MIN_LENGTH 10

/*
*串口解析线程类
*/
class CResolvingTask : public QThread
{
    Q_OBJECT
public:
    explicit CResolvingTask(QObject *parent = nullptr);
    ~CResolvingTask();
    void initProtocol(const QByteArray &barr);

    void run() override;
    void clear(bool filesave);
    void setPortN(QString portn);
    void clearBuff();
    /*设置文件前缀*/
    void setFilePrefix(QString prefixName){
        qDebug()<<"setFilePrefix:"<<prefixName;
        if(m_bprotocol != NULL && !prefixName.isEmpty()){
            QStringList tempty;
            m_bprotocol->filePrefixChange(prefixName, tempty);
        }
    }

public slots:
    void slotAppendData(const QByteArray &barr);
    void slogParamChange(const QMap<QString, QString> &param);
private:
    QThread *m_TaskThread;
    QByteArrayList m_bProtocolHead;
    QStringList m_sProtocolHead;
    CBaseProtocol *m_bprotocol;
    bool m_bProtoIsInit;
    QByteArray m_RecvData;
    QByteArray m_doingData;
    QMutex *m_mArrLock;
    int m_iStartPos;
    QString m_sPortN;
    QMap<QString, QString> m_mPParamMap;
    bool m_bExit;
};

#endif // CRESOLVINGTASK_H
