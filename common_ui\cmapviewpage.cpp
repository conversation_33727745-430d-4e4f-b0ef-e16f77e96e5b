﻿#include "cmapviewpage.h"
#include "ui_cmapviewpage.h"
#include <QGeoPositionInfoSource>
#include <QGeoPositionInfo>

CMapViewPage::CMapViewPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CMapViewPage)
{
    ui->setupUi(this);
    qRegisterMetaType<ImageInfo>("ImageInfo");

    m_geturl = NULL;
    m_mapview = NULL;

    initMap();
}

CMapViewPage::~CMapViewPage()
{
    delete ui;
    if(m_geturl != NULL){
        delete m_geturl;
    }
}

void CMapViewPage::showmapview(int id, double lng, double lat, float process){
    if(m_mapview != NULL){
        m_mapview->drawLine(id, QPointF(lng, lat), process);
    }
}

void CMapViewPage::resizeEvent(QResizeEvent *event){
    //在这里初始化可能会出现闪退的情况，应该是地图尚未初始化完成，但是已经有数据进来导致，改为到构造函数中初始化
    initMap();
}

void CMapViewPage::initMap(){
    if(m_geturl == NULL){
        m_geturl = new GetUrl();
        m_getoverurl = new GetUrl();
        m_geturl->urlinit(0, 1);
        m_getoverurl->urlinit(0, 0);
        m_mapview = dynamic_cast<MapGraphicsView *>(ui->graphicsView);
    }
}

