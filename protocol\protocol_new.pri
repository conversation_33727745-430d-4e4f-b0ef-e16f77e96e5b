HEADERS += \
    $$PWD/c3a0100protocol.h \
    $$PWD/c3a0200protocol.h \
    $$PWD/c3b020bprotocol.h \
    $$PWD/c4a0100protocol.h \
    $$PWD/c4a020a01protocol.h \
    $$PWD/c4a020b01protocol.h \
    $$PWD/c55aa00protocol.h \
    $$PWD/ca5a500protocol.h \
    $$PWD/ca6a600protocol.h \
    $$PWD/caa5500protocol.h \
    $$PWD/caa6600protocol.h \
    $$PWD/cb55b00protocol.h \
    $$PWD/cbaseprotocol.h \
    $$PWD/cbb0000protocol.h \
    $$PWD/cbb11dbbdProtocol.h \
    $$PWD/cbddb0bprotocol.h \
    $$PWD/cbddb1bprotocol.h \
    $$PWD/ccddc0bprotocol.h \
    $$PWD/cotherprotocol.h

SOURCES += \
    $$PWD/c3a0100protocol.cpp \
    $$PWD/c3a0200protocol.cpp \
    $$PWD/c3b020bprotocol.cpp \
    $$PWD/c4a0100protocol.cpp \
    $$PWD/c4a020a01protocol.cpp \
    $$PWD/c4a020b01protocol.cpp \
    $$PWD/c55aa00protocol.cpp \
    $$PWD/ca5a500protocol.cpp \
    $$PWD/ca6a600protocol.cpp \
    $$PWD/caa5500protocol.cpp \
    $$PWD/caa6600protocol.cpp \
    $$PWD/cb55b00protocol.cpp \
    $$PWD/cbaseprotocol.cpp \
    $$PWD/cbb0000protocol.cpp \
    $$PWD/cbb11dbbdProtocol.cpp \
    $$PWD/cbddb0bprotocol.cpp \
    $$PWD/cbddb1bprotocol.cpp \
    $$PWD/ccddc0bprotocol.cpp \
    $$PWD/cotherprotocol.cpp
