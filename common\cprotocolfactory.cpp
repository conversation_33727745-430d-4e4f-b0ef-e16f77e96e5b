﻿#include "cprotocolfactory.h"
#include "c3a0100protocol.h"
#include "c4a0100protocol.h"
#include "caa5500protocol.h"
#include "caa6600protocol.h"
#include "cbddb0bprotocol.h"
#include "cbddb1bprotocol.h"
#include "cbb0000protocol.h"
#include "ca5a500protocol.h"
#include "ca6a600protocol.h"
#include "cbb11dbbdProtocol.h"
#include "cb55b00protocol.h"
#include "c4a020a01protocol.h"
#include "c4a020b01protocol.h"
#include "c3b020bprotocol.h"
#include "ccddc0bprotocol.h"
#include "cotherprotocol.h"

CProtocolFactory::CProtocolFactory(QObject *parent) : QObject(parent)
{

}

CBaseProtocol *CProtocolFactory::getProtocol(E_HEADPROTO index){
    switch (index) {
    case E_AA55:
        return new Caa5500Protocol;
    case E_AA33:
        return NULL;
    case E_A5A5:
        return new CA5A500Protocol;
    case E_A6A6:
        return new CA6A600Protocol;
    case E_AA66:
        return new Caa6600Protocol;
    case E_4A0100:
        return new C4a0100Protocol;
    case E_4A020A:
        return new C4A020A01Protocol;
    case E_4A020B:
        return new C4A020B01Protocol;
    case E_3A0100:
        return new C3a0100Protocol;
    case E_3A020B:
        return new C3B020BProtocol;
    case E_CDDC0B:
        return new CCDDC0BProtocol;
    case E_BDDB0B:
        return new Cbddb0bProtocol;
    case E_BDDB1B:
        return new Cbddb1bProtocol;
    case E_BCCB0B:
        return NULL;
    case E_A55A:
        return NULL;
    case E_55AA:
        return NULL;
    case E_BB00:
        return new Cbb0000Protocol;
    case E_BB11:
        return new cbb11dbbdProtocol;
    case E_B55B:
        return new Cb55b00Protocol;
    default:
        return NULL;
    }
    return NULL;
}

CBaseProtocol *CProtocolFactory::getOtherProtocol(){
    qDebug()<<"getOtherProtocol";
    return new COtherProtocol;
}

QStringList CProtocolFactory::getProKeys(QString protoname){
    int index = CBaseProtocol::getProtoIndex(protoname);
    return getProKeys(index);
}

QStringList CProtocolFactory::getProKeys(int index){
    QStringList qkeys;
    switch (index) {
    case E_AA55:
        qkeys = getAA55Keys();
        break;
    case E_AA33:
        qkeys = getAA33Keys();
        break;
    case E_A5A5:
        qkeys = getA5A5Keys();
        break;
    case E_AA66:
        qkeys = getAA66Keys();
        break;
    case E_A6A6:
        qkeys = getA6A6Keys();
        break;
    case E_4A0100:
        qkeys = get4A0100Keys();
        break;
    case E_3A0100:
        qkeys = get3A0100Keys();
        break;
    case E_3A0200:
        qkeys = get3A0200Keys();
        break;
    case E_BDDB0B:
        qkeys = getBDDB0BKeys();
        break;
    case E_BCCB0B:
        qkeys = getBCCB0BKeys();
        break;
    case E_A55A:
        qkeys = getA55AKeys();
        break;
    case E_55AA:
        qkeys = get55AAKeys();
        break;
    case E_BB00:
        qkeys = getBB00Keys();
        break;
    case E_BB11:
        qkeys = getBB11Keys();
        break;
    case E_B55B:
        qkeys = getB55BKeys();
        break;
    default:
        break;
    }
    return qkeys;
}

QStringList CProtocolFactory::getAA55Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getAA33Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getA5A5Keys(){
    QStringList skeys;
    skeys.append(QString::fromLocal8Bit("陀螺:"));
    skeys.append(QString::fromLocal8Bit("温度:"));
    skeys.append(QString::fromLocal8Bit("陀螺平均值:"));
    skeys.append(QString::fromLocal8Bit("温度平均值:"));
    return skeys;
}

QStringList CProtocolFactory::getAA66Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::get4A0100Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::get3A0100Keys(){
    QStringList skeys;
    skeys.append(QString::fromLocal8Bit("时间戳:"));
    skeys.append(QString::fromLocal8Bit("校准加速度-x:"));
    skeys.append(QString::fromLocal8Bit("校准加速度-y:"));
    skeys.append(QString::fromLocal8Bit("校准加速度-z:"));
    skeys.append(QString::fromLocal8Bit("角速度-x:"));
    skeys.append(QString::fromLocal8Bit("角速度-y:"));
    skeys.append(QString::fromLocal8Bit("角速度-z:"));
    skeys.append(QString::fromLocal8Bit("线性加速度-x:"));
    skeys.append(QString::fromLocal8Bit("线性加速度-y:"));
    skeys.append(QString::fromLocal8Bit("线性加速度-z:"));
    skeys.append(QString::fromLocal8Bit("四元数-w:"));
    skeys.append(QString::fromLocal8Bit("四元数-x:"));
    skeys.append(QString::fromLocal8Bit("四元数-y:"));
    skeys.append(QString::fromLocal8Bit("四元数-z:"));
    return skeys;
}

QStringList CProtocolFactory::getA6A6Keys(){
    QStringList skeys;
    skeys.append(QString::fromLocal8Bit("陀螺x:"));
    skeys.append(QString::fromLocal8Bit("陀螺y:"));
    skeys.append(QString::fromLocal8Bit("陀螺z:"));
    skeys.append(QString::fromLocal8Bit("加计x:"));
    skeys.append(QString::fromLocal8Bit("加计y:"));
    skeys.append(QString::fromLocal8Bit("加计z:"));
    skeys.append(QString::fromLocal8Bit("陀螺温度:"));
    skeys.append(QString::fromLocal8Bit("加计温度:"));
    skeys.append(QString::fromLocal8Bit("陀螺x平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺y平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺z平均值:"));
    skeys.append(QString::fromLocal8Bit("加计x平均值:"));
    skeys.append(QString::fromLocal8Bit("加计y平均值:"));
    skeys.append(QString::fromLocal8Bit("加计z平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺温度均值:"));
    skeys.append(QString::fromLocal8Bit("加计温度均值:"));
    return skeys;
}

QStringList CProtocolFactory::get3A0200Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getBDDB0BKeys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getBCCB0BKeys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getA55AKeys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::get55AAKeys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getBB00Keys(){
    QStringList skeys;
    skeys.append(tr("Frame_num"));
    skeys.append(tr("FPGA_version"));
    skeys.append(tr("WATCH_version"));
    skeys.append("X" + tr("Gears"));
    skeys.append("X" + tr("Flwheelspeed"));
    skeys.append("X" + tr("Frwheelspeed"));
    skeys.append("X" + tr("Blwheelspeed"));
    skeys.append("X" + tr("Brwheelspeed"));
    skeys.append("X" + tr("Caninfocounter"));
    skeys.append(tr("Gyro") + "-X");
    skeys.append(tr("Gyro") + "-Y");
    skeys.append(tr("Gyro") + "-Z");
    skeys.append(tr("Gyro") + tr("Temp") + "-X");
    skeys.append(tr("Gyro") + tr("Temp") + "-Y");
    skeys.append(tr("Gyro") + tr("Temp") + "-Z");
    skeys.append(tr("Accel") + "-X");
    skeys.append(tr("Accel") + "-Y");
    skeys.append(tr("Accel") + "-Z");
    skeys.append(tr("Accel") + tr("Temp"));
    skeys.append(tr("Reserve") + "1");
    skeys.append(tr("Reserve") + "2");
    skeys.append(tr("Reserve") + "3");
    skeys.append(tr("Gnssweek"));
    skeys.append(tr("Millisecondofweek"));
    skeys.append(tr("Gnsssec"));
    skeys.append(tr("Ppsdelay10ns"));
    skeys.append(tr("StartNum"));
    skeys.append(tr("Rtkstatus"));
    skeys.append(tr("Speedstatus"));
    skeys.append(tr("Truenorthtrack"));
    skeys.append(tr("Nvelocity"));
    skeys.append(tr("Evelocity"));
    skeys.append(tr("Uvelocity"));
    skeys.append(tr("positionstatus"));
    skeys.append(tr("directionoflat"));
    skeys.append(tr("Latitude"));
    skeys.append(tr("directionoflon"));
    skeys.append(tr("Longitude"));
    skeys.append(tr("Altitude"));
    skeys.append(tr("Headingstate"));
    skeys.append(tr("Baselen"));
    skeys.append(tr("Roll"));
    skeys.append(tr("Pitch"));
    skeys.append(tr("Yaw"));
    skeys.append(tr("Gears"));
    skeys.append(tr("Caninfocounter"));
    skeys.append(tr("Flwheelspeed"));
    skeys.append(tr("Frwheelspeed"));
    skeys.append(tr("Blwheelspeed"));
    skeys.append(tr("Brwheelspeed"));
    skeys.append(tr("Timeprec"));
    skeys.append(tr("Verticalprec"));
    skeys.append(tr("Horizontalprec"));
    skeys.append(tr("Northprec"));
    skeys.append(tr("Eastprec"));
    skeys.append(tr("Endheightangle"));
    skeys.append(tr("StanDeviat_Lat"));
    skeys.append(tr("StanDeviat_Lon"));
    skeys.append(tr("StanDeviat_Alt"));
    skeys.append(tr("StanDeviat_Heading"));
    skeys.append(tr("StanDeviat_Pitch"));
    skeys.append(tr("Sol_Status"));
    skeys.append(tr("Pos_Type"));
    skeys.append("Checksum");
    skeys.append(tr("Frame_count"));
    skeys.append(tr("Longitude"));
    skeys.append(tr("Latitude"));
    skeys.append(tr("Altitude"));
    skeys.append("A-" + tr("Evelocity"));
    skeys.append("A-" + tr("Nvelocity"));
    skeys.append("A-" + tr("Uvelocity"));
    skeys.append("A-" + tr("Pitch"));
    skeys.append("A-" + tr("Roll"));
    skeys.append("A-" + tr("Yaw"));
    skeys.append(tr("Cali_gyro") + "x");
    skeys.append(tr("Cali_gyro") + "y");
    skeys.append(tr("Cali_gyro") + "z");
    skeys.append(tr("Cali_accel") + "x");
    skeys.append(tr("Cali_accel") + "y");
    skeys.append(tr("Cali_accel") + "z");
    skeys.append(tr("FpgA_Internum"));
    skeys.append(tr("Pack_count"));
    skeys.append(tr("Sytem_status"));
    return skeys;
}

QStringList CProtocolFactory::getBB11Keys(){
    QStringList skeys;
    return skeys;
}

QStringList CProtocolFactory::getB55BKeys(){
    QStringList skeys;
    skeys.append(QString::fromLocal8Bit("陀螺x:"));
    skeys.append(QString::fromLocal8Bit("陀螺y:"));
    skeys.append(QString::fromLocal8Bit("陀螺z:"));
    skeys.append(QString::fromLocal8Bit("加计x:"));
    skeys.append(QString::fromLocal8Bit("加计y:"));
    skeys.append(QString::fromLocal8Bit("加计z:"));
    skeys.append(QString::fromLocal8Bit("陀螺温度:"));
    skeys.append(QString::fromLocal8Bit("加计温度:"));
    skeys.append(QString::fromLocal8Bit("陀螺x平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺y平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺z平均值:"));
    skeys.append(QString::fromLocal8Bit("加计x平均值:"));
    skeys.append(QString::fromLocal8Bit("加计y平均值:"));
    skeys.append(QString::fromLocal8Bit("加计z平均值:"));
    skeys.append(QString::fromLocal8Bit("陀螺温度均值:"));
    skeys.append(QString::fromLocal8Bit("加计温度均值:"));
    return skeys;
}


