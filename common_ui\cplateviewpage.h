﻿#ifndef CPLATEVIEWPAGE_H
#define CPLATEVIEWPAGE_H

#include <QWidget>
#include <QGraphicsPixmapItem>
#include "ccarplatewidget.h"
#include <QTimer>

namespace Ui {
class CPlateViewPage;
}

/*
*仪表视图页面
*/
class CPlateViewPage : public QWidget
{
    Q_OBJECT

public:
    explicit CPlateViewPage(QWidget *parent = nullptr);
    ~CPlateViewPage();

    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;

    void showplateview(double roll, double heading, double pich, double speed, double hheight);
    void resizeEvent(QResizeEvent *event) override;

private:
    Ui::CPlateViewPage *ui;

    qreal rotationAngle;
    CCarplateWidget *m_carplatewd;
    QPixmap pixmap_top ;
    QPixmap pixmap_side;
    QPixmap pixmap_back;
    QMatrix topmatrix;
    QMatrix sidematrix;
    QMatrix backmatrix;
    QGraphicsPixmapItem *pixmapItem_top;
    QGraphicsPixmapItem *pixmapItem_side;
    QGraphicsPixmapItem *pixmapItem_back;
    float testloop;
    //创建 QGraphicsScene
    QGraphicsScene *scene_top;
    QGraphicsScene *scene_side;
    QGraphicsScene *scene_back;
};

#endif // CPLATEVIEWPAGE_H
