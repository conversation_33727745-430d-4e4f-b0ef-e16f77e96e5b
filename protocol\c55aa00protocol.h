﻿#ifndef C55AA00PROTOCOL_H
#define C55AA00PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class C55aa00Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C55aa00Protocol(QObject *parent = nullptr);
    ~C55aa00Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool preInit();
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strua55aa00{
        unsigned char c55;
        unsigned char ca5;
        unsigned char datalen;
        unsigned char col;
        unsigned char drvid05;
        unsigned char msgid93;
        unsigned int  framecount;
        Vector3i32 angleincrement;
        Vector3f speedincrement;
        Vector3i32 angularvelocity;
        Vector3f acceleration;
        short temperature;
        unsigned char selfteststatus;
        unsigned char cha;
        unsigned char chb;
    }Stru55aa00;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN);
    void sigFreqUpdate(const QStringList &datakeys, const QVector<double> dataValues, const QString &sportN);
private:
    int m_icatFreqCounts;
    long m_lcatCurrCounts;
    QVector<double> m_vGyrDatas;
    QVector<double> m_vTempDatas;
    double m_davggyr;
    double m_davgtemp;
    QStringList m_slFreqKeys;

signals:

};

#endif // C55AA00PROTOCOL_H
