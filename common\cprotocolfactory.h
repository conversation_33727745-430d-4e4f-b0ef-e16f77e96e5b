﻿#ifndef CPROTOCOLFACTORY_H
#define CPROTOCOLFACTORY_H

#include <QObject>
#include "cbaseprotocol.h"

/*
*协议工厂类
*/
class CProtocolFactory : public QObject
{
    Q_OBJECT

private:

public:
    /*顺序需要与CBaseProtocol中的g_sProtocolHead一致*/
    enum E_HEADPROTO{E_AA55, E_AA33, E_A5A5,E_AA66, E_B55B,E_4A0100, E_3A0100, E_3A0200, E_BDDB0B, E_BCCB0B, E_A55A, E_55AA, E_BB00, \
                     E_BB11, E_A6A6, E_4A020A, E_4A020B, E_3A020B, E_CDDC0B, E_BDDB1B};
    explicit CProtocolFactory(QObject *parent = nullptr);
    /*根据协议索引获取协议对象*/
    static CBaseProtocol *getProtocol(E_HEADPROTO index);
    static CBaseProtocol *getOtherProtocol();
    /*根据协议名称获取键值*/
    static QStringList getProKeys(QString protoname);
    /*根据协议索引获取键值*/
    static QStringList getProKeys(int index);

private:
    static QStringList getAA55Keys();
    static QStringList getAA33Keys();
    static QStringList getA5A5Keys();
    static QStringList getAA66Keys();
    static QStringList get4A0100Keys();
    static QStringList get3A0100Keys();
    static QStringList get3A0200Keys();
    static QStringList getBDDB0BKeys();
    static QStringList getBCCB0BKeys();
    static QStringList getA55AKeys();
    static QStringList get55AAKeys();
    static QStringList getBB00Keys();
    static QStringList getBB11Keys();
    static QStringList getB55BKeys();
    static QStringList getA6A6Keys();

signals:

};

#endif // CPROTOCOLFACTORY_H
