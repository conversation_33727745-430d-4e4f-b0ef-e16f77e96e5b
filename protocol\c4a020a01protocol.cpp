﻿#include "c4a020a01protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include <QThread>

C4A020A01Protocol::C4A020A01Protocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect C4a0100Protocol";
       connect(this, &C4A020A01Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &C4A020A01Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
       connect(this, &C4A020A01Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);

   }
   m_slProtoKeys.append(QString::fromLocal8Bit("系统时间:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-X:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("温度:"));

   m_iLossFrameSum = 0;
   m_iCheckFailSum = 0;

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);
}

bool C4A020A01Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 4);
    if(barr.size() < sizeof(Stru4a020a)){
        return false;
    }
    m_uMsgLen = sizeof(Stru4a020a);

    if(m_uMsgLen != sizeof(Stru4a020a)){
        m_uMsgLen = sizeof(Stru4a020a);
        return false;
    }

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(4)<<barr.at(5);
}

void C4A020A01Protocol::paseMsg(const QByteArray msg){
    Stru4a020a st_4a020a;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < sizeof(Stru4a020a)){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    if(!sum8CheckSum(msg, 4, 1)){
        //qDebug()<<QTime::currentTime().toString()<<"sumEorCheck error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") +QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    ::memcpy(&st_4a020a, msg.data(), 40);
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;
    dataValues.append(QString::number(st_4a020a.ttemp));
    dataValues.append(QString::number(st_4a020a.accel_x));
    dataValues.append(QString::number(st_4a020a.accel_y));
    dataValues.append(QString::number(st_4a020a.accel_z));
    dataValues.append(QString::number(st_4a020a.gyro_x));
    dataValues.append(QString::number(st_4a020a.gyro_y));
    dataValues.append(QString::number(st_4a020a.gyro_z));
    dataValues.append(QString::number(st_4a020a.temp_UNO));

    writeCvsFile("4A020A", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }

}
