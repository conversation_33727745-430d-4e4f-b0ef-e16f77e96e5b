﻿#ifndef CFLYVIEWPAGE_H
#define CFLYVIEWPAGE_H

#include <QWidget>
#include "cverticalscalwidget.h"
#include "ctophalfboardlab.h"
#include "cyawindwidge.h"
#include "cattitudewidget.h"

namespace Ui {
class CFlyViewPage;
}

/*
*飞行视图页面
*/
class CFlyViewPage : public QWidget
{
    Q_OBJECT

public:
    explicit CFlyViewPage(QWidget *parent = nullptr);
    void showplateview(double roll, double yaw, double pich, double speed, double hheight);
    ~CFlyViewPage();

private:
    Ui::CFlyViewPage *ui;
    CVerticalScalWidget *m_pwidalt;
    CVerticalScalWidget *m_pwidspeed;
    CTopHalfBoardLab *m_plabalt;
    CTopHalfBoardLab *m_plabspeed;
    CAttitudeWidget *m_pwidatt;
    CYawIndWidge *m_pwidyaw;

};

#endif // CFLYVIEWPAGE_H
