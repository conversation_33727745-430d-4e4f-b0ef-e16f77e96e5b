﻿#include "cconfigmanager.h"
#include <QDir>
#include <QDebug>
#include <cprotoparamdata.h>

CConfigManager::CConfigManager()
{
    m_sCurrentDir = QDir::currentPath();
    QString configdir = "/config";
    QDir dir;
    dir.mkpath(m_sCurrentDir + configdir);
    m_cFcommon = m_sCurrentDir + configdir + "/comonconfig.ini";
    qDebug()<<"m_cFcomon:"<<m_cFcommon;
    m_spComonSet = new QSettings(m_cFcommon, QSettings::IniFormat);

    m_spComonSet->beginGroup("SeriCache");
    m_spComonSet->setValue("Com", "COM1");
    m_spComonSet->setValue("Baudrate", 115200);
    m_spComonSet->setValue("StopBit", 1);
    m_spComonSet->setValue("DataBit", 8);
    m_spComonSet->setValue("Parity", QString::fromLocal8Bit("无"));
    m_spComonSet->endGroup();
}

QVariant CConfigManager::getValue(QString key){
    QVariant val;
    QStringList keys = key.split('.');
    m_spComonSet->beginGroup(keys.at(0));
    //默认中文
    val = m_spComonSet->value(keys.at(1), "CN");
    m_spComonSet->endGroup();
    return val;
}

void CConfigManager::setValue(QString key, QString val){
    QStringList keys = key.split('.');
    m_spComonSet->beginGroup(keys.at(0));
    m_spComonSet->setValue(keys.at(1), val);
    m_spComonSet->endGroup();
}

bool CConfigManager::loadConfig(QString &errorinfo){
    loadMapConfig();
    loadDevConfig();
    if(!loadFileConfig(errorinfo)){
        return false;
    }
}

void CConfigManager::saveConfig(){
    saveMapConfig();
    saveFileConfig();
}

void CConfigManager::loadMapConfig(){
    MapParmSt &mapst = CProtoParamData::getMapParmSt();
    m_spComonSet->beginGroup("Map");
    mapst.m_idashMode = m_spComonSet->value("m_iDashMode", 0).toInt();
    mapst.m_iFreshNum = m_spComonSet->value("m_iFreshNum", 200).toInt();
    mapst.m_iPlaystyle = m_spComonSet->value("m_iPlaystyle", 0).toInt();
    mapst.m_iInterTime = m_spComonSet->value("m_iInterTime", 200).toInt();
    mapst.m_iInterPoint = m_spComonSet->value("m_iInterPoint", 200).toInt();
    mapst.m_sfdotColor = m_spComonSet->value("m_sfdotColor", "#FF0000").toString();
    mapst.m_cfdotwidth = m_spComonSet->value("m_cfdotwidth", 0).toInt(); //宽度为2
    mapst.m_sflineColor = m_spComonSet->value("m_sflineColor", "#FF0000").toString();
    mapst.m_cflinewidth = m_spComonSet->value("m_cflinewidth", 1).toInt(); //宽度为1
    mapst.m_ssdotColor = m_spComonSet->value("m_ssdotColor", "#00FF00").toString();
    mapst.m_csdotwidth = m_spComonSet->value("m_csdotwidth", 0).toInt();
    mapst.m_sslineColor = m_spComonSet->value("m_sslineColor", "#00FF00").toString();
    mapst.m_cslinewidth = m_spComonSet->value("m_cslinewidth", 1).toInt();
    mapst.m_stdotColor = m_spComonSet->value("m_stdotColor", "#0000FF").toString();
    mapst.m_ctdotwidth = m_spComonSet->value("m_ctdotwidth", 0).toInt();
    mapst.m_stlineColor = m_spComonSet->value("m_stlineColor", "#0000FF").toString();
    mapst.m_ctlinewidth = m_spComonSet->value("m_ctlinewidth", 1).toInt();
    mapst.m_pflinestyle = m_spComonSet->value("m_pflinestyle", 0).toInt();
    mapst.m_pslinestyle = m_spComonSet->value("m_pslinestyle", 0).toInt();
    mapst.m_ptlinestyle = m_spComonSet->value("m_ptlinestyle", 0).toInt();
    mapst.m_pDefSite = m_spComonSet->value("m_pDefSite", QPointF(113.81893000,  22.74857000)).toPointF();
    mapst.m_iShowLevel = m_spComonSet->value("m_iShowLevel", 14).toInt();
    qDebug()<<"loadMapConfig:"<<mapst.m_idashMode<<mapst.m_sslineColor<<mapst.m_iInterTime;
    m_spComonSet->endGroup();
}

void CConfigManager::saveMapConfig(){
    MapParmSt &mapst = CProtoParamData::getMapParmSt();
    m_spComonSet->beginGroup("Map");
    m_spComonSet->setValue("m_iDashMode", mapst.m_idashMode);
    m_spComonSet->setValue("m_iFreshNum", mapst.m_iFreshNum);
    m_spComonSet->setValue("m_iPlaystyle", mapst.m_iPlaystyle);
    m_spComonSet->setValue("m_iInterTime", mapst.m_iInterTime);
    m_spComonSet->setValue("m_iInterPoint", mapst.m_iInterPoint);
    m_spComonSet->setValue("m_sfdotColor", mapst.m_sfdotColor);
    m_spComonSet->setValue("m_ssdotColor", mapst.m_ssdotColor);
    m_spComonSet->setValue("m_stdotColor", mapst.m_stdotColor);
    m_spComonSet->setValue("m_cfdotwidth", mapst.m_cfdotwidth);
    m_spComonSet->setValue("m_csdotwidth", mapst.m_csdotwidth);
    m_spComonSet->setValue("m_ctdotwidth", mapst.m_ctdotwidth);
    m_spComonSet->setValue("m_sflineColor", mapst.m_sflineColor);
    m_spComonSet->setValue("m_sslineColor", mapst.m_sslineColor);
    m_spComonSet->setValue("m_stlineColor", mapst.m_stlineColor);
    m_spComonSet->setValue("m_cflinewidth", mapst.m_cflinewidth);
    m_spComonSet->setValue("m_cslinewidth", mapst.m_cslinewidth);
    m_spComonSet->setValue("m_ctlinewidth", mapst.m_ctlinewidth);
    m_spComonSet->setValue("m_pflinestyle", mapst.m_pflinestyle);
    m_spComonSet->setValue("m_pslinestyle", mapst.m_pslinestyle);
    m_spComonSet->setValue("m_ptlinestyle", mapst.m_ptlinestyle);
    m_spComonSet->setValue("m_pDefSite", mapst.m_pDefSite);
    m_spComonSet->setValue("m_cslinewidth", mapst.m_cslinewidth);
    m_spComonSet->endGroup();
    m_spComonSet->sync();
}

void CConfigManager::loadDevConfig(){
    DevParmSt &devst = CProtoParamData::getDevParmSt();
    m_spComonSet->beginGroup("Dev");
    devst.prototype = m_spComonSet->value("prototype", "BB00DBBD").toString();
    devst.calitype = m_spComonSet->value("calitype", "MEMS-SCH630").toString();
    devst.gyrotype = m_spComonSet->value("gyrotype", "MEM").toString();
    devst.gyrodirect = m_spComonSet->value("gyrodirect", 0).toInt();
    devst.gyrofrq = m_spComonSet->value("gyrofrq", 500).toInt();
    devst.calitype = m_spComonSet->value("calitype", 1).toInt();
    devst.fpgafrq = m_spComonSet->value("fpgafrq", 500).toInt();
    m_spComonSet->endGroup();

}
void CConfigManager::saveDevConfig(){
    DevParmSt &devst = CProtoParamData::getDevParmSt();
    m_spComonSet->beginGroup("Dev");
    m_spComonSet->setValue("prototype", devst.prototype);
    m_spComonSet->setValue("calitype", devst.calitype);
    m_spComonSet->setValue("gyrotype", devst.gyrotype);
    m_spComonSet->setValue("gyrodirect", devst.gyrodirect);
    m_spComonSet->setValue("gyrofrq", devst.gyrofrq);
    m_spComonSet->setValue("calitype", devst.calitype);
    m_spComonSet->setValue("fpgafrq", devst.fpgafrq);
    m_spComonSet->endGroup();
    m_spComonSet->sync();
}

bool CConfigManager::loadFileConfig(QString &erroinfo){
    FileParmSt *filest = CProtoParamData::getFileParmSt();
    m_spComonSet->beginGroup("File");
    QString filepath = QDir::currentPath() + "/Data";
    QString mappath = QDir::currentPath() + "/MapCache";
    filest->savedir = m_spComonSet->value("DataDir", filepath).toString();
    filest->mapcachedir = m_spComonSet->value("MapCacheDir", mappath).toString();
    QFileInfo finfo(filest->savedir);
    QDir dir(filest->savedir);
    if(finfo.exists() && !finfo.isDir()){
        erroinfo = tr("Data file save dir is occupied,please change app dir!");
        return false;
    }
    dir.mkdir(filest->savedir);
    QFileInfo mapfinfo(filest->mapcachedir);
    QDir mapdir(filest->mapcachedir);
    if(finfo.exists() && !finfo.isDir()){
        erroinfo = tr("Mapcache dir is occupied,please change app dir!");
        return false;
    }
    dir.mkdir(filest->mapcachedir);
    filest->isNeedDat = m_spComonSet->value("isNeedDat", false).toBool();
    filest->isNeedCsv = m_spComonSet->value("isNeedCsv", true).toBool();
    m_spComonSet->endGroup();
}

void CConfigManager::saveFileConfig(){
    qDebug()<<"saveFileConfig";
    FileParmSt *filest = CProtoParamData::getFileParmSt();
    m_spComonSet->beginGroup("SaveFile");
    m_spComonSet->setValue("DataDir", filest->savedir);
    m_spComonSet->setValue("isNeedDat", filest->isNeedDat);
    m_spComonSet->setValue("isNeedCsv", filest->isNeedCsv);
    m_spComonSet->setValue("MapCacheDir", filest->mapcachedir);
    m_spComonSet->endGroup();
    m_spComonSet->sync();
}


