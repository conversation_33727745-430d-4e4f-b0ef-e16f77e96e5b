﻿#include "cinputdialog.h"
#include <QDebug>

CInputDialog::CInputDialog(QObject *parent) : QObject(parent)
{
    // 设置对话框的标题
    inputdlg.setWindowTitle(tr("PwdInput"));
    // 设置对话框的提示文本
    inputdlg.setLabelText(tr("Please input a password:"));
    // 设置输入框为密码模式
    inputdlg.setTextEchoMode(QLineEdit::Password);
    // 设置对话框的输入模式为文本输入
    inputdlg.setInputMode(QInputDialog::TextInput);
}

bool CInputDialog::exec(){
    if (inputdlg.exec() == QDialog::Accepted)
    {
        QString password = inputdlg.textValue();
        qDebug() << u8"输入的密码为：" << password;
        if(password == "TLH666888"){
            return true;
        }
    }
    return false;
}
