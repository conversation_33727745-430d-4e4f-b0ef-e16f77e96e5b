﻿#ifndef CBB0000PROTOCOL_H
#define CBB0000PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

//波特率200000

class Cbb0000Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit Cbb0000Protocol(QObject *parent = nullptr);
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool preInit();

private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _FmcDataInfo_{
        unsigned short head;
        unsigned short cmd;
        unsigned short len;
        unsigned short datalength;                        //1
        unsigned short selftestingcode;         //2
        unsigned short fpgaversion;             //3
        unsigned short watchversion;            //4
        unsigned short Xgears;                   //5
        float   Xflwheelspeed;            //6
        float   Xfrwheelspeed;            //7
        float   Xblwheelspeed;            //8
        float   Xbrwheelspeed;            //9
        unsigned short  Xcaninfocounter;          //10
        float     fogx;                    //11
        float     fogy;                    //12
        float     fogz;                  //13
        short     fogtemperaturex;         //14
        short     fogtemperaturey;         //15
        short     fogtemperaturez;         //26
        float   accelerometerx;          //17
        float   accelerometery;          //18
        float   accelerometerz;          //19
        short     accelerometertemp;       //20
        unsigned short     reserve1e;               //21
        unsigned short     reserve1f;               //22
        unsigned short     Reserve20;               //23
        unsigned short     gnssweek;                //24
        unsigned int     millisecondofweek;       //25
        unsigned int     secondofweek;            //26
        unsigned int     ppsdelay10ns;            //27
        unsigned short     gpsstarnumber;           //28
        unsigned short     rtkstatus;               //29
        unsigned short     speedstatus;             //30
        char     truenorthtrack[6];       //31
        float   northvelocity;           //32
        float   eastvelocity;            //33
        float   upvelocity;              //34
        unsigned short     GnssStaDirLat;          //36
        double  latitude;                //37
        unsigned short    DirLonHeadingSta;          //38
        double  longitude;               //39
        double  altitude;                //40
        float     baselength;              //42
        float   roll;                    //43
        float   pitch;                   //44
        float   yaw;                     //45
        unsigned short     gears;                   //
        unsigned short     caninfocounter;          //
        float   flwheelspeed;            //
        float   frwheelspeed;            //
        float   blwheelspeed;            //
        float   brwheelspeed;            //
        float   timeprecisionZ;          //51
        float   verticalprecZ;           //52
        float   horizontalprecZ;         //53
        float   northprecisionZ;         //54
        float   eastprecisionZ;          //55
        float   endheightangleZ;         //56
        float   StanDeviat_Lat;
        float   StanDeviat_Lon;
        float   StanDeviat_Alt;
        float   StanDeviat_Heading;
        float   StanDeviat_Pitch;
        int   Sol_Status;
        int   Pos_Type;
        unsigned short     checksum;                //57
        unsigned short     frameindex;              //59
        double  Alongitude;        //算法结果，经纬高
        double  Alatitude;        //算法结果，经纬高
        float   Aaltitude;        //算法结果，经纬高
        float   Ave;                //算法结果，东向速度
        float   Avn;                //算法结果，北向速度
        float   Avu;                //算法结果，天向速度
        float   Apitch;                //算法结果，俯仰角
        float   Aroll;                //算法结果，横滚角
        float   Aheading;        //算法结果，偏航角
        unsigned short  checksumA;               //58
        double cali_gyrox;     //标定后陀螺数据
        double cali_gyroy;
        double cali_gyroz;
        double cali_accelx;     //标定的加计数据
        double cali_accely;
        double cali_accelz;
        unsigned int  fpga_internum;
        unsigned int  packnum;
        char  sys_stat[2];
        unsigned short  total_checksum;
    }FmcDataInfo;
#pragma pack(pop)

signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString sportN);
private:
    long m_lcatCurrCounts;
    QVector<double> m_vGyrDatasX;
    QVector<double> m_vGyrDatasY;
    QVector<double> m_vGyrDatasZ;
    QVector<double> m_vTempDatasX;
    QVector<double> m_vTempDatasY;
    QVector<double> m_vTempDatasZ;
    QVector<double> m_vCaliDatasX;
    QVector<double> m_vCaliDatasY;
    QVector<double> m_vCaliDatasZ;
    double m_davggyr;
    double m_davgtemp;
    QStringList m_slFreqKeys;
    QVector<double> m_vScale;
    QVector<int> m_vScaleType;

};

#endif // CBB0000PROTOCOL_H
