﻿#include "ctophalfboardlab.h"
#include <QPainter>
#include <QRect>
#include <QDebug>

CTopHalfBoardLab::CTopHalfBoardLab(QWidget *parent) : QLabel(parent)
{
    m_bisValLab = false;  //默认不需要绘制文本
}

void CTopHalfBoardLab::paintEvent(QPaintEvent *event){
    // 调用基类的 paintEvent 以绘制文本和背景
    QLabel::paintEvent(event);

    // 创建一个 QPainter 对象
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing); // 启用抗锯齿

    // 设置画笔颜色和宽度
    //QPen pen(QColor("#4C2F7A"), 3); // 2 是边框宽度
    QPen pen(QColor(Qt::white), 2); // 2 是边框宽度
    painter.setPen(pen);

    // 获取 QLabel 的矩形区域
    QRect rect = this->rect();
    int margin = 20;   //边距，需要与样式中设置 一致
    QRect bordrect = QRect(rect.x() + margin, rect.y() + margin, rect.width() - 2 * margin, rect.height() - 2 * margin);

    // 圆角半径
    //int radius = 2;

    // 计算上面的三分之二高度
    //int topHeight = bordrect.height() * 2 / 3;

    // 创建一个 QPainterPath 来绘制圆角矩形
    //QPainterPath path;
    //path.moveTo(bordrect.topLeft() + QPoint(0, topHeight)); // 移动到左下角的起点
    //path.lineTo(bordrect.topLeft() + QPoint(0, radius)); // 移动到左上角圆角
    //path.arcTo(QRect(bordrect.topLeft(), QSize(2 * radius, 2 * radius)), 180, -90); // 绘制左上角圆角
    //path.moveTo(bordrect.topLeft() + QPoint(radius, 0));
    //path.lineTo(bordrect.topRight() - QPoint(radius, 0)); // 绘制上边框
    //path.arcTo(QRect(bordrect.topRight() - QPoint(2 * radius, 0), QSize(2 * radius, 2 * radius)), 90, -90); // 绘制右上角圆角
    //path.moveTo(bordrect.topRight() + QPoint(0, radius));
    //path.lineTo(bordrect.topRight() + QPoint(0, topHeight)); // 绘制右边框

    // 绘制路径
    //painter.drawPath(path);
    painter.drawLine(rect.x(), bordrect.y(), rect.x(), bordrect.y() + bordrect.height());
    painter.drawLine(rect.x() + width(), bordrect.y(), rect.x() + width(), bordrect.y() + bordrect.height());

    if(m_bisValLab){
        drawTextVal(painter);
    }
}

void CTopHalfBoardLab::drawTextVal(QPainter &painter){
    //从三分之一处开始绘制
    painter.setFont(QFont("Arial", 20));
    QRect irect(0, height() * 0.3, width(), height() * 0.3);
    QRect drect(width()/2, height() * 0.6, width(), height() * 0.3);
    painter.drawText(irect, Qt::AlignHCenter|Qt::AlignBottom, QString::number(m_integers));
    painter.setFont(QFont("Arial", 14));
    painter.setPen(Qt::yellow);
    painter.drawText(drect, Qt::AlignLeft|Qt::AlignTop,"." + QString::number(m_decimals));
}

void CTopHalfBoardLab::setTextVal(float fval){
    m_integers = static_cast<int>(fval);
    float fractionalPart = fval - m_integers;
    m_decimals = static_cast<int>(fractionalPart * 100);
    m_bisValLab = true;
    update();
}
