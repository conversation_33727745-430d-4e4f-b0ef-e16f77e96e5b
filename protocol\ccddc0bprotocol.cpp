﻿#include "ccddc0bprotocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include <QDebug>

CCDDC0BProtocol::CCDDC0BProtocol(QObject *parent) : CBaseProtocol(parent)
{

    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));

   if(dripage != NULL){
       qDebug()<<"connect Cbddb0bProtocol";
       connect(this, &CCDDC0BProtocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &CCDDC0BProtocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
   }

    m_slProtoKeys.append(tr("Roll") + ":");
    m_slProtoKeys.append(tr("Pitch") + ":");
    m_slProtoKeys.append(tr("Yaw") + ":");
    m_slProtoKeys.append(tr("Gyro") + "-x:");
    m_slProtoKeys.append(tr("Gyro") + "-y:");
    m_slProtoKeys.append(tr("Gyro") + "-z:");
    m_slProtoKeys.append(tr("Cali_accel") + "-x:");
    m_slProtoKeys.append(tr("Cali_accel") + "-y:");
    m_slProtoKeys.append(tr("Cali_accel") + "-z:");

}

bool CCDDC0BProtocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = sizeof(Strucddc0b);

    return true;
}

bool CCDDC0BProtocol::preInit(){
    CBaseProtocol::preInit();
    emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") + QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
    return true;
}

void CCDDC0BProtocol::paseMsg(const QByteArray msg){
    Strucddc0b st_cddc0b;
    QStringList dataValues;
    //异或校验
    if(!sumEorCheck(msg, 0, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sumEorCheck error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    m_iReqCount++;

    ::memcpy(&st_cddc0b, msg.data(), sizeof(Strucddc0b));


    float faccelx = st_cddc0b.accelx * 12.0 / 32768.0;
    float faccely = st_cddc0b.accely * 12.0 / 32768.0;
    float faccelz = st_cddc0b.accelz * 12.0 / 32768.0;
    float fgyrox = st_cddc0b.gyrox * 300.0 / 32768.0;
    float fgyroy = st_cddc0b.gyroy * 300.0 / 32768.0;
    float fgyroz = st_cddc0b.gyroz * 300.0 / 32768.0;
    float fpitch = st_cddc0b.pitch * 360.0 / 32768;
    float froll = st_cddc0b.roll * 360.0 / 32768;
    float fazimuth = st_cddc0b.azimuth * 360.0 / 32768;

    dataValues.append(QString::number(froll));
    dataValues.append(QString::number(fpitch));
    dataValues.append(QString::number(fazimuth));
    dataValues.append(QString::number(fgyrox));
    dataValues.append(QString::number(fgyroy));
    dataValues.append(QString::number(fgyroz));
    dataValues.append(QString::number(faccelx));
    dataValues.append(QString::number(faccely));
    dataValues.append(QString::number(faccelz));

    writeCvsFile("CDDC0B", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }
}
