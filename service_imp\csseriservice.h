﻿#ifndef CSSERISERVICE_H
#define CSSERISERVICE_H

#include <QObject>
#include <QSerialPort>
#include <QSerialPortInfo>
#include "cresolvingtask.h"
#include "cprotoparamdata.h"
#include <QPointer>

/*
* 串口业务处理类
*/
class CSseriService : public QObject
{
    Q_OBJECT
public:
    explicit CSseriService(QObject *parent = nullptr);
    ~CSseriService();
    bool SerialInit(QString sPort,QString sBaudRate, QString sDataBit,QString sParity, QString sStopbits, QString sIsHex, CProtoParamData *parm);
    void serialWriteData();
    QStringList getAvilaCom();
    void seriClose();
    bool seriWrite(QByteArray arr);
    void editWrite(QByteArray arr);
    bool isWorking();
    QByteArray getCurrentBuff();
    void clearBuff();
    void startTimeWork();
    void timerEvent(QTimerEvent *event) override;
    bool startWork();
    void stopWork();
    void setUpdateMode(bool mode=true);
    QByteArray doParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev);
    //void checkUpdateRes();
private:
    QString m_sPort;
    QString m_sBaudRate;
    QString m_sDataBit;
    QString m_sParity;
    QString m_sStopbits;
    QString m_sCheck;
    bool m_bIsHex;
    QPointer<QThread> m_workthread;
    QPointer<QSerialPort> m_serialPort;
    bool m_IsOpen;
    QByteArray m_SeriBuff;
    int m_checkSize;
    QPointer<CResolvingTask> m_resolvTask;
    bool m_bIsWorking;
    QStringList m_sComList;
    CProtoParamData *m_pparm;
    int m_timeStatus;
    int m_icatRound;
    int m_iTimeOut;
    int m_iIntvTimes;
    bool m_bIsUpdating;
    int m_TimerId;
    QByteArray m_bEnd;
public:
    static bool m_isReadSD;
public slots:
    void slotSerialOpenOrClose(const QString portn,const int optype, const QString fileprefix);
    void serialReadData(void);
    void slotSerialError(QSerialPort::SerialPortError e);
    void slotSeriWrite(const char * arr, int &len);
    void slotStartUpdate(QString filename);
    void slotStopUpdate();
    void slotResetUpdate();
    void slotVersionQuery(bool isOnlyQuery = true);
    void slotParmsUpdate(QByteArray cmd, QByteArray parms, bool);
    void slotMultilParmsUpdate(QByteArray cmd, QVector<QByteArray> parms, bool);

signals:
    void sigDataRead(const QByteArray &arr);
    void sigStartRun();
    void sigUpdateEnd(int status, QString result);
    void sigParmsEnd(int status, QByteArray cmd, QByteArray bytearry);
    void sigReadEnd(int status);
};

#endif // CSSERISERVICE_H
