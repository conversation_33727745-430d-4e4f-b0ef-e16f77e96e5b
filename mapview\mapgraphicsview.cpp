﻿#include "mapgraphicsview.h"

#include "bingformula.h"
#include "geturl.h"
#include <qthread.h>
#include <QDebug>
#include <QFont>
#include <QGraphicsItem>
#include <QMouseEvent>
#include <QScrollBar>
#include <QWheelEvent>
#include <QtMath>
#include <QPushButton>
#include <QGraphicsProxyWidget>
#include <QApplication>
#include <QStyle>
#include <QWheelEvent>
#include <QDir>
#include <QDateTime>

const qreal PI = 3.14159265358979323846;
const qreal deg2rad = PI / 180.0;
const qreal rad2deg = 180.0 / PI;

MapGraphicsView::MapGraphicsView(QWidget* parent)
    : QGraphicsView(parent)
{
    qDebug()<<"create MapGraphicsView";
    m_scene = new QGraphicsScene();
    this->setScene(m_scene);
    this->setDragMode(QGraphicsView::ScrollHandDrag);   // 鼠标拖拽

    setRenderHint(QPainter::Antialiasing);

    // 窗口左上角初始显示位置(中国-深圳)
    //m_scenePos.setX(3422532);
    //m_scenePos.setY(1824403);
    //    this->setMouseTracking(true);                       // 开启鼠标追踪

    QHBoxLayout *m_hlayout = new QHBoxLayout;
    btframe = new QFrame(this);
    btframe->setObjectName("btframe");
    //btframe->setGeometry(2, 2, 160, 40);
    //btframe->setFrameShape(QFrame::Panel);
    //btframe->setFrameShadow(QFrame::Raised);

    mapslider = new QSlider(this);
    mapslider->setOrientation(Qt::Vertical);
    mapslider->setRange(3, 18);
    mapslider->setValue(14);

    m_addtb = new QToolButton(this);
    m_addtb->resize(20, 20);
    m_subtb = new QToolButton(this);
    m_subtb->resize(20, 20);
    //ui->bt_startbt->setStyleSheet("border:Opx");
    m_addtb->setIconSize(QSize(m_addtb->width(),m_addtb->height()));
    QPixmap addpixmap(":/img/mapaddnew.png");
    m_addtb->setIcon(QIcon(addpixmap));
    m_subtb->setIconSize(QSize(m_addtb->width(),m_addtb->height()));
    QPixmap subpixmap(":/img/mapsubnew.png");
    m_subtb->setIcon(QIcon(subpixmap));

    m_clearbt = new QPushButton(tr("Clear"), btframe);
    m_clearbt->setIcon(QIcon(":/img/ImgBtn_Clear.png"));
    m_markbt = new QPushButton(tr("Mark"), btframe);
    m_markbt->setIcon(QIcon(":/img/btmark.png"));
    m_satellitebt = new QPushButton(tr("Sat map"),btframe);
    m_satellitebt->setIcon(QIcon(":/img/ImgBtn_Satellite.png"));
    //closebt = new QPushButton(tr("Close"), btframe);
    //closebt->setIcon(QIcon(":/img/ImgBtn_Close.png"));
    m_chRoad = new QCheckBox(tr("Road"), btframe);

    m_opcbox = new QComboBox(btframe);
    m_opcbox->addItem(tr("Operate"));
    m_opcbox->addItem(tr("Ranging"));
    m_lbstartpos = new QLabel(tr("StartPt"), btframe);
    m_lbstoppos = new QLabel(tr("EndPt"), btframe);
    m_lbdistance = new QLabel(tr("Dist") +("/m"), btframe);
    m_lestartpos = new QLineEdit(btframe);
    m_lestoppos = new QLineEdit(btframe);
    m_ledistance = new QLineEdit(btframe);

    m_lbstartpos->resize(30, 28);
    m_lbstoppos->resize(30, 28);
    m_lbdistance->resize(30, 28);
    m_lestartpos->resize(150, 28);
    m_lestoppos->resize(150, 28);
    m_ledistance->setFixedSize(80, 28);

    m_lbstartpos->setVisible(false);
    m_lbstoppos->setVisible(false);
    m_lbdistance->setVisible(false);
    m_lestartpos->setVisible(false);
    m_lestoppos->setVisible(false);
    m_ledistance->setVisible(false);

    m_hlayout->addWidget(m_clearbt);
    m_hlayout->addWidget(m_markbt);
    m_hlayout->addWidget(m_satellitebt);
    m_hlayout->addWidget(m_chRoad);
    m_hlayout->addWidget(m_opcbox);
    m_hlayout->addWidget(m_lbstartpos);
    m_hlayout->addWidget(m_lestartpos);
    m_hlayout->addWidget(m_lbstoppos);
    m_hlayout->addWidget(m_lestoppos);
    m_hlayout->addWidget(m_lbdistance);
    m_hlayout->addWidget(m_ledistance);
    m_hlayout->addStretch();
    m_hlayout->setMargin(0);
    btframe->setLayout(m_hlayout);

    /*clearbt->setStyleSheet("QPushButton {"
                         "background: transparent;"
                         "}");
    m_chRoad->setStyleSheet("QCheckBox {"
                         "background: transparent;"
                         "}");
    satellitebt->setStyleSheet("QPushButton {"
                         "background: transparent;"
                         "}");
    //closebt->setStyleSheet("QPushButton {"
    //                     "background: transparent;"
    //                     "}");
    */
    qDebug()<<"create tbt";
    m_markfilename =  QDir::currentPath() + "/Data/" + \
            QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + "_mark.csv";

    m_clearbt->resize(80, 28);
    m_markbt->resize(80, 28);
    m_chRoad->resize(80, 28);
    m_satellitebt->resize(80, 28);

    m_markfile = NULL;
    //closebt->resize(80, 40);

    //m_clearbt->move(0,0);
    //m_satellitebt->move(80,0);
    //m_chRoad->move(160,0);
    m_chRoad->setVisible(false);
    //closebt->move(240,0);

    currUrlType = 0; //显示地图类型 0-矢量图 1-卫星图

    showRoad = false;

    m_playbwd = new CPlaybackWidget(this);
    m_playbwd->hide();
    //m_playbwd->setStyleSheet("QFrame{ background: transparent;}");

    m_tHidePb = new QToolButton(this);
    m_tHidePb->resize(20, 20);
    m_tHidePb->setIconSize(QSize(20, 20));
    m_tHidePb->setToolButtonStyle(Qt::ToolButtonFollowStyle);
    m_tHidePb->setIcon(QApplication::style()->standardIcon(QStyle::StandardPixmap(5)));

    m_bPbisShow = false;  //回放面板初始不显示

    connect(GetUrlInterface::getInterface(), &GetUrlInterface::update, this, &MapGraphicsView::drawImg);
    //connect(GetUrlInterface::getInterface(), &GetUrlInterface::showLine, this, &MapGraphicsView::drawLine);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::showPos, this, [=](QPointF pixpos, QPointF lnglatpos){
        m_initscenePos = pixpos;
        m_initlnglatPos = lnglatpos;
    });
    connect(m_satellitebt, &QPushButton::clicked, this, &MapGraphicsView::urlChange);
    connect(m_chRoad, &QCheckBox::stateChanged, this, &MapGraphicsView::addRoad);
    connect(m_tHidePb, &QToolButton::clicked, this, &MapGraphicsView::slotShowPbWid);
    connect(m_clearbt, &QPushButton::clicked, this, [=](){
        for (int i = 0;i < m_mCmapTlines.size();i++) {
            CMapTrajLine *mptl = m_mCmapTlines.values().at(i);
            mptl->clear();
            m_bDrwLnBg = false;
            delete mptl;
        }
        m_mCmapTlines.clear();
        m_ifirstrow = -1;
        drawLine();
    });

    //增加标记
    connect(m_markbt, &QPushButton::clicked, this, [=](){
        if(m_mCmapTlines.values().size() > 0){
            CMapTrajLine *mptl = m_mCmapTlines.values().at(0);
            QPointF point = mptl->addMark();
            //将标记点保存到文件
            if(m_markfile == NULL){
                m_markfile = new QFile(m_markfilename);
                m_markfile->open(QIODevice::WriteOnly);
                m_markfile->write(u8"时间,经度,纬度\n");
            }else{
                m_markfile->open(QIODevice::Append);
            }
            QString writestr = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") + "," \
                    + QString::number(point.x(), 'f', 8) + "," + QString::number(point.y(), 'f', 8) + "\n";
            //qDebug()<<"writestr:"<<writestr;
            m_markfile->write(writestr.toLocal8Bit());
            m_markfile->close();
        }
    });

    connect(m_lestoppos, &QLineEdit::editingFinished, this, [=](){
        static QPointF firstpoint, secondpoint;
        QString starts = m_lestartpos->text();
        QString ends = m_lestoppos->text();
        QStringList slstart = starts.split(',');
        QStringList slend = ends.split(',');
        if(slstart.size() == 2 && slend.size() == 2){
            firstpoint = QPointF(slstart.at(0).toDouble(), slstart.at(1).toDouble());
            secondpoint = QPointF(slend.at(0).toDouble(), slend.at(1).toDouble());
            m_ledistance->setText(QString::number(Bing::CalculateDistance(firstpoint.x(), firstpoint.y(), secondpoint.x(), secondpoint.y())));
        }
    });

    //回放
    connect(m_playbwd, &CPlaybackWidget::sigFollowChange, this, [=](bool isfollow){
        m_bisFollow = isfollow;
    });

    //缩放控件
    connect(mapslider, &QSlider::valueChanged, this, &MapGraphicsView::slotSliderChange);
    connect(m_addtb, &QToolButton::clicked, this, [=](){
        mapslider->setValue(mapslider->value() + 1);
    });

    connect(m_subtb, &QToolButton::clicked, this, [=](){
        mapslider->setValue(mapslider->value() - 1);
    });

    //测距
    connect(m_opcbox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, [=](int index){
        //qDebug()<<"currentIndexChanged:"<<index;
        if(index == 1){
            m_lbstartpos->setVisible(true);
            m_lestartpos->setVisible(true);
            m_lestartpos->clear();
            m_lbstoppos->setVisible(true);
            m_lestoppos->setVisible(true);
            m_lestoppos->clear();
            m_lbdistance->setVisible(true);
            m_ledistance->setVisible(true);
            m_ledistance->clear();
            btframe->resize(btframe->width() + 510, 28);
            setDragMode(QGraphicsView::NoDrag);
        }else{
            m_lbstartpos->setHidden(true);
            m_lestartpos->setHidden(true);
            m_lbstoppos->setHidden(true);
            m_lestoppos->setHidden(true);
            m_lbdistance->setHidden(true);
            m_ledistance->setHidden(true);
            btframe->resize(btframe->width() - 510, 28);
            setDragMode(QGraphicsView::ScrollHandDrag);
        }
        //qDebug()<<"frame size:"<<btframe->width()<<btframe->contentsMargins()<<btframe->geometry()<<m_lbstartpos->height()<<m_clearbt->height();
    });

    m_bDrwLnBg = false;
    m_bWidShowed = false;

    m_vLineColor.append(QColor(Qt::red));
    m_vLineColor.append(QColor(Qt::blue));
    m_vLineColor.append(QColor(Qt::green));

    m_ifirstrow = -1;   //第一个轨迹

    m_bisFollow = true;  //跟随

    m_beforelevel = m_level;

    m_vLinePens.append(QPen(Qt::red, 2, Qt::DashDotLine));
    m_vLinePens.append(QPen(Qt::red, 2, Qt::DashDotLine));
    m_vLinePens.append(QPen(Qt::red, 2, Qt::DashDotLine));

    m_vDotpen.append(QPen(Qt::red, 2, Qt::DashDotLine));
    m_vDotpen.append(QPen(Qt::red, 2, Qt::DashDotLine));
    m_vDotpen.append(QPen(Qt::red, 2, Qt::DashDotLine));

}

MapGraphicsView::~MapGraphicsView() {}

void MapGraphicsView::urlChange(){
    if(currUrlType == 0){
        emit GetUrlInterface::getInterface()->urlChange(1);
        currUrlType = 1;
        btframe->resize(btframe->width() + 80, 28);
        m_chRoad->setVisible(true);
        //satellitebt->setStyleSheet("QPushButton {"
        //                     "background: #9932CD;"
        //                     "}");
    }else{
        //satellitebt->setStyleSheet("QPushButton {"
        //                     "background: transparent;"
        //                     "}");
        emit GetUrlInterface::getInterface()->urlChange(0);
        m_chRoad->setCheckState(Qt::Unchecked);
        m_chRoad->setVisible(false);
        btframe->resize(btframe->width() - 80, 28);
        currUrlType = 0;
    }
}

void MapGraphicsView::addRoad(){
    if(currUrlType == 0){
        showRoad = false;
        return;
    }
    if(m_chRoad->isChecked()){
        showRoad = true;
        qDebug()<<"showroad";
        if (m_overitemGroup.contains(m_level))   // 如果底部图层不存在则添加
        {
            m_overitemGroup[m_level]->show();
        }else{
            emit GetUrlInterface::getInterface()->urlChange(2);
            getShowRect();
        }
    }else{
        showRoad = false;
        qDebug()<<"remove";
        for (auto itemG : m_overitemGroup)
        {
            itemG->hide();
        }
    }

}

void MapGraphicsView::setRect(int level)
{
    int w = int(qPow(2, level) * 256);
    QRect rect(0, 0, w, w);
    m_scene->setSceneRect(rect);

    //qDebug()<<"setRect:"<<m_pos<<m_scenePos;

    // 将显示位置移动到缩放之前的位置
    this->horizontalScrollBar()->setValue(qRound(m_scenePos.x() - m_pos.x()));
    this->verticalScrollBar()->setValue(qRound(m_scenePos.y() - m_pos.y()));
}

/**
 * @brief       绘制瓦片图
 * @param info
 */
void MapGraphicsView::drawImg(const ImageInfo& info)
{
    //qDebug()<<"drawimg:"<<info.x<<info.y<<info.z<<info.type;
    //m_scenePos = Bing::tileXYToPixelXY(QPoint(info.x - 5, info.y - 5));
    //qDebug()<<"drawimg:"<<info.x<<info.y<<info.z<<info.type<<m_scenePos;
    if(info.type == 0){
        if (!m_itemGroup.contains(info.z))   // 如果图层不存在则添加
        {
            auto* item = new GraphicsItemGroup();
            m_itemGroup.insert(info.z, item);
            m_scene->addItem(item);
        }

        GraphicsItemGroup* itemGroup = m_itemGroup.value(info.z);
        if (itemGroup)
        {
            itemGroup->addImage(info);
        }
    }

    if(info.type == 1){
        if (!m_overitemGroup.contains(info.z))   // 如果底部图层不存在则添加
        {
            auto* item = new GraphicsItemGroup();
            m_overitemGroup.insert(info.z, item);
            m_scene->addItem(item);
        }

        GraphicsItemGroup* itemGroup = m_overitemGroup.value(info.z);
        if (itemGroup)
        {
            itemGroup->addImage(info);
        }
    }
    //防止干擾，需要同步重新繪製
    m_playbwd->update();
}

/**
 * @brief 清空所有瓦片
 */
void MapGraphicsView::clear()
{
    auto* itemGroup = m_itemGroup.value(m_level);
    if (itemGroup)
    {
        delete itemGroup;
        m_itemGroup.remove(m_level);
        m_level = 0;
    }
}

void MapGraphicsView::mousePressEvent(QMouseEvent* event)
{
    QGraphicsView::mousePressEvent(event);

    if (event->buttons() & Qt::LeftButton)
    {
        m_moveView = true;
    }

    //qDebug()<<event->x()<<event->y();
    QPoint tl = this->mapToScene(0, 0).toPoint();
    QPoint br = this->mapToScene(this->width(), this->height()).toPoint();
    int x = qMax(tl.x(), 0);
    int y = qMax(tl.y(), 0);
    qreal longitude;
    qreal latitude;
    Bing::pixelXYToLatLong(QPoint(x + event->x(), y + event->y()), m_level, longitude, latitude);
    //emit GetUrlInterface::getInterface() -> showPos(longitude, latitude);
    //emit GetUrlInterface::getInterface() -> showPos(br.x(), br.y());
    //emit GetUrlInterface::getInterface() -> showLine(QPointF(longitude, latitude));
    //计算两点之间的距离
    if(m_opcbox->currentIndex() == 1){
        static QPointF firstpoint;
        if(m_lestartpos->text().isEmpty()){
            firstpoint = QPointF(longitude, latitude);
            m_lestartpos->setText(QString("%1,%2").arg(longitude).arg(latitude));
        }else{
            m_lestoppos->setText(QString("%1,%2").arg(longitude).arg(latitude));
            m_ledistance->setText(QString::number(Bing::CalculateDistance(firstpoint.x(), firstpoint.y(), longitude, latitude)));
        }
    }

}

/**
 * @brief          鼠标释放
 * @param event
 */
void MapGraphicsView::mouseReleaseEvent(QMouseEvent* event)
{
    //qDebug()<<"release scenpos1:"<<QString("%1-%2").arg(m_scenePos.x(), 10, 'f', 8).arg(m_scenePos.y(), 10, 'f', 8)<<QString("%1-%2").arg(m_initscenePos.x(), 10, 'f', 8).arg(m_initscenePos.y(), 10, 'f', 8);
    QGraphicsView::mouseReleaseEvent(event);
    //移动位置后，中心点发生变化，需要由右上角的点重新计算中心点的位置
    m_initscenePos = QPointF(this->mapToScene(width()/2, height()/2));

    if (m_moveView)   // 在鼠标左键释放时获取新的瓦片地图
    {
        //emit mousePos(this->mapToScene(event->pos()).toPoint());
        getShowRect();
        m_moveView = false;
    }
    //qDebug()<<"release scenpos2:"<<QString("%1-%2").arg(m_scenePos.x(), 10, 'f', 8).arg(m_scenePos.y(), 10, 'f', 8)<<QString("%1-%2").arg(m_initscenePos.x(), 10, 'f', 8).arg(m_initscenePos.y(), 10, 'f', 8);

    qreal longitude;
    qreal latitude;
    Bing::pixelXYToLatLong(QPoint(m_initscenePos.x(), m_initscenePos.y()), m_level, longitude, latitude);
    m_initlnglatPos = QPointF(longitude, latitude);
    //qDebug()<<"init lnglat:"<<m_initlnglatPos;

    //drawLine();
}

/**
 * @brief        鼠标滚轮缩放
 * @param event
 */
void MapGraphicsView::wheelEvent(QWheelEvent* event)
{   
    bool isfollow = m_bisFollow;
    m_bisFollow = false;      //先取消跟随，防止地图重置冲突
    m_pos = event->pos();                          // 鼠标相对于窗口左上角的坐标
    qDebug()<<"wheelEvent m_pos:"<<m_pos;

    m_scenePos = this->mapToScene(event->pos());   // 鼠标在场景中的坐标
    //qreal longitude;
    //qreal latitude;
    //Bing::pixelXYToLatLong(QPoint(m_scenePos.x() + width() / 2, m_scenePos.y() + height() / 2), m_level, longitude, latitude);
    //m_initlnglatPos = QPointF(longitude, latitude);

    //qDebug()<<"wheelEvent m_pos:"<<m_pos<<m_initlnglatPos<<m_scenePos;


    if (event->angleDelta().y() > 0)
    {
        if(m_level == 20){
            m_bisFollow = isfollow;
            return;
        }
        m_scenePos = m_scenePos * 2;   // 放大
        m_level++;
    }
    else
    {
        if(m_level == 3){
            m_bisFollow = isfollow;
            return;
        }
        m_scenePos = m_scenePos / 2;   // 缩小
        m_level--;
    }
    m_level = qBound(0, m_level, 22);                            // 限制缩放层级
    setRect(m_level);                                            // 设置缩放后的视图大小
    emit GetUrlInterface::getInterface() -> setLevel(m_level);   // 设置缩放级别
    getShowRect();

    // 隐藏底层缩放前所有图层
    for (auto itemG : m_itemGroup)
    {
        itemG->hide();
    }

    if (m_itemGroup.contains(m_level))   // 如果图层存在则显示
    {
        GraphicsItemGroup* itemGroup = m_itemGroup.value(m_level);
        itemGroup->show();
    }
    else   // 如果不存在则添加
    {
        auto* item = new GraphicsItemGroup();
        m_itemGroup.insert(m_level, item);
        m_scene->addItem(item);
    }

    //隐藏上层缩放所有图层
    for (auto itemG : m_overitemGroup)
    {
        itemG->hide();
    }

    if(showRoad){
        if (m_overitemGroup.contains(m_level))   // 如果图层存在则显示
        {
            GraphicsItemGroup* itemGroup = m_overitemGroup.value(m_level);
            itemGroup->show();
        }
        else   // 如果不存在则添加
        {
            auto* item = new GraphicsItemGroup();
            m_overitemGroup.insert(m_level, item);
            m_scene->addItem(item);
        }
    }
    //重新根据地图级别计算轨迹中心点位置
    //m_initscenePos = Bing::latLongToPixelXY(m_initlnglatPos.x(),m_initlnglatPos.y(), m_level);
    m_initscenePos = this->mapToScene(width()/2,height()/2);
    drawLine();
    m_bisFollow = isfollow;

    m_beforelevel = m_level;
    mapslider->setValue(m_level);
    m_scenePos = this->mapToScene(0,0); //更新左上角的位置

}

void MapGraphicsView::slotSliderChange(int val){
    //若是鼠标滚轮触发则不处理
    if(m_beforelevel == val){
        return;
    }
    m_pos = QPointF(width() / 2, height()/2);
    m_scenePos = mapToScene(m_pos.x(), m_pos.y());
    if(m_beforelevel > val){
        m_scenePos = m_scenePos / qPow(2.0, m_beforelevel - val);
    }else{
        m_scenePos = m_scenePos * qPow(2.0, val - m_beforelevel);
    }
    m_level = val;
    m_beforelevel = m_level;
    //m_initscenePos = mapToScene(width() / 2, height() / 2);
    //QPoint oldscenspos = mapFromScene(m_scenePos);
    //m_scenePos = QPointF(m_initscenePos.x() - width() / 2, m_initscenePos.y() - height() / 2);
    //qDebug()<<"slotSliderChange"<<m_initscenePos<<m_scenePos<<m_level<<m_initlnglatPos<<oldscenspos;

    setRect(m_level);                                            // 设置缩放后的视图大小
    emit GetUrlInterface::getInterface() -> setLevel(m_level);   // 设置缩放级别
    getShowRect();

    // 隐藏底层缩放前所有图层
    for (auto itemG : m_itemGroup)
    {
        itemG->hide();
    }

    if (m_itemGroup.contains(m_level))   // 如果图层存在则显示
    {
        GraphicsItemGroup* itemGroup = m_itemGroup.value(m_level);
        itemGroup->show();
    }
    else   // 如果不存在则添加
    {
        auto* item = new GraphicsItemGroup();
        m_itemGroup.insert(m_level, item);
        m_scene->addItem(item);
    }

    //隐藏上层缩放所有图层
    for (auto itemG : m_overitemGroup)
    {
        itemG->hide();
    }

    if(showRoad){
        if (m_overitemGroup.contains(m_level))   // 如果图层存在则显示
        {
            GraphicsItemGroup* itemGroup = m_overitemGroup.value(m_level);
            itemGroup->show();
        }
        else   // 如果不存在则添加
        {
            auto* item = new GraphicsItemGroup();
            m_overitemGroup.insert(m_level, item);
            m_scene->addItem(item);
        }
    }
    //重新根据地图级别计算轨迹中心点位置
    m_initscenePos = Bing::latLongToPixelXY(m_initlnglatPos.x(),m_initlnglatPos.y(), m_level);
    drawLine();
}

/**
 * @brief       窗口大小变化后获取显示新的地图
 * @param event
 */
void MapGraphicsView::resizeEvent(QResizeEvent* event)
{
    QGraphicsView::resizeEvent(event);

    //将鼠标偏移位置置为0
    m_pos = QPointF(0,0);

    m_playbwd->resize(this->width(), 60);
    m_playbwd->move(0, this->height() - 60);
    if(m_bPbisShow){
        m_tHidePb->move(this->width() - 20, this->height() - 60 - 20);
    }else{
        m_tHidePb->move(this->width() - 20, this->height() - 20);
    }

    //由初始中心点计算左上角的点，放大中心点的坐标不会发生变化
    m_initscenePos = Bing::latLongToPixelXY(m_initlnglatPos.x(),m_initlnglatPos.y(), m_level);
    m_scenePos = QPointF(m_initscenePos.x() - width() / 2, m_initscenePos.y() - height() / 2);
    //qDebug()<<"MapGraphicsView::resizeEvent:"<<mapFromScene(m_initscenePos)<<mapFromScene(m_scenePos)<<m_pos;
    setRect(m_level);
    //drawLine();
    getShowRect();
}

/**
 * @brief       窗口显示时设置显示瓦片的视图位置
 * @param event
 */
void MapGraphicsView::showEvent(QShowEvent* event)
{
    if(m_bPbisShow){
        m_tHidePb->move(this->width() - 20, this->height() - 60 - 20);
    }else{
        m_tHidePb->move(this->width() - 20, this->height() - 20);
    }
    m_playbwd->resize(this->width(), 60);
    m_playbwd->move(0, this->height() - 60);

    mapslider->move(20 - mapslider->width() / 2, 86);
    m_subtb->move(20 - m_addtb->width() / 2, 86 + mapslider->height() + 6);
    m_addtb->move(20 - m_subtb->width() / 2, 60);

    setRect(m_level);
    //qDebug()<<"view size"<<this->width()<<this->height();
    QGraphicsView::showEvent(event);
}

/**
 * @brief 获取当前场景的显示范围（场景坐标系）
 * 当使用鼠标拖动时，scence对应窗口的坐标会发生变化，也就是实际窗口的坐标对应虚拟的坐标发生了变化，在鼠标释放时，根据当前窗口的坐标可以获取到对应的新的虚拟坐标
 */
void MapGraphicsView::getShowRect()
{
    QRect rect;
    int w = int(qPow(2, m_level) * 256);   // 最大范围
    QPoint tl = this->mapToScene(0, 0).toPoint();
    QPoint br = this->mapToScene(this->width(), this->height()).toPoint();
    rect.setX(qMax(tl.x(), 0));
    rect.setY(qMax(tl.y(), 0));
    rect.setRight(qMin(br.x(), w));
    rect.setBottom(qMin(br.y(), w));
    //qDebug()<<"getShowRect:"<<rect;
    emit GetUrlInterface::getInterface() -> showRect(rect);
}

void MapGraphicsView::drawLine(int id, QPointF cpos, float process){
    //qDebug()<<"drawLine:"<<id<<cpos;
    if(m_ifirstrow == -1){
        m_ifirstrow = id;
        m_playbwd->reset();
    }
    if(id == m_ifirstrow){
        m_playbwd->setCurrentProcess(process);
    }
    QPointF currentpos = Bing::latLongToPixelXY(cpos.x(),cpos.y(), m_level);
    //m_initlnglatPos = cpos;
    if(!m_bDrwLnBg){
        //重画时重新获取配置参数信息
        MapParmSt &sMapParmst = CProtoParamData::getMapParmSt();
        qDebug()<<"sMapParmst.m_cdlineColor:"<<sMapParmst.m_sflineColor;
        m_vLinePens[0].setColor(QColor(sMapParmst.m_sflineColor));
        m_vLinePens[0].setWidth(sMapParmst.m_vdbnum.at(sMapParmst.m_cflinewidth));
        m_vLinePens[0].setStyle(sMapParmst.m_vdstyle.at(sMapParmst.m_pflinestyle));
        m_vLinePens[1].setColor(QColor(sMapParmst.m_sslineColor));
        m_vLinePens[1].setWidth(sMapParmst.m_vdbnum.at(sMapParmst.m_cslinewidth));
        m_vLinePens[1].setStyle(sMapParmst.m_vdstyle.at(sMapParmst.m_pslinestyle));
        m_vLinePens[2].setColor(QColor(sMapParmst.m_stlineColor));
        m_vLinePens[2].setWidth(sMapParmst.m_vdbnum.at(sMapParmst.m_ctlinewidth));
        m_vLinePens[2].setStyle(sMapParmst.m_vdstyle.at(sMapParmst.m_ptlinestyle));
        if(sMapParmst.m_iFreshNum > sMapParmst.m_iInterTime){
            m_iFrushFrq = sMapParmst.m_iFreshNum / sMapParmst.m_iInterTime;
        }else{
            m_iFrushFrq = 1;
        }

        m_vDotpen[0].setWidth(sMapParmst.m_cfdotwidth);
        m_vDotpen[0].setColor(QColor(sMapParmst.m_sfdotColor));
        m_vDotpen[1].setWidth(sMapParmst.m_csdotwidth);
        m_vDotpen[1].setColor(QColor(sMapParmst.m_ssdotColor));
        m_vDotpen[2].setWidth(sMapParmst.m_ctdotwidth);
        m_vDotpen[2].setColor(QColor(sMapParmst.m_stdotColor));
        //一开始将此点设置为显示地图中心，并将地图层级设置为17
        m_level = 17;
        mapslider->setValue(17);
        m_initscenePos = Bing::latLongToPixelXY(cpos.x(),cpos.y(), m_level);
        m_scenePos = QPointF(m_initscenePos.x(), m_initscenePos.y());
        emit GetUrlInterface::getInterface() -> setLevel(m_level);   // 设置缩放级别
        setRect(m_level);                                            // 设置缩放后的视图大小
        getShowRect();

        //// 隐藏底层缩放前所有图层
        //for (auto itemG : m_itemGroup)
        //{
        //    itemG->hide();
        //}
        //
        //if (m_itemGroup.contains(m_level))   // 如果图层存在则显示
        //{
        //    GraphicsItemGroup* itemGroup = m_itemGroup.value(m_level);
        //    itemGroup->show();
        //}
        //else   // 如果不存在则添加
        //{
        //    auto* item = new GraphicsItemGroup();
        //    m_itemGroup.insert(m_level, item);
        //    m_scene->addItem(item);
        //}
        //
        ////隐藏上层缩放所有图层
        //for (auto itemG : m_overitemGroup)
        //{
        //    itemG->hide();
        //}

        if(showRoad){
            if (m_overitemGroup.contains(m_level))   // 如果图层存在则显示
            {
                GraphicsItemGroup* itemGroup = m_overitemGroup.value(m_level);
                itemGroup->show();
            }
            else   // 如果不存在则添加
            {
                auto* item = new GraphicsItemGroup();
                m_overitemGroup.insert(m_level, item);
                m_scene->addItem(item);
            }
        }



    }

    QPointF posdistance = currentpos - m_initscenePos;
    //qDebug()<<"posdistance:"<<m_bDrwLnBg<<m_bisFollow<<qAbs(posdistance.x())<<qAbs(posdistance.y())<<posdistance<<width()/2 - 60<<height()/2 - 60;

    if(m_bDrwLnBg && m_bisFollow && (qAbs(posdistance.x()) > width()/2 - 60 || qAbs(posdistance.y()) > height()/2 - 60)){
        //qDebug()<<"xdistance:"<<posdistance<<currentpos<<m_initscenePos<<m_pos;
        m_initscenePos = currentpos;
        m_initlnglatPos = cpos;
        //m_scenePos += posdistance;
        m_scenePos = QPointF(m_initscenePos.x() - width() / 2 + m_pos.x(), m_initscenePos.y() - height() / 2 + m_pos.y());
        setRect(m_level);
        getShowRect();
    }

    m_bDrwLnBg = true;

    if(!m_mCmapTlines.contains(id)){
        m_mCmapTlines[id] = new CMapTrajLine(m_scene, m_level);
        //m_mCmapTlines[id]->setColor(m_vLineColor.at(m_mCmapTlines.size() - 1));
        //m_mCmapTlines[id]->setColor(m_vLineColor.at(m_mCmapTlines.size() - 1));
        //m_mCmapTlines[id]->getColor();

        int i = 0;
        for(CMapTrajLine *traline:m_mCmapTlines.values()){
            //qDebug()<<"set pen:"<<m_vLinePens[i].color().name();
            traline->setPen(m_vLinePens[i], m_vDotpen[i]);
            if(i > 2){
                traline->setPen(m_vLinePens[0], m_vDotpen[0]);
            }
            i++;
        }
    }

    m_mCmapTlines[id]->appendData(cpos);
    //qDebug()<<"drawLine"<<m_level<<QString::number(cpos.x(), 'g', 8)<<QString::number(cpos.y(), 'g', 8);
    drawLine();
}
void MapGraphicsView::drawLine(){
    if(m_iFrushFrq != m_iFrushSum++ ){
        return;
    }

    m_iFrushSum = 1;

    //仅在视图页面显示的时候绘制
    if(!isVisible()){
        return;
    }
    for (int i = 0;i < m_mCmapTlines.size();i++) {
        m_mCmapTlines.values().at(i)->setlevel(m_level);
        m_mCmapTlines.values().at(i)->drawLine();
    }
}


void MapGraphicsView::slotShowPbWid(){
    qDebug()<<"slotShowPbWid";
    if(m_bPbisShow){
        m_playbwd->setHidden(true);
        m_bPbisShow = false;
        m_tHidePb->move(this->width() - 20, this->height() - 20);
        m_tHidePb->setIcon(QApplication::style()->standardIcon(QStyle::StandardPixmap(5)));

    }else{
        m_playbwd->setHidden(false);
        m_bPbisShow = true;
        m_tHidePb->move(this->width() - 20, this->height() - 60 - 20);
        m_tHidePb->setIcon(QApplication::style()->standardIcon(QStyle::StandardPixmap(6)));

    }
}

CMapTrajLine::CMapTrajLine(QGraphicsScene *scene, int level): m_scene(scene), m_level(level){
    m_itemMak = NULL;
    m_itemP = NULL;
    m_itemD = NULL;
    m_itemSite = NULL;
    m_cLinePen = QPen(Qt::red, 2, Qt::DashLine);
    m_cDotPen = QPen(Qt::red, 2, Qt::DashLine);
}

void CMapTrajLine::appendData(QPointF &pf){
    m_vLinepos.append(pf);
}

void CMapTrajLine::clear(){
    m_vLinepos.clear();
    m_vMarkpos.clear();
    drawLine();
}

void CMapTrajLine::setPen(QPen &pen, QPen &dpen){
    m_cLinePen = pen;
    m_cDotPen = dpen;
}

void CMapTrajLine::drawLine(){
    if(m_itemMak != NULL){
        m_scene->removeItem(m_itemMak);
        delete m_itemMak;
        m_itemMak = NULL;
    }

    if(m_itemP != NULL){
        m_scene->removeItem(m_itemP);
        delete m_itemP;
        m_itemP = NULL;
    }

    if(m_itemD != NULL){
        m_scene->removeItem(m_itemD);
        delete m_itemD;
        m_itemD = NULL;
    }


    if(m_itemSite != NULL){
        m_scene->removeItem(m_itemSite);
        delete m_itemSite;
        m_itemSite = NULL;
    }

    if(m_vAllmark.size() != 0){
        for(int i = 0; i < m_vAllmark.size(); i++){
            m_scene->removeItem(m_vAllmark.at(i));
            delete  m_vAllmark[i];
        }
        m_vAllmark.clear();
        m_vMarkTips.clear();
    }

    m_scene->update();

    int pointsize = m_vLinepos.size();

    if(pointsize == 0){
        return;
    }

    QPainterPath ppath;
    QPainterPath dpath;

    QPointF startpos = Bing::latLongToPixelXY(m_vLinepos.at(0).x(), m_vLinepos.at(0).y(), m_level);
    QPointF endpos;
    ppath.moveTo(startpos);
    for (int i = 1; i < m_vLinepos.size(); i++) {
        endpos = Bing::latLongToPixelXY(m_vLinepos.at(i).x(), m_vLinepos.at(i).y(), m_level);
        ppath.lineTo(endpos);
        //ppath.addEllipse(endpos, 1, 1);
        //ppath.lineTo(endpos);
        dpath.moveTo(endpos);
        dpath.addEllipse(endpos, 1, 1);
        //auto *itemL = new QGraphicsLineItem(startpos.x(), startpos.y(), endpos.x(), endpos.y());

    }
    ppath.moveTo(startpos);
    ppath.closeSubpath();
    m_itemP = new QGraphicsPathItem;
    m_itemD = new QGraphicsPathItem;
    //QPen pen;
    //pen.setColor(m_cLineColor);
    //pen.setWidth(1);
    m_itemP->setPen(m_cLinePen);
    m_itemP->setPath(ppath);
    m_scene->addItem(m_itemP);
    m_itemP->show();

    m_itemD->setPen(m_cDotPen);
    m_itemD->setPath(dpath);
    m_scene->addItem(m_itemD);
    m_itemD->show();

    m_itemMak = new QGraphicsPixmapItem;
    m_itemMak->setPixmap(QPixmap(":/img/MarkerAuto.png", "png"));
    m_itemMak->setPos(endpos.x() - 10, endpos.y() - 28);
    m_scene->addItem(m_itemMak);
    m_itemMak->show();

    for(int i = 0; i < m_vMarkpos.size(); i++){
        QPointF markpos = Bing::latLongToPixelXY(m_vMarkpos.at(i).x(), m_vMarkpos.at(i).y(), m_level);
        QGraphicsPixmapItem *itemMak = new QGraphicsPixmapItem;
        m_vAllmark.append(itemMak);
        itemMak->setPixmap(QPixmap(":/img/MarkerAutoGreen.png", "png"));
        itemMak->setPos(markpos.x() - 10, markpos.y() - 28);
        m_scene->addItem(itemMak);
        m_vMarkTips.append(QString::number(m_vMarkpos.at(i).x(), 'f', 8) + "," + QString::number(m_vMarkpos.at(i).y(), 'f', 8));
        itemMak->setToolTip(m_vMarkTips.last());
        itemMak->show();
    }

    //m_itemSite = new QGraphicsTextItem(QString("%1-%2").arg(m_vLinepos.at(pointsize - 1).x()).arg(m_vLinepos.at(pointsize - 1).y()));
    //m_itemSite->setPos(endpos.x() - 20, endpos.y() - 38);
    //m_scene->addItem(m_itemSite);
    //m_itemSite->show();
}
