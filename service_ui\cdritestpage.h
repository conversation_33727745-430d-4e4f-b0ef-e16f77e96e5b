﻿#ifndef CDRITESTPAGE_H
#define CDRITESTPAGE_H

#include <QWidget>
#include <QMap>
#include <QLabel>
#include <QComboBox>
#include <QLCDNumber>
#include <QTimer>
#include <QTimerEvent>
#include <QPaintEvent>

static int max_col_num = 15;

namespace Ui {
class CDriTestPage;
}

/*
* 单口调试页面
*/
class CDriTestPage : public QWidget
{
    Q_OBJECT

public:
    explicit CDriTestPage(QWidget *parent = nullptr);
    ~CDriTestPage();
    void showEvent(QShowEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void timerEvent(QTimerEvent *e) override;
public slots:
    void slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    void slotStatusShow(const QStringList &statuskeys, const QStringList statusValues, const QString sportN);
    void setSeriComs(const QStringList &comlist);


private:
    Ui::CDriTestPage *ui;
    QString m_sComboxPortN;
    QString m_scurrentPortN;
    QList<QLabel *> m_NaLabList;
    QList<QLabel *> m_VaLabList;
    QMap<QString, QStringList> m_slKeyBMap;
    QMap<QString, QLabel *> m_labMap;
    QComboBox *m_combox;
    QMap<QString, QStringList> m_valuesMap;
    QLCDNumber *m_lcdtime;
    QLabel *m_labfreq;
    uint32_t m_ucurrtime;
    int m_iLabHight;
signals:
    void sigToProtocol(const int &type, const QStringList &params);
    void sigUpgPageTime(const QString &str);
};

#endif // CDRITESTPAGE_H
