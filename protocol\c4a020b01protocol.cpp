﻿#include "c4a020b01protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include <QThread>

C4A020B01Protocol::C4A020B01Protocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect C4a0100Protocol";
       connect(this, &C4A020B01Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &C4A020B01Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
       connect(this, &C4A020B01Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);

   }
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加计温度:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度z:"));

   m_iLossFrameSum = 0;
   m_iCheckFailSum = 0;

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);
}

bool C4A020B01Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 4);
    if(barr.size() < sizeof(Stru4a020b)){
        return false;
    }
    m_uMsgLen = sizeof(Stru4a020b);

    if(m_uMsgLen != sizeof(Stru4a020b)){
        m_uMsgLen = sizeof(Stru4a020b);
        return false;
    }

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(4)<<barr.at(5);
}

void C4A020B01Protocol::paseMsg(const QByteArray msg){
    Stru4a020b st_4a020b;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < sizeof(Stru4a020b)){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    if(!sumEorCheck(msg, 4 , 1)){
        qDebug()<<QTime::currentTime().toString()<<"sumEorCheck error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") +QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    ::memcpy(&st_4a020b, msg.data(), sizeof (Stru4a020b));
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;
    dataValues.append(QString::number(st_4a020b.gryotempx * 0.0625, 'f', 2));
    dataValues.append(QString::number(st_4a020b.gryotempy * 0.0625, 'f', 2));
    dataValues.append(QString::number(st_4a020b.gryotempz * 0.0625, 'f', 2));
    //前两个数值表示整数，后面的表示小数
    QString sval = QString::number(st_4a020b.acceltemp);
    sval.insert(2, '.');
    dataValues.append(sval);
    dataValues.append(QString::number(st_4a020b.gyro_x));
    dataValues.append(QString::number(st_4a020b.gyro_y));
    dataValues.append(QString::number(st_4a020b.gyro_z));
    dataValues.append(QString::number(st_4a020b.accel_x));
    dataValues.append(QString::number(st_4a020b.accel_y));
    dataValues.append(QString::number(st_4a020b.accel_z));


    writeCvsFile("4A020B", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }

}

