﻿#include "cplateviewpage.h"
#include "ui_cplateviewpage.h"
#include <QDebug>
#include <QPainter>
#include <QGraphicsScene>

CPlateViewPage::CPlateViewPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CPlateViewPage)
{
    ui->setupUi(this);

    //setWindowFlags(Qt::FramelessWindowHint); // 无边框
    //setAttribute(Qt::WA_TranslucentBackground); // 设置背景透明
    //setStyleSheet("background: transparent;"); // 样式表设置背景透明

    //QPixmap pixmap=QPixmap(":/img/platebackgd.jpeg").scaled(this->size());
    //QPalette palette;
    ////设置窗口背景图片
    //palette.setBrush(QPalette::Window,QBrush(pixmap));
    ////设置窗口背景颜色
    //palette.setColor(QPalette::Window,QColor(255, 150, 30));
    //this->setPalette(palette);

    //创建 QGraphicsScene
    scene_top = new QGraphicsScene;
    scene_side = new QGraphicsScene;
    scene_back = new QGraphicsScene;

    pixmap_top.load(":/img/bsj911top01.png");
    pixmap_side.load(":/img/bsj911side01.png");
    pixmap_back.load(":/img/bsj911back01.png");

    // 创建 QGraphicsPixmapItem 并将图像加载到 QGraphicsPixmapItem 中
    pixmapItem_top = new QGraphicsPixmapItem(pixmap_top);
    pixmapItem_side = new QGraphicsPixmapItem(pixmap_side);
    pixmapItem_back = new QGraphicsPixmapItem(pixmap_back);

    pixmapItem_top->setTransformationMode(Qt::SmoothTransformation);
    pixmapItem_side->setTransformationMode(Qt::SmoothTransformation);
    pixmapItem_back->setTransformationMode(Qt::SmoothTransformation);

    // 设置图像的旋转中心点为图像中心
    pixmapItem_top->setTransformOriginPoint(pixmap_top.width() / 2, pixmap_top.height() / 2);
    pixmapItem_side->setTransformOriginPoint(pixmap_side.width() / 2, pixmap_side.height() / 2);
    pixmapItem_back->setTransformOriginPoint(pixmap_back.width() / 2, pixmap_back.height() / 2);

    // 将 QGraphicsPixmapItem 添加到 QGraphicsScene 中
    scene_top->addItem(pixmapItem_top);
    scene_side->addItem(pixmapItem_side);
    scene_back->addItem(pixmapItem_back);

    //// 创建 QGraphicsView，并将 QGraphicsScene 设置为它的场景
    ui->graphicsView_yaw->setScene(scene_top);
    ui->graphicsView_yaw->setRenderHint(QPainter::Antialiasing);
    ui->graphicsView_pitch->setScene(scene_side);
    ui->graphicsView_pitch->setRenderHint(QPainter::Antialiasing);
    ui->graphicsView_roll->setScene(scene_back);
    ui->graphicsView_roll->setRenderHint(QPainter::Antialiasing);

    m_carplatewd = static_cast<CCarplateWidget *>(ui->wd_scalewd);

    CustomGraphicsView *graphicsView_yaw = ui->graphicsView_yaw;
    CustomGraphicsView *graphicsView_pitch = ui->graphicsView_pitch;
    CustomGraphicsView *graphicsView_roll = ui->graphicsView_roll;
    graphicsView_yaw->initStartSite(0);
    graphicsView_pitch->initStartSite(90);
    graphicsView_roll->initStartSite(90);

    rotationAngle = 0.0;

}

CPlateViewPage::~CPlateViewPage()
{
    delete ui;
}

void CPlateViewPage::paintEvent(QPaintEvent *e){
    QPixmap pixm(":/img/background1.jpeg");
    QPainter painter(this);
    painter.drawPixmap(rect(), pixm);
}

void CPlateViewPage::showplateview(double pitch, double heading, double roll, double speed, double ahheight){
    //qDebug()<<"showplateview"<<roll<<heading<<pitch;
    //testloop += 4.5;
    //roll = testloop + 1.2;
    //heading += testloop + 90.2;
    //pitch += testloop + 180.6;

    pixmapItem_top->setRotation( -1 * heading);
    pixmapItem_side->setRotation(pitch);
    pixmapItem_back->setRotation(roll);

    CustomGraphicsView *graphicsView_yaw = ui->graphicsView_yaw;
    CustomGraphicsView *graphicsView_pitch = ui->graphicsView_pitch;
    CustomGraphicsView *graphicsView_roll = ui->graphicsView_roll;
    graphicsView_yaw->setDegRotate(-1 * heading);
    graphicsView_pitch->setDegRotate(pitch);
    graphicsView_roll->setDegRotate(roll);

    ui->lb_pich->setText(tr("Pich") + ":" + QString::number(pitch, 'g', 4));
    ui->lb_yaw->setText(tr("Yaw") + ":" + QString::number(heading, 'g', 4));
    ui->lb_roll->setText(tr("Roll") + ":" + QString::number(roll, 'g', 4));

    if(qAbs(speed) > 240){
        speed = 240;
    }
    m_carplatewd->showspeedview(speed);
    m_carplatewd->showheightview(ahheight);
}


void CPlateViewPage::showEvent(QShowEvent *event){
    //QPen pen;
    //pen.setColor(Qt::red);
    //QPainterPath toppath;
    //int topw = ui->graphicsView_yaw->width();
    //int toph = ui->graphicsView_yaw->height();
    //int radius =  qMin(topw, toph) / 2;
    ////toppath.moveTo(radius, radius);
    //QRect rect = ui->graphicsView_yaw->geometry();
    //toppath.moveTo(rect.width()/2,height()/2);
    //toppath.arcTo(rect, 0, 360);
    ////toppath.addRect(rect);
    //qDebug()<<"topw-toph:"<<topw<<toph<<radius<<rect;
    //ui->graphicsView_yaw->scene()->addPath(toppath);
}

void CPlateViewPage::resizeEvent(QResizeEvent *event){
    //pixmap_top.scaled(ui->graphicsView_yaw->size(),  Qt::KeepAspectRatio, Qt::SmoothTransformation);
    //qDebug()<<"CPlateViewPage::resizeEvent:"<<ui->graphicsView_yaw->size()<<pixmap_top.size();
    //pixmapItem_top->setPixmap(pixmap_top.scaled(ui->graphicsView_yaw->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));

}
