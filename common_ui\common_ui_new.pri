HEADERS += \
    $$PWD/caboutdlg.h \
    $$PWD/cattitudewidget.h \
    $$PWD/ccollectrecorddialog.h \
    $$PWD/cdeviceconfigwidget.h \
    $$PWD/cdeviceparmwidget.h \
    $$PWD/cfilefieldconfigwidget.h \
    $$PWD/cflyviewpage.h \
    $$PWD/cmapconfigwidget.h \
    $$PWD/cmapviewpage.h \
    $$PWD/cplateviewpage.h \
    $$PWD/cplaybackwidget.h \
    $$PWD/ctimerconfigwidget.h \
    $$PWD/cverticalscalwidget.h \
    $$PWD/cyawindwidge.h

SOURCES += \
    $$PWD/caboutdlg.cpp \
    $$PWD/cattitudewidget.cpp \
    $$PWD/ccollectrecorddialog.cpp \
    $$PWD/cdeviceconfigwidget.cpp \
    $$PWD/cdeviceparmwidget.cpp \
    $$PWD/cfilefieldconfigwidget.cpp \
    $$PWD/cflyviewpage.cpp \
    $$PWD/cmapconfigwidget.cpp \
    $$PWD/cmapviewpage.cpp \
    $$PWD/cplateviewpage.cpp \
    $$PWD/cplaybackwidget.cpp \
    $$PWD/ctimerconfigwidget.cpp \
    $$PWD/cverticalscalwidget.cpp \
    $$PWD/cyawindwidge.cpp

FORMS += \
    $$PWD/caboutdlg.ui \
    $$PWD/ccollectrecorddialog.ui \
    $$PWD/cdeviceconfigwidget.ui \
    $$PWD/cdeviceparmwidget.ui \
    $$PWD/cfilefieldconfigwidget.ui \
    $$PWD/cflyviewpage.ui \
    $$PWD/cmapconfigwidget.ui \
    $$PWD/cmapviewpage.ui \
    $$PWD/cplateviewpage.ui \
    $$PWD/cplaybackwidget.ui \
    $$PWD/ctimerconfigwidget.ui
