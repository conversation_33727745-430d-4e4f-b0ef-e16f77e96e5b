﻿#ifndef CCDDC0BPROTOCOL_H
#define CCDDC0BPROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class CCDDC0BProtocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit CCDDC0BProtocol(QObject *parent = nullptr);
    bool setProtoLength(const QByteArray &barr);
    bool preInit();
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strucddc0b{
        uint8_t header[3];	//0xcd,0xdc,0x0b
        short roll;		    //横滚角
        short pitch;		//俯仰角
        short azimuth;	    //航向角
        short gyrox;		//陀螺x轴
        short gyroy;		//陀螺y轴
        short  gyroz;		//陀螺z轴
        short accelx;		//校准加速度x轴
        short accely;		//校准加速度y轴
        short accelz;		//校准加速度z轴
        uint8_t xor_verify2;
    }Strucddc0b;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);

signals:

};

#endif // CCDDC0BPROTOCOL_H
