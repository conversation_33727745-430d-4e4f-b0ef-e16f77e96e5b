﻿#include "ca5a500protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cdataanapage.h"
#include "cgyrocalibpage.h"
#include "cdrivetestpage.h"
#include "ctlhtools.h"
#include "cprotocolfactory.h"
#include <QDebug>

CA5A500Protocol::CA5A500Protocol(QObject *parent) : CBaseProtocol(parent)
{
     CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
     CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));
     CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
     CGyroCalibPage *gyropage = static_cast<CGyroCalibPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_GyroCalib"));
    if(dripage != NULL){
        qDebug()<<"connect CA5A500Protocol";
        connect(this, &CA5A500Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &CA5A500Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
        connect(this, &CA5A500Protocol::sigDataUpdate,  drivepage, &CDriveTestPage::slotDataShow);
        connect(this, &CA5A500Protocol::sigChartsUpdate, danapage, &CDataAnaPage::slotDataShow);
        connect(this, &CA5A500Protocol::sigFreqUpdate, gyropage, &CGyroCalibPage::slotDataShow);
        connect(this, &CA5A500Protocol::sigDestory, gyropage, &CGyroCalibPage::slotDestory);
    }
    //m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
    //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺:"));
    //m_slProtoKeys.append(QString::fromLocal8Bit("温度:"));
    //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺平均值:"));
    //m_slProtoKeys.append(QString::fromLocal8Bit("温度平均值:"));

    //m_slFreqKeys.append(QString::fromLocal8Bit("采样序号:"));
    //m_slFreqKeys.append(m_slProtoKeys.mid(2, 2));

    m_iFramIndx = -1;
    setFrameErr(E_FRAME_OK);
    m_icatFreqCounts = 0;
    m_lcatCurrCounts = 0l;
    m_davggyr = 0.0;
    m_davgtemp = 0.0;
}

bool CA5A500Protocol::setProtoLength(const QByteArray &barr){
    m_slProtoKeys =  CProtocolFactory::getProKeys(m_iProtoIndex);
    m_slFreqKeys.append(m_slProtoKeys.mid(2, 2));
    m_bMsgHead = barr.mid(0, 2);
    m_uMsgLen = sizeof (Strua5a500);

    return true;
}

CA5A500Protocol::~CA5A500Protocol(){
    qDebug()<<"~CA5A500Protocol";
}

void CA5A500Protocol::paseMsg(const QByteArray msg){
    Strua5a500 st_a5a500;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < sizeof (Strua5a500)){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;
    //qDebug()<<"CA5A500Protocol1111"<<sizeof (st_a5a500);
    QByteArray tempmsg = msg;
    reserverData(tempmsg, 2, 4);
    reserverData(tempmsg, 6, 2);

    ::memcpy(&st_a5a500, tempmsg.data(), m_uMsgLen);

    //char buff[10] = {0};
    //::memcpy(buff, &st_a5a500.temp, 2);
    //buff[0] = 0xfa;
    //buff[1] = 0x01;
    //::memcpy(&st_a5a500.temp, buff, 2);
    //qDebug()<<"buff:"<<QByteArray(buff).toHex(' ')<<st_a5a500.temp;

    //uint16_t currentindx = st_a5a500.pgidex;
    //qDebug()<<"code:"<<st_aa6600.st921info.selftestingcode;
    //if(m_iFramIndx != -1 && ((m_iFramIndx + 1) % m_iFpgaFc != currentindx)){
    //    qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN;
    //    qDebug()<< m_sMsgCache.toHex();
    //    qDebug()<< msg.toHex();
    //    m_iLossFrameSum++;
    //    setFrameErr(E_FRAME_LOSS);
    //    if(m_bIsNeedShowL){
    //        emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(QString::fromLocal8Bit("丢帧数:%1").arg(m_iLossFrameSum)), m_sPortN);
    //        m_bIsNeedShowL = false;
    //    }
    //}

    //异或校验
    if(!sumEorCheck(msg, 2 , 1)){
    //和校验
    //if(!sum8CheckSum(msg, 2, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sum8CheckSum error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        //return;
    }
    //qDebug()<<"CA5A500Protocol1113"<<st_a5a500.temp<<st_a5a500.flog;
    m_sMsgCache.clear();
    m_sMsgCache.append(msg);
    //m_iFramIndx = currentindx;

    QMap<QString, QString> divisor;
    CProtoParamData::getProtocolDivisor(m_iProtoIndex, divisor);

    double fog = 0.0;
    float temp = 0.0;
    if(divisor.contains(u8"陀螺:")){
        fog = CTlhTools::calDivisor(st_a5a500.flog, divisor[u8"陀螺:"]);
    }else{
        //fogx = st_b55b00.fogx/80.00;
        fog = st_a5a500.flog;
    }

    //dataValues.append(QString::number(st_a5a500.pgidex));
    dataValues.append(QString::number(fog, 'f', 6));
    if(st_a5a500.temp >> 15){
        temp = -1 * complement2original(st_a5a500.temp & 0x0000FFFF);
    }else{
        temp = st_a5a500.temp;
    }

    float fogtemp = 0.0;
    if(divisor.contains(u8"温度:")){
        fogtemp = CTlhTools::calDivisor(temp, divisor[u8"温度:"]);
    }else{
        fogtemp = temp/16.00;
    }

    //qDebug()<<"temp1:"<<st_a5a500.temp<<temp;
    dataValues.append(QString::number(fogtemp, 'f', 2));
    QStringList orikeys;
    orikeys.append(m_slProtoKeys.mid(0, 2));
    writeCvsFile("A5A5", orikeys, dataValues);

    //生成频率采样文件
    //qDebug()<<"m_icatFreqCounts:"<<m_lcatCurrCounts<<m_icatFreqCounts;
    if(m_icatFreqCounts != 0 && (++m_lcatCurrCounts % m_icatFreqCounts == 0) ){
        m_vGyrDatas.append(fog);
        m_vTempDatas.append(fogtemp);
        m_davggyr = CTlhTools::getAverage(m_vGyrDatas);
        m_davgtemp = CTlhTools::getAverage(m_vTempDatas);
        //qDebug()<<"A5A5:"<<st_a5a500.flog<<m_davggyr<<m_vGyrDatas.size();
        QStringList slwriteval;
        slwriteval<<QString::number(m_davggyr, 'f', 6) << QString::number(m_davgtemp, 'f', 2);
        QVector<double> vddatas;
        //vddatas.append(m_lcatCurrCounts);
        vddatas.append(m_davggyr);
        vddatas.append(m_davgtemp);
        //qDebug()<<slwriteval;
        writeCvsFile("A5A5_C" + QString::number(m_icatFreqCounts), m_slFreqKeys, slwriteval, 1);
        emit sigFreqUpdate(m_slFreqKeys, vddatas, m_sPortN, m_iProtoIndex);
        m_vGyrDatas.clear();
        m_vTempDatas.clear();
    }else{
        m_vGyrDatas.append(fog);
        m_vTempDatas.append(fogtemp);
    }

    dataValues.append(QString::number(m_davggyr));
    dataValues.append(QString::number(m_davgtemp));

    if(m_bIsNeedShow){
        if(m_slProtoKeys.size() != dataValues.size()){
            return;
        }
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }else{

    }
    m_bIsNeedKeys = false;

    QVector<double> vddata;
    //vddata.append(st_a5a500.pgidex);
    vddata.append(fog);
    vddata.append(fogtemp);
    vddata.append(m_davggyr);
    vddata.append(m_davgtemp);

    emit sigChartsUpdate(m_slProtoKeys, vddata, m_sPortN, m_iProtoIndex);

}

bool CA5A500Protocol::preInit(){
    CBaseProtocol::preInit();
    qDebug()<<"m_catFreqCounts"<<m_icatFreqCounts<<m_dCatFreq;
    return true;
}
