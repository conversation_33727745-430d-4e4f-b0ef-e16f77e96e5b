﻿#include "cdataanapage.h"
#include "ui_cdataanapage.h"
#include "cloadfiletaskdlg.h"
#include <QFile>

#define MAX_WAVE_NUM 5

CDataAnaPage::CDataAnaPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CDataAnaPage)
{
    ui->setupUi(this);

    m_slcfirstwid = new CScrollLineChart(ui->frame);
    m_slcsecondwid = new CScrollLineChart(ui->frame);
    m_slcthirdwid = new CScrollLineChart(ui->frame);

    m_lwFirstwid = new QListWidget(ui->frame);
    m_lwSecondwid = new QListWidget(ui->frame);
    m_lwThirdwid = new QListWidget(ui->frame);

    m_iCurrentDataindex = 0;

    m_vGraphNum.append(0);
    m_vGraphNum.append(0);
    m_vGraphNum.append(0);
    m_vGraphNum.append(0);

    //窗口顺序
    /*1 3
     *2 4*/

    m_vSlcWids.push_back(m_slcfirstwid);
    m_vSlcWids.push_back(m_slcsecondwid);
    m_vSlcWids.push_back(m_slcthirdwid);

    m_vLwcWids.push_back(m_lwFirstwid);
    m_vLwcWids.push_back(m_lwSecondwid);
    m_vLwcWids.push_back(m_lwThirdwid);

    m_lwFirstwid->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_lwFirstwid->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_lwSecondwid->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_lwSecondwid->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_lwThirdwid->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_lwThirdwid->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    QStringList initstrlist;
    for(int i = 0; i < 60; i++){
        initstrlist.append("0.0");
    }
    m_vslLwData.append(initstrlist);
    m_vslLwData.append(initstrlist);
    m_vslLwData.append(initstrlist);

    m_lwFirstwid->addItems(m_vslLwData[0]);
    m_lwSecondwid->addItems(m_vslLwData[1]);
    m_lwThirdwid->addItems(m_vslLwData[2]);

    m_vvlwItems.append(QVector<QListWidgetItem *>());
    m_vvlwItems.append(QVector<QListWidgetItem *>());
    m_vvlwItems.append(QVector<QListWidgetItem *>());

    for(int i = 0; i < initstrlist.size(); i++){
        m_vvlwItems[0].append(m_lwFirstwid->item(i));
        m_vvlwItems[1].append(m_lwSecondwid->item(i));
        m_vvlwItems[2].append(m_lwThirdwid->item(i));
        m_lwFirstwid->item(i)->setTextAlignment(Qt::AlignHCenter);
        m_lwSecondwid->item(i)->setTextAlignment(Qt::AlignHCenter);
        m_lwThirdwid->item(i)->setTextAlignment(Qt::AlignHCenter);
    }

    QVBoxLayout *vboxlayout = new QVBoxLayout;
    QHBoxLayout *fvboxlayout = new QHBoxLayout;
    QHBoxLayout *sboxlayout = new QHBoxLayout;
    QHBoxLayout *tboxlayout = new QHBoxLayout;
    fvboxlayout->addWidget(m_slcfirstwid);
    fvboxlayout->addWidget(m_lwFirstwid);
    fvboxlayout->addStretch();
    fvboxlayout->setStretch(0, 10);
    fvboxlayout->setStretch(1, 1);
    fvboxlayout->setMargin(2);
    sboxlayout->addWidget(m_slcsecondwid);
    sboxlayout->addWidget(m_lwSecondwid);
    sboxlayout->addStretch();
    sboxlayout->setStretch(0, 10);
    sboxlayout->setStretch(1, 1);
    sboxlayout->setMargin(2);
    tboxlayout->addWidget(m_slcthirdwid);
    tboxlayout->addWidget(m_lwThirdwid);
    tboxlayout->addStretch();
    tboxlayout->setStretch(0, 10);
    tboxlayout->setStretch(1, 1);
    tboxlayout->setMargin(2);
    vboxlayout->addLayout(fvboxlayout);
    vboxlayout->addLayout(sboxlayout);
    vboxlayout->addLayout(tboxlayout);
    vboxlayout->setSpacing(0);
    vboxlayout->setMargin(0);
    ui->frame->setLayout(vboxlayout);

    //m_slcfirstwid->startFlush();
    //m_slcsecondwid->startFlush();
    //m_slcthirdwid->startFlush();

    m_mWidDataRalation[0] = QVector<int>(1, 0); //默认第一个窗口显示第0个参数（初始化为1个数据，数据为0）
    m_mWidDataRalation[1] = QVector<int>(1, 1); //默认第二个窗口显示第1个参数
    m_mWidDataRalation[2] = QVector<int>(1, 2); //默认第三个窗口显示第2个参数
    m_sCurrentPortN = "";
    m_tCode = QTextCodec::codecForName("GBK");

    //默认为在线模式，禁用导入按钮
    m_bIsOnline = true;
    ui->bt_openfile->setDisabled(true);
    //默认开启三窗口
    ui->cb_widsum->setCurrentIndex(2);

    ui->bt_showrange->setDisabled(true);

    m_fMarinrate = 0.05;

    m_iprotoindex = -1;
}

CDataAnaPage::~CDataAnaPage()
{
    delete ui;
}

void CDataAnaPage::on_bt_gencursor_clicked()
{
    int iselwid = ui->cb_widnum->currentIndex();
    if(iselwid < m_vSlcWids.size()){
        m_vSlcWids[iselwid]->genCursor();
    }else{
        m_slcfirstwid->genCursor();
        m_slcsecondwid->genCursor();
        m_slcthirdwid->genCursor();
    }
}

void CDataAnaPage::on_bt_delcursor_clicked()
{
    int iselwid = ui->cb_widnum->currentIndex();
    if(iselwid < m_vSlcWids.size()){
        m_vSlcWids[iselwid]->delCursor();
    }else{
        m_slcfirstwid->delCursor();
        m_slcsecondwid->delCursor();
        m_slcthirdwid->delCursor();
    }
}

void CDataAnaPage::on_cb_widsum_currentIndexChanged(int index)
{
    bool bres = true;
    //单窗口时，当前选择哪个窗口则保留哪个窗口
    if(index == 0){
        int iselwid = ui->cb_widnum->currentIndex();
        //qDebug()<<"on_cb_widsum_currentIndexChanged:"<<index<<iselwid<<m_vSlcWids.size();
        for(int i = 0; i < m_vSlcWids.size(); i++){
            if( i != iselwid){
                if(m_bIsOnline){
                    m_vSlcWids.at(i)->stopFlush();
                }
                setWidVisibel(i, false);
            }else{
                if(m_bIsOnline){
                    m_vSlcWids.at(i)->startFlush();
                }
                setWidVisibel(i, true);
            }
        }
    }else{
        for(int i = 0; i < m_vSlcWids.size(); i++){
             bres = i < index + 1;
             setWidVisibel(i, bres);
             if(bres && m_bIsOnline){
                 m_vSlcWids.at(i)->startFlush();
             }else{
                 m_vSlcWids.at(i)->stopFlush();
             }
        }
    }
}

void CDataAnaPage::slotDataShow(const QStringList datakeys, const QVector<double> dataValues, const QString sportN, const int protoindex){
    //qDebug()<<datakeys.size()<<m_slKeys.size()<<m_bIsOnline;
    if(m_bIsOnline == false || datakeys.size() == 0){
        return;
    }

    if(m_sCurrentPortN == ""){
        ui->cb_portn->addItem(sportN);
        m_sCurrentPortN = sportN;
        m_iprotoindex = protoindex;
    }

    if(m_sCurrentPortN != sportN){
        m_slKeys.clear();
        if(ui->cb_portn->findText(sportN) == -1){
            ui->cb_portn->addItem(sportN);
        }
        return;
    }

    //串口相同，协议不同进行切换
    if(m_iprotoindex != -1 && m_iprotoindex != protoindex){
        on_ch_realttdata_stateChanged(0);
        m_iprotoindex = protoindex;
    }

    if(m_slKeys.size() == 0 ){
            m_slKeys.clear();
            for(QString skey:datakeys){
                m_slKeys.append(skey.remove(':'));
            }
            ui->cb_showparm->clear();
            ui->cb_showparm->addItems(m_slKeys);
    }

    //qDebug()<<dataValues.at(1);
    for(int k = 0; k < m_mWidDataRalation.keys().size() && k < m_slKeys.size(); k++){
        int ikey = m_mWidDataRalation.keys().at(k);
        int keysize = m_mWidDataRalation[ikey].size();
        //qDebug()<<"keysize:"<<keysize<<ikey;
        if(m_vSlcWids[ikey]->isVisible() && keysize > 0){
            for(int i = 0; i < m_mWidDataRalation[ikey].size(); i++){
                m_vSlcWids[ikey]->appendData(dataValues.at(m_mWidDataRalation[ikey].at(i)), i);
                m_vSlcWids[ikey]->setTitleName(m_slKeys[m_mWidDataRalation[ikey].at(i)], i);
            }
            m_vslLwData[ikey].removeFirst();
            if(dataValues.at(m_mWidDataRalation[ikey].at(0)) > 1000){
                m_vslLwData[ikey].append(QString("%1").arg(dataValues.at(m_mWidDataRalation[ikey].at(0)), 10, 'f', 2));
            }else{
                m_vslLwData[ikey].append(QString("%1").arg(dataValues.at(m_mWidDataRalation[ikey].at(0)), 10, 'f', 6));
            }
            for(int i = 0; i < m_vslLwData[ikey].size(); i++){
                m_vvlwItems[ikey].at(i)->setText(m_vslLwData[ikey].at(i));
            }
        }
    }

}

void CDataAnaPage::on_cb_showparm_currentIndexChanged(int index)
{
    QVector<int> vcurent = m_mWidDataRalation[ui->cb_widnum->currentIndex()];
    if(index > -1 && vcurent.last() != index && m_slKeys.size() > index){
        //if(!m_bIsOnline && ui->ch_overlying->isChecked()){
        //清除已有数据
        m_vSlcWids[ui->cb_widnum->currentIndex()]->reset();
        if(ui->ch_overlying->isChecked()){
            //叠加显示波形，最多叠加五个
            if(m_mWidDataRalation[ui->cb_widnum->currentIndex()].size() == MAX_WAVE_NUM){
                QMessageBox::information(this, tr("Info"), tr("Maximum support") + QString("%1").arg(MAX_WAVE_NUM) + tr("Waveform overlay display"));
                return;
            }
            m_mWidDataRalation[ui->cb_widnum->currentIndex()].append(index);
            m_vSlcWids[ui->cb_widnum->currentIndex()]->setTotalGraph(m_mWidDataRalation[ui->cb_widnum->currentIndex()].size());
            m_vSlcWids[ui->cb_widnum->currentIndex()]->setTitleName(m_slKeys[index], m_mWidDataRalation[ui->cb_widnum->currentIndex()].size() - 1);
        }else{
            //替换原波形数据，替换只能替换第一条波形
            m_vSlcWids[ui->cb_widnum->currentIndex()]->setTotalGraph(1);
            m_mWidDataRalation[ui->cb_widnum->currentIndex()][0] = index;
            m_mWidDataRalation[ui->cb_widnum->currentIndex()].resize(1);
            m_vSlcWids[ui->cb_widnum->currentIndex()]->setTitleName(m_slKeys[index], 0);
        }
    }
    qDebug()<<"on_cb_showparm_currentIndexChanged";
}

void CDataAnaPage::on_bt_stoppaint_clicked()
{
    int iselwid = ui->cb_widnum->currentIndex();
    qDebug()<<iselwid;
    if(iselwid < m_vSlcWids.size()){
        m_vSlcWids[iselwid]->stopFlush();
    }else{
        m_slcfirstwid->stopFlush();
        m_slcsecondwid->stopFlush();
        m_slcthirdwid->stopFlush();
    }
}

void CDataAnaPage::on_bt_startpaint_clicked()
{
    int iselwid = ui->cb_widnum->currentIndex();
    if(iselwid < m_vSlcWids.size()){
        m_vSlcWids[iselwid]->startFlush();
    }else{
        m_slcfirstwid->startFlush();
        m_slcsecondwid->startFlush();
        m_slcthirdwid->startFlush();
    }
}

void CDataAnaPage::on_bt_openfile_clicked()
{
    QString currentDir = QDir::currentPath();
    m_sWorkFile = QFileDialog::getOpenFileName(this, tr("Open File"), currentDir, tr("Csv files (*.csv)"));
    if(!m_sWorkFile.isEmpty()){
        ui->ed_filename->setText(m_sWorkFile);
    }
}

void CDataAnaPage::on_bt_loadfile_clicked()
{
    m_bIsOnline = false;
    QFile file(m_sWorkFile);
    if(!file.open(QIODevice::ReadOnly)){
        QMessageBox::critical(this, tr("Error"), tr("Fail to open file") + ":" + file.errorString());
        return;
    }

    QByteArray arr;
    arr = file.readLine();
    if(arr.size() == 0){
        QMessageBox::critical(this, tr("Error"), tr("File read failure") + ":" + file.errorString());
        return;
    }
    file.close();

    QString utfdata = m_tCode->toUnicode(arr);

    auto keysread = utfdata.split("\",\"");
    if(keysread.size() < 2){
        keysread = utfdata.split(',');
    }
    keysread.replaceInStrings("\"", "");
    keysread.replaceInStrings(":", "");
    keysread.replaceInStrings("\n", "");

    int iwidth = 160;
    int iheight = 40;
    int ipadding = 10;
    int colnum = 8;
    int keynum = keysread.size();

    m_keysDlg = new QDialog(this);
    m_keysDlg->setModal(true);
    for(int i = 0; i < keynum; i++){
        QCheckBox *keybox = new QCheckBox(m_keysDlg);
        m_vCheckbox.append(keybox);
        keybox->setText(keysread.at(i));
        keybox->resize(iwidth, iheight);
        keybox->move(ipadding + (i / colnum) * iwidth , ipadding + (i % colnum) * iheight);
    }

    QPushButton *keybutton = new QPushButton(m_keysDlg);
    keybutton->setText(tr("Sure"));
    keybutton->resize(iwidth, iheight);
    if(keynum % colnum != 0){
        keybutton->move(ipadding + (keynum / colnum) * iwidth , ipadding + (colnum - 1) * iheight);
    }else{
        keybutton->move(ipadding + (keynum / colnum + 1) * iwidth , ipadding);
    }

    m_keysDlg->show();
    connect(keybutton, &QPushButton::clicked, this, &CDataAnaPage::slotLoadFile);

}

void CDataAnaPage::slotLoadFile(){
    for (int i = 0; i < m_vslData.size();i++) {
        delete m_vslData.at(i);
    }
    m_vslData.clear();
    qDebug()<<"slotLoadFile"<<m_vCheckbox.size();
    //QDialog *lfdlg = new QDialog;
    m_slKeys.clear();
    ui->cb_showparm->clear();
    //ui->cb_showparm->setDisabled(true);
    QVector<int> vselindex;
    for(int i = 0; i < m_vCheckbox.size(); i++){
        if(m_vCheckbox.at(i)->isChecked() && vselindex.size() < 4){
            vselindex.push_back(i);
            m_vslData.push_back(new QList<double>);
            m_slKeys.append(m_vCheckbox.at(i)->text().remove(':'));
            ui->cb_showparm->addItem(m_vCheckbox.at(i)->text());
        }
        delete m_vCheckbox.at(i);
    }
    m_keysDlg->close();
    CLoadFileTaskDlg *lfdlg = new CLoadFileTaskDlg(this);
    connect(lfdlg, &CLoadFileTaskDlg::sigReadFileEnd, this, &CDataAnaPage::slotReadFileEnd);
    lfdlg->loadFileDetail(m_sWorkFile, vselindex, m_vslData);
    lfdlg->show();
    m_vCheckbox.clear();
}

void CDataAnaPage::slotReadFileEnd(){

    qDebug()<<m_vslData.size()<<m_slKeys<<m_bIsOnline<<m_vLwcWids[0]->isVisible()<<m_vLwcWids[1]->isVisible()<<m_vLwcWids[2]->isVisible();
    //qDebug()<<*(m_vslData.at(1));
    int parmnum = m_vslData.size() > MAX_WAVE_NUM ? MAX_WAVE_NUM : m_vslData.size(); //最多同时支持5条曲线

    if(ui->ch_overlying->isChecked()){
        m_vSlcWids[0]->setTotalGraph(parmnum);
        for (int i = 0;i < parmnum;i++) {
            m_vSlcWids[0]->setTitleName(m_slKeys[i], i);
        }
        m_vSlcWids[0]->reset();
        for (int i = 0; i < m_vslData.at(0)->size(); i++) {
            for (int j = 0;j < parmnum;j++) {
                m_vSlcWids[0]->appendData(m_vslData.at(j)->value(i), j);
            }
        }

        ui->cb_widsum->setCurrentIndex(0);

        m_vSlcWids[0]->stopFlush();
    }else{
        switch(parmnum){
        case 3:
            m_vSlcWids[2]->setTitleName(m_slKeys[2], 0);
            m_vSlcWids[2]->reset();
        case 2:
            m_vSlcWids[1]->setTitleName(m_slKeys[1], 0);
            m_vSlcWids[1]->reset();
        case 1:
            m_vSlcWids[0]->setTitleName(m_slKeys[0], 0);
            m_vSlcWids[0]->reset();
            break;
        default:
            return;
        }

        ui->cb_widsum->setCurrentIndex(parmnum - 1);

        for (int i = 0; i < m_vslData.at(0)->size(); i++) {
            switch(parmnum){
            case 3:
                m_vSlcWids[2]->appendData(m_vslData.at(2)->value(i), 0);
            case 2:
                m_vSlcWids[1]->appendData(m_vslData.at(1)->value(i), 0);
            case 1:
                m_vSlcWids[0]->appendData(m_vslData.at(0)->value(i), 0);
            default:
                break;
            }
        }

        switch(parmnum){
        case 3:
            //delete m_vslData.at(2);
            m_vSlcWids[2]->stopFlush();
        case 2:
            //delete m_vslData.at(1);
            m_vSlcWids[1]->stopFlush();
        case 1:
            //delete m_vslData.at(0);
            m_vSlcWids[0]->stopFlush();
            break;
        default:
            return;
        }
    }
}

void CDataAnaPage::on_ch_realttdata_stateChanged(int arg1)
{
    m_slKeys.clear();
    for (int i = 0; i < m_vSlcWids.size(); i++) {
        m_vSlcWids[i]->reset();
        m_vSlcWids[i]->setFlushStatus(ui->ch_realttdata->isChecked());
        if(ui->ch_realttdata->isChecked()){
            if(m_vSlcWids[i]->isVisible()){
                m_vLwcWids[i]->setHidden(false);
            }
            m_vSlcWids[i]->reset();
            m_vSlcWids[i]->setValidScrollBar(false);
            ui->bt_openfile->setDisabled(true);
            ui->bt_showrange->setDisabled(true);
            m_vSlcWids[i]->startFlush();
        }else{
            m_vSlcWids[i]->ressetHRange(0, 0, 0.05);
            m_vSlcWids[i]->ressetVRange(0, 0, 0.05);
            m_vSlcWids[i]->reset();
            m_vLwcWids[i]->setHidden(true);
            m_vSlcWids[i]->setValidScrollBar(true);
            ui->bt_openfile->setDisabled(false);
            ui->bt_showrange->setDisabled(false);
        }
    }

    if(ui->ch_realttdata->isChecked()){
        m_bIsOnline = true;
    }else{
        m_bIsOnline = false;
    }
}

void CDataAnaPage::on_bt_showrange_clicked()
{
    QPointer<QDialog> showdlg = new QDialog(this);
    showdlg->setModal(true);
    showdlg->resize(461, 244);
    QVBoxLayout *vboxlay = new QVBoxLayout(showdlg);
    QStringList drectname = {tr("Horizontal"), tr("Vertical")};
    QVector<QLineEdit *> vlineedit;
    int iselwid = ui->cb_widnum->currentIndex();
    QVector<double> varange;
    m_vSlcWids[iselwid]->getDataRange(varange);
    for(int i = 0, j = 0; i < 2; i++){
        QHBoxLayout *hhboxlay = new QHBoxLayout;
        QGroupBox *hgroupbox = new QGroupBox(drectname.at(i), showdlg);
        QLabel *labbegin = new QLabel(tr("Minimum"),hgroupbox);
        QLabel *labend = new QLabel(tr("Maximum"),hgroupbox);
        QLabel *labmargin = new QLabel(tr("Margin"), hgroupbox);
        QLineEdit *lebegin = new QLineEdit(QString::number(varange.at(j++)), hgroupbox);
        vlineedit.append(lebegin);
        lebegin->setFixedSize(100, 40);
        QLineEdit *leend = new QLineEdit(QString::number(varange.at(j++)),hgroupbox);
        vlineedit.append(leend);
        leend->setFixedSize(100, 40);
        QLineEdit *lemargin = new QLineEdit(QString::number(varange.at(j++)),hgroupbox);
        vlineedit.append(lemargin);
        lemargin->setFixedSize(80, 40);
        hhboxlay->addWidget(labbegin);
        hhboxlay->addWidget(lebegin);
        hhboxlay->addWidget(labend);
        hhboxlay->addWidget(leend);
        hhboxlay->addWidget(labmargin);
        hhboxlay->addWidget(lemargin);
        hhboxlay->setContentsMargins(0, 20, 0, 20);
        hgroupbox->setLayout(hhboxlay);
        vboxlay->addWidget(hgroupbox);
    }
    QHBoxLayout *hhboxlay = new QHBoxLayout;
    QPushButton *confirm = new QPushButton(tr("Sure"), showdlg);
    QPushButton *breset = new QPushButton(tr("Reset"), showdlg);
    QPushButton *cancel = new QPushButton(tr("Cancel"), showdlg);
    hhboxlay->addStretch();
    hhboxlay->addWidget(confirm);
    hhboxlay->addWidget(breset);
    hhboxlay->addWidget(cancel);
    hhboxlay->addStretch();
    vboxlay->addLayout(hhboxlay);
    m_phShowRange.clear();
    m_pvShowRange.clear();
    connect(breset, &QPushButton::clicked, this, [=](){
        vlineedit.at(0)->setText("0");
        vlineedit.at(1)->setText("0");
        vlineedit.at(2)->setText("0.0");
        vlineedit.at(3)->setText("0");
        vlineedit.at(4)->setText("0");
        vlineedit.at(5)->setText("0.05");
    });
    connect(confirm, &QPushButton::clicked, this, [=](){
        showdlg->close();
        m_phShowRange.append(vlineedit.at(0)->text().toDouble());
        m_phShowRange.append(vlineedit.at(1)->text().toDouble());
        m_phShowRange.append(vlineedit.at(2)->text().toDouble());
        m_pvShowRange.append(vlineedit.at(3)->text().toDouble());
        m_pvShowRange.append(vlineedit.at(4)->text().toDouble());
        m_pvShowRange.append(vlineedit.at(5)->text().toDouble());

        if(m_phShowRange.at(0) > m_phShowRange.at(1) || m_pvShowRange.at(0) > m_pvShowRange.at(1)){
            m_phShowRange.clear();
            m_pvShowRange.clear();
            QMessageBox::critical(this, tr("Error"), tr("Invalid input range!"));
            return;
        }

        if(m_phShowRange.at(0) < m_phShowRange.at(1) && !m_bIsOnline){
            m_vSlcWids[iselwid]->ressetHRange(m_phShowRange.at(0), m_phShowRange.at(1), m_phShowRange.at(2));
        }else{
            m_vSlcWids[iselwid]->ressetHRange(0, 0, m_phShowRange.at(2));
        }
        if(m_pvShowRange.at(0) < m_pvShowRange.at(1)){
            qDebug()<<"m_phShowRange:"<<m_phShowRange;
            m_vSlcWids[iselwid]->ressetVRange(m_pvShowRange.at(0), m_pvShowRange.at(1), m_pvShowRange.at(2));
        }else{
            //上限下限设置为0，表示按原始数据绘制
            m_vSlcWids[iselwid]->ressetVRange(0, 0, m_pvShowRange.at(2));
        }
        qDebug()<<"m_phShowRange"<<m_phShowRange<<m_pvShowRange<<m_vslData.at(0)->size();
        m_vSlcWids[iselwid]->reset();
        for (int i = 0; i < m_vslData.at(0)->size(); i++) {
            m_vSlcWids[iselwid]->appendData(m_vslData.at(iselwid)->value(i), 0);
        }
        m_vSlcWids[iselwid]->stopFlush();
    });
    connect(cancel, &QPushButton::clicked, this, [=](){
        showdlg->close();
    });
    showdlg->show();
}

void CDataAnaPage::setWidVisibel(int index, bool isshow){
    m_vSlcWids[index]->setVisible(isshow);
    if(m_bIsOnline){
        m_vLwcWids[index]->setVisible(isshow);
    }else{
        m_vLwcWids[index]->setVisible(false);
    }
}

void CDataAnaPage::on_cb_portn_currentIndexChanged(const QString &arg1)
{
    m_sCurrentPortN = arg1;
    m_slKeys.clear();

    m_vSlcWids[2]->reset();
    m_vSlcWids[1]->reset();
    m_vSlcWids[0]->reset();
}

void CDataAnaPage::on_cb_widsize_currentIndexChanged(const QString &srange)
{
    m_vSlcWids[2]->setWindRange(srange.toInt());
    m_vSlcWids[1]->setWindRange(srange.toInt());
    m_vSlcWids[0]->setWindRange(srange.toInt());
}

//当单窗口显示时，选择哪个窗口则显示哪个窗口的数据
void CDataAnaPage::on_cb_widnum_currentIndexChanged(int index)
{
    int sum_index = ui->cb_widsum->currentIndex();
    if(sum_index == 0){
        on_cb_widsum_currentIndexChanged(sum_index);
    }

}
