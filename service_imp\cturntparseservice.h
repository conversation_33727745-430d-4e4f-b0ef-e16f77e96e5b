﻿#ifndef CTUREBLEPARSESERVICE_H
#define CTUREBLEPARSESERVICE_H

#include <QObject>
#include <QMultiMap>

typedef enum{
    E_INPOS,
    E_INSPEED,
    E_INSWAY,
    E_INFZ,
    E_INSTOP,
    E_OUTPOS,
    E_OUTSPEED,
    E_OUTSWAY,
    E_OUTFZ,
    E_OUTSTOP,
    E_DELY
}E_TURNCMD;

typedef QPair<E_TURNCMD, QStringList> ES_PAIR;

/*
* 转台自动脚本解析类
*/

class CTurnTParseService : public QObject
{
    Q_OBJECT
public:
    explicit CTurnTParseService(QObject *parent = nullptr);
    bool axleParse(int cmd_type, const QStringList &sitem, QList<ES_PAIR> &Cmdlist);
    bool turnTableTextParse(const QString &text, QList<ES_PAIR> &Cmdlist);
    const QMultiMap<E_TURNCMD, QStringList> &getParseData();

signals:
private:
};

#endif // CTUREBLEPARSESERVICE_H
