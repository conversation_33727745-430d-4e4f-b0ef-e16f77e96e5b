HEADERS += \
    $$PWD/cconfigmanager.h \
    $$PWD/cloadfiletaskdlg.h \
    $$PWD/cprotocolfactory.h \
    $$PWD/cprotoparamdata.h \
    $$PWD/cprotoparamdialog.h \
    $$PWD/cresolvingtask.h \
    $$PWD/csubpagemanager.h \
    $$PWD/ctlhtools.h \
    $$PWD/tlhv1window.h

SOURCES += \
    $$PWD/CLoadFileTaskDlg.cpp \
    $$PWD/cconfigmanager.cpp \
    $$PWD/cprotocolfactory.cpp \
    $$PWD/cprotoparamdata.cpp \
    $$PWD/cprotoparamdialog.cpp \
    $$PWD/cresolvingtask.cpp \
    $$PWD/csubpagemanager.cpp \
    $$PWD/ctlhtools.cpp \
    $$PWD/tlhv1window.cpp

FORMS += \
    $$PWD/cprotoparamdialog.ui \
    $$PWD/tlhv1window.ui
