﻿#include "csseriservice.h"

#include <QDebug>
#include <QMessageBox>
#include <QMap>
#include <QList>
#include <algorithm>
#include <QTimer>
#include <QCoreApplication>
#include "ctlhtools.h"

#define SERI_TIMEOUT 3000

bool CSseriService::m_isReadSD = false;  //是否读取SD数据标识

CSseriService::CSseriService(QObject *parent) : ICommService(parent)
{
    m_sBaudRate = "115200";
    m_sDataBit = "8";
    m_sParity = tr("No");
    m_sStopbits = "1";
    m_bIsHex = true;
    m_IsOpen = 0;

    m_workthread = new QThread;
    m_workthread->start();

    m_resolvTask = new CResolvingTask;
    m_resolvTask->start();
    m_bIsWorking = false;
    m_timeStatus = 0;      //0-等待状态，1-工作状态

    moveToThread(m_workthread);

    m_serialPort = new QSerialPort;
    //getAvilaCom();
    m_checkSize = 0;
    m_SeriBuff.clear();
    m_serialPort->moveToThread(m_workthread);

    m_bIsUpdating = false;

    m_TimerId = -1;

    m_bEnd.append(4, 0x0f);

    qDebug()<<"CSseriService:"<<QThread::currentThreadId()<<this<<m_resolvTask;

    qRegisterMetaType<QSerialPort::SerialPortError>("QSerialPort::SerialPortError");
    //绑定串口接收数据处理信号与槽函数
    connect(m_serialPort, &QSerialPort::readyRead, this, &CSseriService::serialReadData);
    //错误错误处理信号处理
    connect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);
    //绑定数据与处理的信号和槽函数
    connect(this, &CSseriService::sigDataRead, m_resolvTask, &CResolvingTask::slotAppendData);
}

CSseriService::~CSseriService(){
    qDebug()<<"~CSseriService:"<<QThread::currentThreadId()<<this;
    m_resolvTask->requestInterruption();
    m_workthread->quit();
    m_serialPort->close();
    m_resolvTask->quit();
    m_serialPort->deleteLater();
    m_resolvTask->deleteLater();
}

bool CSseriService::SerialInit(QString sPort,QString sBaudRate, QString sDataBit,QString sParity, QString sStopbits, QString sIsHex, CProtoParamData *parm){
    qDebug()<<"SerialInit:"<<QThread::currentThreadId();
    m_sPort = sPort;
    m_sBaudRate = sBaudRate;
    m_sDataBit = sDataBit;
    m_sParity = sParity;
    m_sStopbits = sStopbits;
    if(sIsHex == tr("Text")){
        m_bIsHex = false;
    }else{
        m_bIsHex = true;
    }

    qDebug()<<"set baudrate:"<<sBaudRate;

    if(sBaudRate == "4800"){
        m_serialPort->setBaudRate(QSerialPort::Baud4800);
    }else if(sBaudRate == "9600"){
        m_serialPort->setBaudRate(QSerialPort::Baud9600);
    }else if(sBaudRate == "38400"){
        m_serialPort->setBaudRate(QSerialPort::Baud38400);
    }else if(sBaudRate == "57600"){
        m_serialPort->setBaudRate(QSerialPort::Baud57600);
    }else if(sBaudRate == "115200"){
        m_serialPort->setBaudRate(QSerialPort::Baud115200);
    }else if(sBaudRate == "460800"){
        m_serialPort->setBaudRate(QSerialPort::Baud460800);
    }else if(sBaudRate == "921600"){
        m_serialPort->setBaudRate(QSerialPort::Baud921600);
    }else if(sBaudRate == "2000000"){
        qDebug()<<"2000000";
        m_serialPort->setBaudRate(QSerialPort::Baud2000000);
    }

    if(sDataBit == "8"){
        m_serialPort->setDataBits(QSerialPort::Data8);
    }

    if(sStopbits == "1"){
        m_serialPort->setStopBits(QSerialPort::OneStop);
    }else if(sStopbits == "1.5"){
        m_serialPort->setStopBits(QSerialPort::OneAndHalfStop);
    }else{
        m_serialPort->setStopBits(QSerialPort::TwoStop);
    }

    if(sParity == tr("Event")){
        m_serialPort->setParity(QSerialPort::EvenParity);
    }else if(sParity == tr("Odd")){
        m_serialPort->setParity(QSerialPort::OddParity);
    }else{
        m_serialPort->setParity(QSerialPort::NoParity);
    }

    connect(parm, &CProtoParamData::sigParamChange, m_resolvTask, &CResolvingTask::slogParamChange);
    m_icatRound = parm->getTimeRound();
    m_iTimeOut = parm->getTimeOut();
    m_iIntvTimes = parm->getIntvTimes();

    m_serialPort->setPortName(sPort);

    //m_serialPort->setFlowControl(QSerialPort::NoFlowControl);

    /*if(!m_serialPort->open(QSerialPort::ReadWrite)){
        qDebug()<<"port open fail:"<<m_serialPort->errorString();
        return false;
    }

    startTimeWork();

    qDebug()<<"readbuff:"<<m_serialPort->readBufferSize();

    m_bIsWorking = true;
    m_resolvTask->setPortN(m_sPort);
    */
    return true;
}

// 实现接口函数
bool CSseriService::CommInit(const QString &sPort, const QString &sBaudRate, const QString &sDataBit,
                            const QString &sParity, const QString &sStopbits, const QString &sIsHex,
                            CProtoParamData *parm)
{
    return SerialInit(sPort, sBaudRate, sDataBit, sParity, sStopbits, sIsHex, parm);
}

void CSseriService::commWriteData()
{
    serialWriteData();
}

void CSseriService::commClose()
{
    seriClose();
}

bool CSseriService::commWrite(QByteArray arr)
{
    return seriWrite(arr);
}

void CSseriService::commReadData()
{
    serialReadData();
}

void CSseriService::slotCommOpenOrClose(const QString config, const int optype, const QString fileprefix)
{
    slotSerialOpenOrClose(config, optype, fileprefix);
}

void CSseriService::slotCommWrite(const char * arr, int &len)
{
    slotSeriWrite(arr, len);
}

//打开或者关闭串口
void CSseriService::slotSerialOpenOrClose(const QString portn,const int optype, const QString fileprefix){
    if(portn != m_sPort){
        return;
    }

    if(optype == 0 && !m_bIsWorking){
        m_resolvTask->clearBuff();
        m_resolvTask->setPortN(m_sPort);
        m_resolvTask->setFilePrefix(fileprefix);
        if(!m_serialPort->open(QSerialPort::ReadWrite)){
            qDebug()<<"port open fail:"<<m_serialPort->errorString()<<QThread::currentThreadId()<<m_serialPort;
        }else{
            qDebug()<<"slotSerialOpen"<<QThread::currentThreadId()<<QThread::currentThreadId()<<m_serialPort;
            m_bIsWorking = true;
        }
    }else{
        if(m_bIsWorking){
            disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);
            qDebug()<<"slotSerialClose"<<QThread::currentThreadId()<<m_serialPort;
            m_serialPort->close();
            m_bIsWorking = false;
        }
    }

    if(m_iTimeOut > 0){
        startTimeWork();
    }

    //qDebug()<<"readbuff:"<<m_serialPort->readBufferSize();


}

//读取串口数据，并送到数据处理对象
void CSseriService::serialReadData(){
    //升级模式改为同步读取数据
    //qDebug()<<"serialReadData:"<<QThread::currentThreadId();
    if(m_bIsUpdating){
        return;
    }

    QByteArray rcvdata = m_serialPort->readAll();
    if(rcvdata.size() == 0){
        return;
    }
//qDebug()<<"zheng 2025-5-17 rcvdata:"<<rcvdata.toHex();
    //判断是否是在读取SD数据
    if(m_isReadSD){
        //qDebug()<<"rcvData:"<<rcvdata.toHex(' ');
        int pos = 0;
        if((pos = rcvdata.indexOf(m_bEnd)) != -1){
            //读取结束
            for(int i = pos; i < rcvdata.size(); i++){
                if(static_cast<uint8_t>(rcvdata.at(i) != 0x0f)){
                    break;
                }
            }
            m_isReadSD = false;
            emit sigReadEnd(0);
            delete this;
        }
    }
    //最多保留8192字节数据
    if(m_SeriBuff.size() == 0 || m_SeriBuff.size() < 8192){
        //qDebug()<<"rcvdata:"<<rcvdata.toHex();
        //qDebug()<<"serialReadData"<<rcvdata.toHex(' ');
        m_SeriBuff.append(rcvdata);
    }
    //qDebug()<<"serialReadData:"<<rcvdata.size();
    emit sigDataRead(rcvdata);
}

void CSseriService::clearBuff(){
    m_SeriBuff.clear();
}

void CSseriService::slotVersionQuery(bool isOnlyQuery){
    unsigned char upinfohead[] = {0xAF, 0x55, 0xFA};
    //开始命令
    unsigned char upbegincmd[] = {0xAA, 0x51};
    unsigned char upbeginres[] = {0xFA, 0x55, 0xAF};

    //结束命令
    unsigned char upstopcmd[] = {0xAA, 0x55};
    unsigned char upstopres[] = {0xFA, 0x55, 0xAF};

    QByteArray resarr = QByteArray((char *)upbeginres, 3);

    disconnect(m_serialPort, &QSerialPort::readyRead, this, &CSseriService::serialReadData);
    disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);

    m_serialPort->clear();

    unsigned char writebuf[256] = {0};
    unsigned short packindex = 0;
    int writelen = 256;
    bool isbegeinres = false;
    int startops = 0;
    int mxloop = 4;
    int mxreadtimes = 1024;
    QByteArray startresarr;
    QByteArray readback;

    emit sigUpdateEnd(0, "***" + tr("Start querying device version information") + tr("***!"));

    memset(writebuf, 0x00, sizeof (writebuf));
    memcpy(writebuf + 251, &packindex, 2);
    memcpy(writebuf, upinfohead, 3);
    memcpy(writebuf + 3, upbegincmd, 2);
    memcpy(writebuf + 5, &writelen, 2);
    writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 2);
    writebuf[254] = 0x00;
    writebuf[255] = 0xFF;

    //设置缓冲区大小，只有缓冲区满了才会触发waitForReadyRead
    m_serialPort->setReadBufferSize(64);

    //qDebug()<<"write start:"<<QByteArray((char *)writebuf, 256).toHex(' ');
    do{
        mxloop--;
        readback.clear();
        isbegeinres = false;
        m_serialPort->write((const char *)writebuf, writelen);
        m_serialPort->flush();
        mxreadtimes = 1024;

        //先直接接收一次
        bool isreadyread = false;
        while (!isbegeinres && mxloop > 0 && mxreadtimes > 0) {
            QCoreApplication::processEvents();
            if(!isreadyread && !m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
                mxloop--;
            }
            mxreadtimes--;
            //qDebug()<<"mareadtimes:"<<mxreadtimes;
            isreadyread = true;
            startresarr = m_serialPort->readAll();
            if(startresarr.size() == 0){
                isreadyread = false;
                continue;
            }
            readback.append(startresarr);
            //qDebug()<<"recv:"<<readback.toHex(' ')<<mxreadtimes<<mxloop;
            if((startops = readback.indexOf(resarr)) != -1 && readback.length() > startops + 63){
                isbegeinres = true;
                break;
            }
            if(startops == -1){
                readback.remove(0, readback.size() - 3);
            }
        }

        if(!isbegeinres){
            emit sigUpdateEnd(1, tr("Failed to receive version query response, failed to query version information!"));
            m_bIsUpdating = false;
            if(isOnlyQuery){
                delete this;
            }
            return;
        }else if(static_cast<unsigned char>(readback[startops + 7]) != 0x01){
            QThread::sleep(1);
            continue;
        }
        break;
    }while(mxloop > 0);


    int ver1 = (unsigned char)readback[startops + 8];
    int ver2 = (unsigned char)readback[startops + 9];
    int ver3 = (unsigned char)readback[startops + 10];
    int idate4;
    memcpy(&idate4, readback.data() + startops + 11, 4);
    int ver5 = (unsigned char)readback[startops + 15];
    QString sver5;
    switch (ver5) {
    case 0:
        sver5 = "GD";
        break;
    case 1:
        sver5 = "HPM";
        break;
    case 2:
        sver5 = "SEM";
        break;
    case 3:
        sver5 = "INF";
        break;
    default:
        sver5 = "GD";
        break;
    }
    int ver6 = (unsigned char)readback[startops + 16];
    QString sver6;
    switch (ver6) {
    case 0:
        sver6 = "alpha";
        break;
    case 1:
        sver6 = "beta";
        break;
    case 2:
        sver6 = "rc";
        break;
    case 3:
        sver6 = "ga";
        break;
    default:
        sver6 = "alpha";
        break;
    }
    int ver7 = (unsigned char)readback[startops + 17];
    QString sver7 = "x";
    if(ver7 == 255){
        sver7 = "x";
    }else{
        sver7 = QString::number(ver7);
    }

    QString strBanBen = QString("V%1.%2.%3.%4_%5_%6_%7").arg(ver1).arg(ver2).arg(ver3).arg(idate4).arg(sver5).arg(sver6).arg(sver7);
    emit sigUpdateEnd(0, tr("Received version query response, current version number is") + QString(" %1 !").arg(strBanBen));

    //后续需要升级，则直接退出
    if(!isOnlyQuery){
        return;
    }

    packindex++;
    memset(writebuf + 3, 0x00, sizeof (writebuf) - 3);
    memcpy(writebuf + 251, &packindex, 2);
    memcpy(writebuf + 3, upstopcmd, 2);
    memcpy(writebuf + 5, &writelen, 2);
    writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 2);
    writebuf[254] = 0x00;
    writebuf[255] = 0xFF;

    //qDebug()<<"write end:"<< QByteArray((char *)writebuf, 256).toHex(' ')<<writelen;

    m_serialPort->write((const char *)writebuf, writelen);
    //查询版本后,发送结束命令需要flush或者wait一下，否则导致下位机收不到指令无法恢复输出数据
    m_serialPort->flush();
    if(!m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
    //    qDebug()<<"write end result timeout";
    //    //m_serialPort->write((const char *)writebuf, writelen);
    }
    QByteArray testarr = m_serialPort->readAll();
    //qDebug()<<testarr.toHex(' ');

    m_bIsUpdating = false;
    delete this;
}

void CSseriService::slotStartUpdate(QString filename){
    qDebug()<<"slotStartUpdate";

//    // 断开所有信号连接，确保没有其他处理干扰串口通信
//    disconnect(m_serialPort, &QSerialPort::readyRead, this, &CSseriService::serialReadData);
//    disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);//zheng 2025-5-17
    //头
    unsigned char upinfohead[] = {0xAF, 0x55, 0xFA};
    //开始命令
    unsigned char upbegincmd[] = {0xAA, 0x51};
    unsigned char upbeginres[] = {0xFA, 0x55, 0xAF};
    QByteArray startresarr = QByteArray((char *)upbeginres, 3);
    //数据命令
    unsigned char updoingcmd[] = {0xAA, 0x52};
    unsigned char updoingres[] = {0xFA, 0x55, 0xAF};
    QByteArray doningresarr = QByteArray((char *)updoingres, 3);
    //结束命令
    unsigned char upstopcmd[] = {0xAA, 0x55};
    unsigned char upstopres[] = {0xFA, 0x55, 0xAF, 0xAA, 0x55};
    QByteArray endresarr = QByteArray((char *)upstopres, 5);

    unsigned char writebuf[256] = {0};

    int datapacklen = 128;
    char readbuf[128] = {0};
    int readlen = 0;
    bool isnext = true;
    int writelen = 256;
    unsigned short tpacknum = 0;
    unsigned short cpacknum = -1;
    unsigned short packindex = 0;

    int startops = 0;
    int mxloop = 3;
    int mxreadtimes = 1024;
    int iupgfail = 0;
    QByteArray startarr;
    int ibprocess = 0;
    int icprocess = 0;
    int resendtimes = 0;
    QByteArray readback;
    bool isreadyread = false;

    m_bIsUpdating = true;

    //QFile filetest("D:\\TLH\\020.bin");
    //filetest.open(QIODevice::WriteOnly);

    QFile binf(filename);
    if(!binf.open(QIODevice::ReadOnly)){
        emit sigUpdateEnd(1, tr("Failed to read upgrade package, please check upgrade package, upgrade process failed!"));
        m_bIsUpdating = false;
        delete this;
        return;
    }

    uint32_t filecheck = CTlhTools::calfile_crc32(filename);
    if(filecheck == 0){
        emit sigUpdateEnd(1, tr("Failed to read upgrade package, please check upgrade package, upgrade process failed!"));
        m_bIsUpdating = false;
        binf.close();
        delete this;
        return;
    }

    emit sigUpdateEnd(0, "***" + tr("Notify the device to start upgrading") + "***");
    //disconnect(m_serialPort, &QSerialPort::readyRead, this, &CSseriService::serialReadData);
    //disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);

    slotVersionQuery(false);
    if(!m_bIsUpdating){
        delete this;
        return;
    }

    if(binf.size() % datapacklen == 0){
        tpacknum = binf.size() / datapacklen;
    }else{
        tpacknum = binf.size() / datapacklen + 1;
    }

    qDebug()<<"begin transfer pack data";

    packindex++;
    emit sigUpdateEnd(0, tr("Start pushing upgrade packages!"));

    memcpy(writebuf, upinfohead, 3);
    memcpy(writebuf + 3, updoingcmd, 2);
    memcpy(writebuf + 5, &writelen, 2);

    while(true){
        //检查是否有信号需要处理
        QCoreApplication::processEvents();
        memset(writebuf + 7, 0x00, sizeof (writebuf) - 7);
        if(!m_bIsUpdating){
            iupgfail = 1;
            break;
        }
        if(isnext){
            resendtimes = 0;
            cpacknum++;
            readlen = binf.read(readbuf, datapacklen);
            if(readlen < 1){
                break;
            }
        }else{
            //失败数据包最多重发3次
            if(++resendtimes > 3){
                emit sigUpdateEnd(0, tr("Data packet retransmission failed!"));
                iupgfail = 1;
                break;
            }
        }
        memcpy(writebuf + 12, readbuf, readlen);
        memcpy(writebuf + 7, &cpacknum, 2);
        memcpy(writebuf + 9, &tpacknum, 2);
        memcpy(writebuf + 11, &readlen, 1);
        memcpy(writebuf + 251, &packindex, 2);
        writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 2);
        //writebuf[62] = CTlhTools::GetCrc8Check((unsigned char *)writebuf + 14, 40);
        writebuf[254] = 0x00;
        writebuf[255] = 0xFF;

        //qDebug()<<"write data:"<<cpacknum<<":"<<QByteArray((char *)writebuf, writelen).toHex(' ')<<readlen;

        m_serialPort->write((const char *)writebuf, writelen);
        m_serialPort->flush();
        //if(resendtimes == 0){
        //    filetest.write((const char *)(writebuf + 12), readlen);
        //}
        //if(m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
        //    backlen = m_serialPort->read(backbuf, 16);
        //    qDebug()<<"recv data back:"<<QByteArray(backbuf, 16).toHex(' ');
        //}else{
        //    qDebug()<<"recv data back timeout";
        //}
        mxloop = 3;
        mxreadtimes = 1024;

        readback.clear();
        startarr.clear();

        isreadyread = false;
        while (mxloop > 0 && mxreadtimes > 0) {
            QCoreApplication::processEvents();
            if(!isreadyread && !m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
                mxloop--;
                qDebug()<<"try times1:"<<mxloop;
                //continue;
            }

            isreadyread = true;
            mxreadtimes--;
            startarr = m_serialPort->readAll();
            readback.append(startarr);

            //qDebug()<<"recv data back:"<<readback.toHex(' ')<<m_serialPort->bytesAvailable();
            if( (startops = readback.lastIndexOf(doningresarr)) != -1 && readback.length() > startops + 63){
                //qDebug()<<"get doningresarr:"<<startops;
                //检查包编号是否正确
                unsigned short bpacknum;
                memcpy(&bpacknum, readback.data() + startops + 8, 2);
                //qDebug()<<"get doningresarr:"<<startops<<bpacknum<<cpacknum<<mxreadtimes;
                if(cpacknum == bpacknum){
                    break;
                }
            }

            if(startops == -1){
                readback.remove(0, readback.size() - 3);
            }
            QThread::usleep(1000);
        }
        //qDebug()<<"recv:"<<readback.toHex(' ')<<mxreadtimes;

        if(readback.size() > 7 && static_cast<unsigned char>(readback[startops + 7]) == 0x01){
            isnext = true;
        }else{
            if(startops == -1){
                emit sigUpdateEnd(0, tr("Receiving device upgrade package response fail!"));
            }else{
                emit sigUpdateEnd(0, tr("Receiving device upgrade package response error!"));
            }
            isnext = false;
        }

        if(packindex++ == 65535){
            packindex = 0;
        }

        icprocess = 100 * cpacknum / tpacknum;
        if(ibprocess < icprocess){
            emit sigUpdateEnd(0, tr("Push upgrade installation package") + QString("%1%!").arg(icprocess));
            ibprocess = icprocess;
        }
    }

    //filetest.close();

    emit sigUpdateEnd(0, tr("Upgrade package push completed!"));
    memset(writebuf + 3, 0x00, sizeof (writelen) - 3);
    memcpy(writebuf + 251, &packindex, 2);
    memcpy(writebuf + 3, upstopcmd, 2);
    memcpy(writebuf + 5, &writelen, 2);
    //最后升级结束报文新增一个文件校验码，用于下位机确保收到的升级程序是完整无误的
    memcpy(writebuf + 7, &filecheck, 4);
    writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 2);
    writebuf[254] = 0x00;
    writebuf[255] = 0xFF;

    //qDebug()<<"write end:"<< QByteArray((char *)writebuf, 256).toHex(' ');
    QByteArray startarr1;
    startarr1.clear();
    m_serialPort->write((const char *)writebuf, writelen);




    if(!m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
        m_serialPort->write((const char *)writebuf, writelen);

    }
//    else
//    {
//        startarr1 = m_serialPort->readAll();
//        qDebug()<<"recv data back:"<<startarr1.toHex(' ')<<m_serialPort->bytesAvailable();
//    }

    isreadyread = false;
    mxreadtimes = 1024;
    mxloop = 3;
    readback.clear();
    while (mxloop > 0 && mxreadtimes > 0) {
        QCoreApplication::processEvents();
        if(!isreadyread && !m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
            mxloop--;
            qDebug()<<"try times1:"<<mxloop;
            //continue;
        }

        isreadyread = true;
        mxreadtimes--;
        startarr = m_serialPort->readAll();
        readback.append(startarr);

//        qDebug()<<"zheng recv data back:"<<readback.toHex(' ')<<m_serialPort->bytesAvailable();
        if( (startops = readback.indexOf(endresarr)) != -1 && readback.length() > startops + 6){
             qDebug()<<"get endresarr:"<<startops<<"readback: "<<readback.toHex(' ');
             break;
        }

        if(startops == -1 ){
            readback.remove(0, readback.size() - 5);
        }
        QThread::usleep(1000);
//        QThread::usleep(10);//zheng 2025-5-17
    }
    qDebug()<<"recv end:"<<readback.toHex(' ')<<mxreadtimes;

    if(readback.size() > 7 && static_cast<unsigned char>(readback[startops + 7]) == 0x01 && !iupgfail){
        iupgfail = false;
    }else{
        if(startops == -1){
            emit sigUpdateEnd(0, tr("Receiving device upgrade package response fail!"));
        }else{
            emit sigUpdateEnd(0, tr("Receiving device upgrade package response error!"));
        }
        iupgfail = true;
    }
//    // 在函数结束前恢复连接
//    connect(m_serialPort, &QSerialPort::readyRead, this, &CSseriService::serialReadData);
//    connect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);//zheng 2025-5-17
    m_bIsUpdating = false;
    if(!iupgfail){
        emit sigUpdateEnd(2, tr("Upgrade process executed successfully!"));
    }else{
        emit sigUpdateEnd(1, tr("Upgrade process execution failed!"));
    }

    delete this;
}

void CSseriService::slotStopUpdate(){
    qDebug()<<"slotStopUpdate";
    m_bIsUpdating = false;
    //头
    unsigned char upinfohead[] = {0xAF, 0x55, 0xFA};
    //结束命令
    unsigned char upstopcmd[] = {0xAA , 0x59};

    unsigned char writebuf[256] = {0};

    int writelen = 256;

    unsigned short packindex = 0;

    memcpy(writebuf + 251, &packindex, 2);
    memcpy(writebuf, upinfohead, 3);
    memcpy(writebuf + 3, upstopcmd, 2);
    memcpy(writebuf + 5, &writelen, 2);
    writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 2);
    writebuf[254] = 0x00;
    writebuf[255] = 0xFF;

    //QByteArray testarr;

    //qDebug()<<"write start:"<<testarr.append((char *)writebuf, 64).toHex(' ');

    writelen = m_serialPort->write((const char *)writebuf, writelen);
    if(writelen > 0){
        qDebug()<<"send stop update info succ:"<<writelen;
    }else{
        writelen = m_serialPort->write((const char *)writebuf, writelen);
        qDebug()<<"send stop update info fail";
    }

    return;

}

void CSseriService::slotResetUpdate(){
    qDebug()<<"slotResetUpdate";
    m_bIsUpdating = false;
    //头
    unsigned char upinfohead[] = {0x55, 0xAA, 0x02, 0x03, 0x00, 0x38};
    //结束命令
    unsigned char upsresetcmd[] = {0xCA, 0x11, 0x00};

    unsigned char writebuf[64] = {0};

    int writelen = 64;

    unsigned short packindex = 0;

    memcpy(writebuf + 60, &packindex, 2);
    memcpy(writebuf, upinfohead, 6);
    memcpy(writebuf + 6, upsresetcmd, 3);
    writebuf[62] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 64), 6, 2);
    writebuf[63] = 0xF0;

    //QByteArray testarr;
    //
    //qDebug()<<"write reset:"<<testarr.append((char *)writebuf, 64).toHex(' ');

    writelen = m_serialPort->write((const char *)writebuf, writelen);
    if(writelen > 0){
        qDebug()<<"send stop update info succ:"<<writelen;
    }else{
        writelen = m_serialPort->write((const char *)writebuf, writelen);
        qDebug()<<"send stop update info fail";
    }
}

void CSseriService::setUpdateMode(bool mode){
    m_bIsUpdating = mode;
}

QStringList CSseriService::getAvilaCom(){
    QStringList comList;
    QList<QSerialPortInfo> portlist;
    foreach(const QSerialPortInfo &info, QSerialPortInfo::availablePorts()){
        QSerialPort serial;
        serial.setPort(info);
        //if(serial.open(QIODevice::ReadWrite)){
            comList.append(info.portName());
        //    serial.close();
        //}
    }
    comList.sort();

    //qDebug()<<"getAvilaCom:"<<m_sComList<<comList;

    if(m_sComList != comList){
        m_sComList = comList;
    }
    return m_sComList;
}


void CSseriService::slotSerialError(QSerialPort::SerialPortError e){
    if(e > QSerialPort::SerialPortError::OpenError){
        m_serialPort->clearError();
        qDebug()<<"doSerialError:"<<e;
        //窗口出现异常错误，关闭串口，并结束本次数据会话
        //delete  this;
        qDebug()<<"delete Thread id error:"<<QThread::currentThreadId();
        QTimer::singleShot(1000, this, [=](){
             qDebug()<<"delete Thread id error exe:"<<QThread::currentThreadId();
             delete  this;
        });
     }
}

bool CSseriService::seriWrite(QByteArray arr){
    qint64 reslen = 0;
    reslen = m_serialPort->write(arr);
    if(reslen == arr.size()){
        return true;
    }else{
        qDebug()<<"write seri fail:"<<reslen;
        return false;
    }
}

void CSseriService::slotSeriWrite(const char * arr, int &len){
    qint64 reslen = 0;
    //写串口前先清理一下临时接收buff
    m_SeriBuff.clear();
    reslen = m_serialPort->write(arr, len);
    if(reslen != len){
        qDebug()<<"write seri fail:"<<reslen;
    }
    len = reslen;
}

void CSseriService::editWrite(QByteArray arr){
    emit sigDataRead(arr);
}

bool CSseriService::isWorking(){
    return m_bIsWorking;
}

QByteArray CSseriService::getCurrentBuff(){
    QByteArray backdata = m_SeriBuff;
    m_SeriBuff.clear();
    return backdata;
}

/****************************************
*方法名 autoWorking
*描述  定时按轮次采集串口数据
*
*
****************************************/
void CSseriService::startTimeWork(){
    //qDebug()<<"parm 2 addr:"<<QTime::currentTime().toString()<<parm;
     if(m_timeStatus == 0 ){
         m_timeStatus = 1;
         m_TimerId = startTimer(m_iTimeOut, Qt::PreciseTimer);
     }else{
         //暂时不支持定时采集多轮
         //if(--m_icatRound == 0){
         //delete后不能执行任何代码，否则可能引发未知错误
             delete this;
         //}
         /*
         startTimer(m_iIntvTimes, Qt::PreciseTimer);
         */
     }

}

void CSseriService::timerEvent(QTimerEvent *event){
    startTimeWork();
}

//多次参数设置
void CSseriService::slotMultilParmsUpdate(QByteArray cmd, QVector<QByteArray> vparms, bool isolddev){
    qDebug()<<"slotparmupdate:"<<vparms.size();
    unsigned char fbit = static_cast<unsigned char>(cmd[0]);
    unsigned char sbit = static_cast<unsigned char>(cmd[1]);
    QByteArray resbytearr;
    QByteArray resbackdata;

    disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);

    m_bIsUpdating = true;

    for(int i = 0; i < vparms.size(); i++){
        resbytearr = doParmsUpdate(cmd, vparms.at(i), isolddev);
        if(static_cast<unsigned char>(resbytearr[0]) == 0xEE && static_cast<unsigned char>(resbytearr[1]) == 0xEE){
            m_bIsUpdating = false;
            emit sigParmsEnd(1, cmd, vparms.at(i));
            return;
        }
        resbackdata.append(resbytearr);
    }

    m_bIsUpdating = false;

    qDebug()<<"resbackdata:"<<resbackdata.toHex(' ');
    emit sigParmsEnd(0, cmd, resbackdata);

    //若是不重启串口线程，则需要将串口缓冲区设置为默认的0，无限制，但是我们采用重启的方式
    m_serialPort->setReadBufferSize(0);

    //不重启串口，避免有些参数设置重新生成文件不合适的情况
    //delete this;
}

void CSseriService::slotParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev){
    qDebug()<<"slotparmupdate:"<<parms.toHex(' ');
    unsigned char fbit = static_cast<unsigned char>(cmd[0]);
    unsigned char sbit = static_cast<unsigned char>(cmd[1]);
    QByteArray resbytearr;
    QByteArray resbackdata;

    if(parms.size() > 246){
        emit sigParmsEnd(1, cmd, parms);
        return;
    }

    disconnect(m_serialPort, &QSerialPort::errorOccurred, this, &CSseriService::slotSerialError);

    m_bIsUpdating = true;
    if(fbit == 0xAA && sbit == 0xF4 && !isolddev){
        for(int i = 0; i < parms.size();){
            resbytearr = doParmsUpdate(cmd, parms.mid(i, 4), isolddev);
            i += 4;
            if(static_cast<unsigned char>(resbytearr[0]) == 0xEE && static_cast<unsigned char>(resbytearr[1]) == 0xEE){
                m_bIsUpdating = false;
                emit sigParmsEnd(1, cmd, parms);
                return;
            }
            resbackdata.append(resbytearr);
        }
    }else{
        resbytearr = doParmsUpdate(cmd, parms, isolddev);
        if(static_cast<unsigned char>(resbytearr[0]) == 0xEE && static_cast<unsigned char>(resbytearr[1]) == 0xEE){
            m_bIsUpdating = false;
            emit sigParmsEnd(1, cmd, parms);
            return;
        }
        resbackdata.append(resbytearr);
    }

    m_bIsUpdating = false;

    qDebug()<<"resbackdata:"<<resbackdata.toHex(' ');
    emit sigParmsEnd(0, cmd, resbackdata);

    //若是不重启串口线程，则需要将串口缓冲区设置为默认的0，无限制，但是我们采用重启的方式
    m_serialPort->setReadBufferSize(0);

    //不重启串口，避免有些参数设置重新生成文件不合适的情况
    //delete this;
}

QByteArray CSseriService::doParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev){
    unsigned char upinfohead[] = {0xAF ,0x55 ,0xFA};
    unsigned char upinfooldhead[] = {0xFA ,0x55};
    unsigned char writebuf[256] = {0};
    unsigned short writelen = 256;
    unsigned short reslen = 64;
    unsigned char reshread[] ={0xFA, 0x55, 0xAF};
    QByteArray resarrhread;
    if(!isolddev){
        m_serialPort->setReadBufferSize(64);
        resarrhread.append((char *)reshread, 3);
    }else{
        resarrhread.append((char *)upinfooldhead, 2);
    }
    int mxloop = 3;    //读取次数
    int mxreadtimes = 1024;  //重试次数
    QByteArray startarr, readback;
    bool isreadyread;
    int startops = 0;

    qDebug()<<"parms:"<<parms.toHex(' ')<<cmd.toHex(' ');
    if(static_cast<unsigned char>(cmd.at(0)) == 0xAA && static_cast<unsigned char>(cmd.at(1)) == 0xC5 && \
       static_cast<unsigned char>(parms.at(1) == 0x03)){
        qDebug()<<"parms1:"<<parms.toHex(' ')<<cmd.toHex(' ');
        m_isReadSD = true;
    }

    memcpy(writebuf, upinfohead, 3);
    memcpy(writebuf + 3, cmd.data(), 2);
    if(!isolddev){
        memcpy(writebuf + 5, &writelen, 2);
        memcpy(writebuf + 7, parms.data(), parms.size());
    }else{
        memcpy(writebuf + 5, parms.data(), parms.size());
        if(static_cast<unsigned char>(cmd[0]) == 0xAA && static_cast<unsigned char>(cmd.data()[1]) == 0xF4){
            reslen = 64;
        }else{
            reslen = 6;
        }
    }

    if(!isolddev){
        writebuf[253] = CTlhTools::sum8CheckSum(QByteArray((char *)writebuf, 256), 3, 3);
        writebuf[254] = 0x00;
        writebuf[255] = 0xFF;
    }else{
        writebuf[5 + parms.size()] = 0xFF;
    }

    QByteArray testarr;
    //qDebug()<<"write data :"<<testarr.append((char *)writebuf, 256).toHex(' ')<<writelen;
    if(!isolddev){
        m_serialPort->write((const char *)writebuf, writelen);
    }else{
        m_serialPort->write((const char *)writebuf, 6 + parms.size());
    }

    isreadyread = true;

    while (mxloop > 0 && mxreadtimes > 0) {
        QCoreApplication::processEvents();
        if(!isreadyread && !m_serialPort->waitForReadyRead(SERI_TIMEOUT)){
            mxloop--;
            if(!isolddev){
                //重发一次解决下位机程序从高德到普通模式需要重启串口的问题
                m_serialPort->write((const char *)writebuf, writelen);
            }
            //qDebug()<<"try times:"<<mxloop;
            //continue;
        }
        isreadyread = false;
        mxreadtimes--;
        startarr = m_serialPort->readAll();
        readback.append(startarr);
        //qDebug()<<"recv data back:"<<readback.toHex(' ')<<reslen<<resarrhread.toHex(' ');
        if( (startops = readback.indexOf(resarrhread)) != -1 && readback.length() > startops + reslen - 1){
            //qDebug()<<"get doningresarr:"<<startops<<readback.mid(startops, reslen).toHex(' ');
            //emit sigParmsEnd(0, cmd, readback.mid(startops, reslen));
            if(isolddev || static_cast<unsigned char>(readback[startops + 7]) == 0x01){
                return readback.mid(startops, reslen);
            }else{
                readback.remove(startops, reslen);
                //QThread::sleep(1);
                m_serialPort->write((const char *)writebuf, writelen);
            }
        }

        if(startops == -1){
            readback.remove(0, readback.size() - 3);
        }
    }

    //m_bIsUpdating = false;
    //
    //emit sigParmsEnd(2, cmd, parms);
    readback.clear();
    readback.append(0xEE);
    readback.append(0xEE);
    return readback;
}



