﻿#include "ca6a600protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cdataanapage.h"
#include "cgyrocalibpage.h"
#include "ctlhtools.h"
#include "cprotocolfactory.h"
#include <QDebug>

CA6A600Protocol::CA6A600Protocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));
    CGyroCalibPage *gyropage = static_cast<CGyroCalibPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_GyroCalib"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect CA6A600Protocol";
       connect(this, &CA6A600Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &CA6A600Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
       connect(this, &CA6A600Protocol::sigDataUpdate,  drivepage, &CDriveTestPage::slotDataShow);
       connect(this, &CA6A600Protocol::sigChartsUpdate, danapage, &CDataAnaPage::slotDataShow);
       connect(this, &CA6A600Protocol::sigFreqUpdate, gyropage, &CGyroCalibPage::slotDataShow);
       connect(this, &CA6A600Protocol::sigDestory, gyropage, &CGyroCalibPage::slotDestory);
   }
   //m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺x:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺y:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺z:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("加计x:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("加计y:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("加计z:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("加计温度:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺x平均值:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺y平均值:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("陀螺z平均值:"));
   //m_slProtoKeys.append(QString::fromLocal8Bit("温度平均值:"));

   //m_slFreqKeys.append(QString::fromLocal8Bit("采样序号:"));

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);
   m_icatFreqCounts = 0;
   m_lcatCurrCounts = 0l;
   m_davggyrx = 0.0;
   m_davggyry = 0.0;
   m_davggyrz = 0.0;
   m_davgcalx = 0.0;
   m_davgcaly = 0.0;
   m_davgcalz = 0.0;
   m_davggyrtemp = 0.0;
   m_davgcaltemp = 0.0;
}

bool CA6A600Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 2);
    m_uMsgLen = sizeof (Strua6a600);
    m_slProtoKeys =  CProtocolFactory::getProKeys(m_iProtoIndex);
    m_slFreqKeys.append(m_slProtoKeys.mid(8, 8));

    return true;
}

CA6A600Protocol::~CA6A600Protocol(){
    qDebug()<<"~CA5A500Protocol";
}

void CA6A600Protocol::paseMsg(const QByteArray msg){
    Strua6a600 st_a6a600;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < sizeof (Strua6a600)){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    //qDebug()<<"CA5A500Protocol1111"<<sizeof (st_a5a500);
    QByteArray tempmsg = msg;
    reserverData(tempmsg, 2, 4);
    reserverData(tempmsg, 6, 4);
    reserverData(tempmsg, 10, 4);
    reserverData(tempmsg, 14, 2);
    reserverData(tempmsg, 16, 2);
    reserverData(tempmsg, 18, 2);
    reserverData(tempmsg, 20, 2);
    reserverData(tempmsg, 22, 2);

    ::memcpy(&st_a6a600, tempmsg.data(), m_uMsgLen);

    //char buff[10] = {0};
    //::memcpy(buff, &st_a5a500.temp, 2);
    //buff[0] = 0xfa;
    //buff[1] = 0x01;
    //::memcpy(&st_a5a500.temp, buff, 2);
    //qDebug()<<"buff:"<<QByteArray(buff).toHex(' ')<<st_a5a500.temp;

    //uint16_t currentindx = st_a5a500.pgidex;
    //qDebug()<<"code:"<<st_aa6600.st921info.selftestingcode;
    //if(m_iFramIndx != -1 && ((m_iFramIndx + 1) % m_iFpgaFc != currentindx)){
    //    qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN;
    //    qDebug()<< m_sMsgCache.toHex();
    //    qDebug()<< msg.toHex();
    //    m_iLossFrameSum++;
    //    setFrameErr(E_FRAME_LOSS);
    //    if(m_bIsNeedShowL){
    //        emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(QString::fromLocal8Bit("丢帧数:%1").arg(m_iLossFrameSum)), m_sPortN);
    //        m_bIsNeedShowL = false;
    //    }
    //}

    //异或校验
    if(!sumEorCheck(msg, 2 , 1)){
    //和校验
    //if(!sum8CheckSum(msg, 2, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sum8CheckSum error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        //return;
    }
    //qDebug()<<"CA5A500Protocol1113"<<st_a5a500.temp<<st_a5a500.flog;
    m_sMsgCache.clear();
    m_sMsgCache.append(msg);
    //m_iFramIndx = currentindx;

    double fogx = 0.0;
    double fogy = 0.0;
    double fogz = 0.0;
    double calix = 0.0;
    double caliy = 0.0;
    double caliz = 0.0;
    QMap<QString, QString> divisor;
    CProtoParamData::getProtocolDivisor(m_iProtoIndex, divisor);

    //dataValues.append(QString::number(st_a5a500.pgidex));
    if(divisor.contains(u8"陀螺x:")){
        fogx = CTlhTools::calDivisor(st_a6a600.flogx, divisor[u8"陀螺x:"]);
    }else{
        fogx = st_a6a600.flogx;
    }

    if(divisor.contains(u8"陀螺y:")){
        fogy = CTlhTools::calDivisor(st_a6a600.flogy, divisor[u8"陀螺y:"]);
    }else{
        fogy = st_a6a600.flogy;
    }

    if(divisor.contains(u8"陀螺z:")){
        fogz = CTlhTools::calDivisor(st_a6a600.flogz, divisor[u8"陀螺z:"]);
    }else{
        fogz = st_a6a600.flogz;
    }

    if(divisor.contains(u8"加计x:")){
        calix = CTlhTools::calDivisor(st_a6a600.accelx, divisor[u8"加计x:"]);
    }else{
        calix = st_a6a600.accelx;
    }

    if(divisor.contains(u8"加计y:")){
        caliy = CTlhTools::calDivisor(st_a6a600.accely, divisor[u8"加计y:"]);
    }else{
        caliy = st_a6a600.accely;
    }

    if(divisor.contains(u8"加计z:")){
        caliz = CTlhTools::calDivisor(st_a6a600.accelz, divisor[u8"加计z:"]);
    }else{
        caliz = st_a6a600.accelz;
    }


    dataValues.append(QString::number(fogx, 'f', 4));
    dataValues.append(QString::number(fogy, 'f', 4));
    dataValues.append(QString::number(fogz, 'f', 4));
    dataValues.append(QString::number(calix, 'f', 4));
    dataValues.append(QString::number(caliy, 'f', 4));
    dataValues.append(QString::number(caliz, 'f', 4));
    float temp = 0;
    double flogtemp = 0.0;
    if(st_a6a600.flogtemp >> 15){
        temp = -1 * complement2original(st_a6a600.flogtemp & 0x0000FFFF);
    }else{
        temp = st_a6a600.flogtemp;
    }

    if(divisor.contains(u8"陀螺温度:")){
        flogtemp = CTlhTools::calDivisor(temp, divisor[u8"陀螺温度:"]);
    }else{
        flogtemp = temp/16.00;
    }

    double acceltemp = 0;
    if(st_a6a600.acceltemp >> 15){
        temp = -1 * complement2original(st_a6a600.acceltemp & 0x0000FFFF);
    }else{
        temp = st_a6a600.acceltemp;
    }

    if(divisor.contains(u8"加计温度:")){
        acceltemp = CTlhTools::calDivisor(temp, divisor[u8"加计温度:"]);
    }else{
        acceltemp = temp;
    }

    //qDebug()<<"temp1:"<<st_a5a500.temp<<temp;
    dataValues.append(QString::number(flogtemp, 'f', 2));
    dataValues.append(QString::number(acceltemp, 'f', 2));
    QStringList orikeys;
    orikeys.append(m_slProtoKeys.mid(0, 8));
    writeCvsFile("A6A6", orikeys, dataValues);

    //生成频率采样文件
    //qDebug()<<"m_icatFreqCounts:"<<m_lcatCurrCounts<<m_icatFreqCounts;
    if(m_icatFreqCounts != 0 && (++m_lcatCurrCounts % m_icatFreqCounts == 0) ){
        m_vGyrDatasx.append(fogx);
        m_vGyrDatasy.append(fogy);
        m_vGyrDatasz.append(fogz);
        m_vCaliDatasx.append(calix);
        m_vCaliDatasy.append(caliy);
        m_vCaliDatasz.append(caliz);
        m_vGTempDatas.append(flogtemp);
        m_vCTempDatas.append(acceltemp);
        m_davggyrx = CTlhTools::getAverage(m_vGyrDatasx);
        m_davggyry = CTlhTools::getAverage(m_vGyrDatasy);
        m_davggyrz = CTlhTools::getAverage(m_vGyrDatasz);
        m_davgcalx = CTlhTools::getAverage(m_vCaliDatasx);
        m_davgcaly = CTlhTools::getAverage(m_vCaliDatasy);
        m_davgcalz = CTlhTools::getAverage(m_vCaliDatasz);
        m_davggyrtemp = CTlhTools::getAverage(m_vGTempDatas);
        m_davgcaltemp = CTlhTools::getAverage(m_vCTempDatas);
        //qDebug()<<"A5A5:"<<st_a5a500.flog<<m_davggyr<<m_vGyrDatas.size();
        QStringList slwriteval;
        slwriteval<<QString::number(m_davggyrx, 'f', 6)<<QString::number(m_davggyry, 'f', 6)<<QString::number(m_davggyrz, 'f', 6) \
                  <<QString::number(m_davgcalx, 'f', 6)<<QString::number(m_davgcaly, 'f', 6)<<QString::number(m_davgcalz, 'f', 6) \
                  <<QString::number(m_davggyrtemp, 'f', 2)<<QString::number(m_davgcaltemp, 'f', 2);
        QVector<double> vddatas;
        //vddatas.append(m_lcatCurrCounts);
        vddatas.append(m_davggyrx);
        vddatas.append(m_davggyry);
        vddatas.append(m_davggyrz);
        vddatas.append(m_davgcalx);
        vddatas.append(m_davgcaly);
        vddatas.append(m_davgcalz);
        vddatas.append(m_davggyrtemp);
        vddatas.append(m_davgcaltemp);
        //qDebug()<<slwriteval;
        writeCvsFile("A6A6_C" + QString::number(m_icatFreqCounts), m_slFreqKeys, slwriteval, 1);
        emit sigFreqUpdate(m_slFreqKeys, vddatas, m_sPortN, m_iProtoIndex);
        m_vGyrDatasx.clear();
        m_vGyrDatasy.clear();
        m_vGyrDatasz.clear();
        m_vCaliDatasx.clear();
        m_vCaliDatasy.clear();
        m_vCaliDatasz.clear();
        m_vGTempDatas.clear();
        m_vCTempDatas.clear();
    }else{
        m_vGyrDatasx.append(fogx);
        m_vGyrDatasy.append(fogy);
        m_vGyrDatasz.append(fogz);
        m_vCaliDatasx.append(calix);
        m_vCaliDatasy.append(caliy);
        m_vCaliDatasz.append(caliz);
        m_vGTempDatas.append(flogtemp);
        m_vCTempDatas.append(acceltemp);
    }

    dataValues.append(QString::number(m_davggyrx, 'f', 4));
    dataValues.append(QString::number(m_davggyry, 'f', 4));
    dataValues.append(QString::number(m_davggyrz, 'f', 4));
    dataValues.append(QString::number(m_davgcalx, 'f', 4));
    dataValues.append(QString::number(m_davgcaly, 'f', 4));
    dataValues.append(QString::number(m_davgcalz, 'f', 4));
    dataValues.append(QString::number(m_davggyrtemp, 'f', 2));
    dataValues.append(QString::number(m_davgcaltemp, 'f', 2));

    //串口调试
    if(m_bIsNeedShow){
        //qDebug()<<"A6A6:"<<m_slProtoKeys.size()<<dataValues.size();
        if(m_slProtoKeys.size() != dataValues.size()){
            return;
        }
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }
    m_bIsNeedKeys = false;

    //绘图数据
    QVector<double> vddata;
    //vddata.append(st_a5a500.pgidex);
    vddata.append(fogx);
    vddata.append(fogy);
    vddata.append(fogz);
    vddata.append(calix);
    vddata.append(calix);
    vddata.append(calix);
    vddata.append(flogtemp);
    vddata.append(acceltemp);
    vddata.append(m_davggyrx);
    vddata.append(m_davggyry);
    vddata.append(m_davggyrz);
    vddata.append(m_davgcalx);
    vddata.append(m_davgcaly);
    vddata.append(m_davgcalz);
    vddata.append(m_davggyrtemp);
    vddata.append(m_davgcaltemp);

    emit sigChartsUpdate(m_slProtoKeys, vddata, m_sPortN, m_iProtoIndex);

}

bool CA6A600Protocol::preInit(){
    CBaseProtocol::preInit();
    qDebug()<<"m_catFreqCounts"<<m_icatFreqCounts<<m_dCatFreq;
    return true;
}

