// xlsxchart.h

#ifndef QXLSX_CHART_H
#define QXLSX_CHART_H

#include "xlsxabstractooxmlfile.h"

#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <QtGlobal>

QT_BEGIN_NAMESPACE_XLSX

class AbstractSheet;
class Worksheet;
class ChartPrivate;
class CellRange;
class DrawingAnchor;

class QXLSX_EXPORT Chart : public AbstractOOXmlFile
{
    Q_DECLARE_PRIVATE(Chart)
public:
    enum ChartType {             // 16 type of chart (ECMA 376)
        CT_NoStatementChart = 0, // Zero is internally used for unknown types
        CT_<PERSON><PERSON>hart,
        CT_Area3<PERSON>hart,
        CT_<PERSON><PERSON><PERSON>,
        CT_<PERSON>3<PERSON><PERSON>,
        <PERSON>_<PERSON><PERSON>,
        CT_<PERSON><PERSON>,
        <PERSON>_<PERSON><PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON><PERSON><PERSON><PERSON>,
        CT_<PERSON><PERSON>hart,
        CT_<PERSON>3<PERSON><PERSON>,
        CT_<PERSON>ubble<PERSON>hart,
    };
    enum ChartAxisPos { None = (-1), Left = 0, Right, Top, Bottom };

private:
    friend class AbstractSheet;
    friend class Worksheet;
    friend class Chartsheet;
    friend class DrawingAnchor;

private:
    Chart(AbstractSheet *parent, CreateFlag flag);

public:
    ~Chart();

public:
    void addSeries(const CellRange &range,
                   AbstractSheet *sheet = nullptr,
                   bool headerH         = false,
                   bool headerV         = false,
                   bool swapHeaders     = false);
    void setChartType(ChartType type);
    void setChartStyle(int id);
    void setAxisTitle(Chart::ChartAxisPos pos, QString axisTitle);
    void setChartTitle(QString strchartTitle);
    void setChartLegend(Chart::ChartAxisPos legendPos, bool overlap = false);
    void setGridlinesEnable(bool majorGridlinesEnable = false, bool minorGridlinesEnable = false);

public:
    bool loadFromXmlFile(QIODevice *device) override;
    void saveToXmlFile(QIODevice *device) const override;
};

QT_END_NAMESPACE_XLSX

#endif // QXLSX_CHART_H
