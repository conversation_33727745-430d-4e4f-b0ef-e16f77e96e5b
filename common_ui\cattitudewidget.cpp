﻿#include "cattitudewidget.h"

CAttitudeWidget::CAttitudeWidget(QWidget *parent) : QWidget(parent), m_fpitch(0), m_froll(0) {

}

void CAttitudeWidget::setAttitude(float pitch, float roll) {
    this->m_fpitch = pitch;
    this->m_froll = roll;
    update();
}

void CAttitudeWidget::paintEvent(QPaintEvent *event) {
    Q_UNUSED(event);
    //qDebug()<<"AT:"<<width()<<height();
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    // 设置圆角矩形的路径
    drawMarinGrid(painter);
    drawAlVal(painter, 0, 0, 0);
    QPainterPath path;
    QRect rect(width()*0.2, 0, width()*0.6, height()); // 矩形区域
    int radius = 20; // 圆角半径
    path.addRoundedRect(rect, radius, radius);
    painter.setClipPath(path); // 设置裁剪路径

    drawAttitudeIndicator(painter);
}

void CAttitudeWidget::drawAttitudeIndicator(QPainter &painter) {
    //int side = qMin(width(), height());
    int side = sqrt(pow( width(), 2) + pow(height(), 2));
    int centerX = width() / 2;
    int centerY = height() / 2;

    painter.save();
    painter.translate(centerX, centerY);
    painter.rotate(m_froll);
    //qDebug()<<"AT:"<<centerX<<centerY;
    drawHorizon(painter, m_fpitch, side);
    drawPitchScale(painter, m_fpitch, side);
    painter.restore();

    drawFixedElements(painter, centerX, centerY, side);
}

void CAttitudeWidget::drawHorizon(QPainter &painter, float pitch, int side) {
    QColor skyColor("#007fd0");
    QColor groundColor("#d49b00");
    float pitchOffset = (pitch / 90.0f) * (side / 2);

    //qDebug()<<"pitch:"<<pitch<<side<<-side / 2 + pitchOffset;

    painter.setPen(Qt::NoPen);
    painter.setBrush(skyColor);
    painter.drawRect(-side, -side + pitchOffset, side * 2, side);

    painter.setBrush(groundColor);
    painter.drawRect(-side, pitchOffset, side * 2, side);

    painter.setBrush(Qt::white);
    painter.drawRect(-side, pitchOffset, side * 2, 3);
}

void CAttitudeWidget::drawPitchScale(QPainter &painter, float pitch, int side) {
    painter.setPen(QPen(Qt::white, 2));
    QFont font = painter.font();
    font.setPointSize(10);
    painter.setFont(font);

    float scaleSpacing = 20.0f;
    for (int i = -90; i <= 90; i += 10) {
        float yOffset = (pitch - i ) / 90.0f * (side / 2); // 修正位置计算

        if (i % 30 == 0) {
            painter.drawLine(-30, yOffset, 30, yOffset);
            painter.drawText(35, yOffset + 5, QString::number(i));
            painter.drawText(-65, yOffset + 5, QString::number(i));
        } else {
            painter.drawLine(-15, yOffset, 15, yOffset);
        }
    }
}

void CAttitudeWidget::drawFixedElements(QPainter &painter, int centerX, int centerY, int side) {
    painter.setPen(QPen(QColor("#D9D919"), 2));
    drawRollScale(painter, centerX, centerY, 400);
    drawArraw(painter, centerX, centerY, 300);
}

void CAttitudeWidget::drawMarinGrid(QPainter &painter){
    painter.save();
    painter.setPen(QPen(QColor("#C0C0C0"), 3));
    painter.drawLine( 0, 0, width() * 0.18, height() * 0.25);
    painter.drawEllipse(width() * 0.18, height() * 0.25, 8, 8);
    painter.drawLine( 0, height() * 0.5, width() * 0.18, height() * 0.5);
    painter.drawLine( 0, height(), width() * 0.18, height() * 0.75);
    painter.drawEllipse(width() * 0.18, height() * 0.75, 8, 8);
    painter.drawLine( width() * 0.18, height() * 0.25, width() * 0.18, height() * 0.75);

    painter.drawLine( width(), 0, width() * 0.82, height() * 0.25);
    painter.drawEllipse(width() * 0.82 - 8, height() * 0.25, 8, 8);
    painter.drawLine( width(), height() * 0.5, width() * 0.82, height() * 0.5);
    painter.drawLine( width(), height(), width() * 0.82, height() * 0.75);
    painter.drawEllipse(width() * 0.82 - 8, height() * 0.75, 8, 8);
    painter.drawLine( width() * 0.82, height() * 0.25, width() * 0.82, height() * 0.75);
    painter.restore();
}

void CAttitudeWidget::drawAlVal(QPainter &painter, double pitch, double roll, double heading){
    int textw = width() * 0.18;
    int texth = 40;

    painter.setPen("#4C2F7A"); // 文本框边框和文本用绿色
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.setBrush(QColor("#f2e1e7"));// 文本框背景用米色
    QRect pitchrect(0, height() * 0.5, textw, texth);
    //painter.drawRect(pitchrect);

    //// 添加上面的圆角矩形部分
    int radius = 20; // 圆角的半径
    QPainterPath path;
    path.addRoundedRect(pitchrect.x(), pitchrect.y() - 2* texth, pitchrect.width(), texth * 4, radius, radius, Qt::AbsoluteSize);
    painter.drawPath(path.simplified());
    path.clear();

    //painter.drawRect(pitchrect.x(), pitchrect.y() - texth, pitchrect.width(), pitchrect.height());
    painter.drawText(pitchrect.x(), pitchrect.y() - texth, pitchrect.width(), pitchrect.height(), Qt::AlignCenter, tr("Pitch"));
    painter.drawText(pitchrect, Qt::AlignCenter, QString::number(m_fpitch, 'f', 4));
    painter.drawLine(pitchrect.x(), pitchrect.y(), textw, pitchrect.y());

    QRect rollrect(width() * 0.82, height() * 0.5, textw, texth);

    path.addRoundedRect(rollrect.x(), rollrect.y() - 2* texth, rollrect.width(), texth * 4, radius, radius, Qt::AbsoluteSize);
    painter.drawPath(path.simplified());
    path.clear();

    //painter.drawRect(rollrect);
    //painter.drawRect(rollrect.x(), rollrect.y() - texth, rollrect.width(), rollrect.height());
    painter.drawText(rollrect.x(), rollrect.y() - texth, rollrect.width(), rollrect.height(), Qt::AlignCenter, tr("Roll"));
    painter.drawText(rollrect, Qt::AlignCenter, QString::number(m_froll, 'f', 4));
    painter.drawLine(rollrect.x() + textw, rollrect.y(), textw, rollrect.y());

}

void CAttitudeWidget::drawRollScale(QPainter &painter, int centerX, int centerY, int side) {
    painter.setPen(QPen(Qt::white, 2));
    const int radius = side / 2 - 20; // 刻度线末端到中心的距离
    const int arcradius = radius - 20;

    // 定义圆弧的参数
    int startAngle = -330 * 16; // 起始角度（90度，Qt中角度单位为1/16度）
    int spanAngle =  120 * 16; // 圆弧跨度（140度，负值表示顺时针绘制）

    // 计算绘制区域
    QRectF rect(centerX - arcradius, centerY - arcradius, arcradius * 2, arcradius * 2);

    // 绘制圆弧
    painter.drawArc(rect, startAngle, spanAngle);

    for (int angle = 0; angle < 360; angle += 10) {
        painter.save();
        painter.translate(centerX, centerY);
        painter.rotate(angle); // 旋转到当前刻度角度

        if(angle <= 60 || angle >= 300){

        // 绘制刻度线
            if (angle % 30 == 0) {
                painter.drawLine(0, -radius, 0, -radius + 20); // 长刻度
                // 绘制刻度文本（需要额外处理旋转）
                //painter.save();
                //painter.translate(0, -radius + 30); // 移动到刻度末端外侧
                //painter.rotate(-angle); // 抵消外部旋转，让文本保持水平
                //painter.drawText(-15, 5, QString::number(angle)); // 居中文本
                //painter.restore();
            } else {
                painter.drawLine(0, -radius, 0, -radius + 10); // 短刻度
            }
        }

        painter.restore();
    }
}

void CAttitudeWidget::drawArraw(QPainter &painter, int centerX, int centerY, int side){
    // 设置画笔和画刷
    painter.setPen(Qt::black); // 边框颜色
    painter.setBrush(Qt::yellow); // 填充颜色

    //painter.drawLine(centerX - 20, centerY, centerX + 20, centerY);
    //painter.drawLine(centerX, centerY - 20, centerX, centerY + 20);

    //绘制左上三角形
    QPolygon ltriangle;
    ltriangle << QPoint(centerX, centerY) // 顶部
             << QPoint(centerX, centerY * 1.05) // 左下角
             << QPoint(centerX * 0.7, centerY*1.2); // 右下角
    painter.drawPolygon(ltriangle);

    //绘制右上三角形
    QPolygon rtriangle;
    rtriangle << QPoint(centerX, centerY) // 顶部
             << QPoint(centerX, centerY * 1.05) // 左下角
             << QPoint(centerX * 1.3, centerY * 1.2); // 右下角
    painter.drawPolygon(rtriangle);
    //
    //// 绘制右侧翼三角形
    QPolygon bottomWing;
    bottomWing << QPoint(centerX, centerY * 1.05)
              << QPoint(centerX * 0.7, centerY * 1.2)
              << QPoint(centerX, centerY * 1.1)
              << QPoint(centerX * 1.3, centerY * 1.2);
    painter.drawPolygon(bottomWing);
}

