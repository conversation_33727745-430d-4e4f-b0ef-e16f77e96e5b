﻿#include "cresolvingtask.h"
#include "cprotocolfactory.h"
#include <QDebug>

CResolvingTask::CResolvingTask(QObject *parent) : QThread(parent)
{
    for(QString var:g_sProtocolHead){
        m_bProtocolHead.append(QByteArray::fromHex(var.toLocal8Bit()));
    }

    qDebug()<<"CResolvingTask"<<QThread::currentThreadId();

    m_bProtoIsInit = false;
    m_mArrLock = new QMutex;
    m_bprotocol = NULL;
    m_RecvData.clear();

}

CResolvingTask::~CResolvingTask(){
    qDebug()<<"~CResolvingTask:"<<this;
    delete  m_bprotocol;
    delete  m_mArrLock;
    exit();
    wait();
}

void CResolvingTask::run(){
    QByteArray bhead;
    int uMsgLen = 0;
    int indxpos = 0;
    int donepos = 0;
    QByteArray leftbuf;
    QByteArray tempbuf;
    int datalen = 0;
    m_doingData.clear();
    while (!isInterruptionRequested()) {
        //qDebug()<<"CResolvingTask:"<<uMsgLen<<m_RecvData.size();
        if(!m_bProtoIsInit || m_bprotocol == NULL || m_RecvData.size() < uMsgLen){
            msleep(1);
        }else {
            uMsgLen = m_bprotocol->getProtoLength();
            bhead = m_bprotocol->getProtoHead();
            //qDebug()<<"doingData size:"<<m_RecvData.size()<<m_doingData.size()<<donepos<<uMsgLen;
            m_doingData.remove(0, donepos);
            donepos = 0;
            m_mArrLock->lock();
            //tempbuf = m_RecvData;
            //qDebug()<<"m_RecvData:"<<m_RecvData.size();
            m_doingData.append(m_RecvData);
            tempbuf = m_RecvData;
            m_RecvData.clear();
            datalen = m_doingData.size();
            m_mArrLock->unlock();

            //int uLoop = m_RecvData.size() / uMsgLen;
            //for(int i = 0; i < uLoop; i++){
                //qDebug()<<"run:"<<m_RecvData.size()<<i;
            while(datalen > 0 && (indxpos = m_doingData.indexOf(bhead, donepos)) != -1){
                if(datalen - indxpos < uMsgLen){
                    //qDebug()<<"doingData:"<<doingData.size()<<indxpos;
                    //m_bleftbuf = doingData.mid(indxpos, doingData.size() - indxpos);
                    //qDebug()<<"loss msg left:"<<m_bleftbuf.toHex(' ');
                    break;
                }
                m_bprotocol->paseMsg(m_doingData.mid(indxpos, uMsgLen));
                //写完未定义格式的二进制数据后直接退出循环
                if(uMsgLen == -1){
                    donepos = datalen;
                    break;
                }
                if(m_bprotocol->getFrameErr() != E_FRAME_OK){
                    //qDebug()<<"loss msg tempbuf:"<<tempbuf.toHex(' ');
                    //qDebug()<<"loss msg doingData:"<<m_doingData.toHex();
                    //qDebug()<<"loss msg left:"<<leftbuf.toHex(' ');
                    m_bprotocol->setFrameErr(E_FRAME_OK);
                }
                donepos = indxpos + uMsgLen;
            }

            //}
            indxpos = 0;
        }
        //qDebug()<<"m_RecvData size:"<<m_RecvData.size();
    }
}

void CResolvingTask::initProtocol(const QByteArray &barr){
    for(int i = 0; i < m_bProtocolHead.size(); i++){
        if((m_iStartPos = barr.indexOf(m_bProtocolHead.at(i))) != -1){
            qDebug()<<"initProtocol:"<<i;
             m_bprotocol = CProtocolFactory::getProtocol(static_cast<CProtocolFactory::E_HEADPROTO>(i));
             if(m_bprotocol != NULL){
                m_bprotocol->writeProtocoIndex(i);
                m_bprotocol->setProtoLength(barr.mid(m_iStartPos, barr.size() - m_iStartPos));
                int msglen = m_bprotocol->getProtoLength();
                int msgcount = (barr.size() - m_iStartPos) / msglen;
                //防止报文中包含了其他的头导致误判
                if(msglen == 1 || (msgcount > 1 && barr.count(m_bProtocolHead.at(i)) < msgcount/2)){
                    qDebug()<<msgcount<<barr.count(m_bProtocolHead.at(i))<<barr.size()<<msglen<<barr.toHex(' ');
                    delete m_bprotocol;
                    m_bprotocol = NULL;
                    continue;
                }
                m_bprotocol->setPortN(m_sPortN);
                m_bprotocol->setProtoParam(m_mPParamMap);
                m_bprotocol->preInit();
                qDebug()<<"initProtocol len:"<<m_bprotocol->getProtoLength();
                m_bProtoIsInit = true;
                m_RecvData.clear();
             }else{
                 //如果走到这里，说明定义了头枚举，但是没有增加创建对应对象的工厂逻辑
                 qDebug()<<"get protocol fail:"<<i;
             }
             break;
        }
    }

    //当已经连续接收到了512个字节的数据，并且无法找到已定义格式，则确定为未定义格式数据
    if(m_RecvData.size() > 512){
        m_bprotocol = CProtocolFactory::getOtherProtocol();
        m_bprotocol->setProtoLength(barr);
        m_bprotocol->setPortN(m_sPortN);
        m_bprotocol->setProtoParam(m_mPParamMap);
        m_bprotocol->preInit();
        m_bProtoIsInit = true;
        m_RecvData.clear();
    }
}

void CResolvingTask::slotAppendData(const QByteArray &barr){
    //qDebug()<<"slotAppendData:"<<barr.toHex(' ');
    if(!m_bProtoIsInit){
        //initProtocol(barr);
        initProtocol(m_RecvData);

        if(m_bprotocol == NULL){
            m_RecvData.append(barr);
            //qDebug()<<"invalid msg:"<<barr.mid(0, 4).toHex(' ')<<m_RecvData.size();
            return;
        }
    }

    m_mArrLock->lock();
    if(m_iStartPos != 0){
        m_RecvData.append(barr.mid(m_iStartPos, barr.size() - m_iStartPos));
        m_iStartPos = 0;
    }else{
        m_RecvData.append(barr);
        //qDebug()<<"msg:"<<barr.toHex(' ')<<m_RecvData.size();
    }
    m_mArrLock->unlock();

    /*if(m_RecvData.size() < MIN_LENGTH){
        return;
    }
    */

}

void CResolvingTask::clearBuff(){
    qDebug()<<"clear clearBuff time";
    m_mArrLock->lock();
    m_RecvData.clear();
    m_doingData.clear();
    m_mArrLock->unlock();
    if(m_bprotocol != NULL){
        qDebug()<<"clear time";
        m_bprotocol->setRunTimes(0);
    }
}

void CResolvingTask::clear(bool filesave){
    if(m_bprotocol != NULL){
        m_bprotocol->setFileSave(filesave);
        delete m_bprotocol;
        m_bprotocol = NULL;
    }
    m_mArrLock->lock();
    m_RecvData.clear();
    m_bProtoIsInit = false;
    m_mArrLock->unlock();
}

void CResolvingTask::setPortN(QString portn){
    m_sPortN = portn;
}

void CResolvingTask::slogParamChange(const QMap<QString, QString> &param){
    m_mPParamMap = param;
    qDebug()<<"slogParamChange"<<QThread::currentThreadId()<<this;
    if(m_bprotocol != NULL){
        m_bprotocol->setProtoParam(param);
    }
}
