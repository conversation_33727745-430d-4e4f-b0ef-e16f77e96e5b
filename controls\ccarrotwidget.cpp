﻿#include "ccarrotwidget.h"
#include "ui_ccarrotwidget.h"

CCarRotWidget::CCarRotWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CCarRotWidget)
{
    ui->setupUi(this);

    setObjectName("CCarRotWidget");

    QPixmap pixmap=QPixmap(":/img/platebackgd.jpeg").scaled(this->size());
    QPalette palette;
    //设置窗口背景图片
    palette.setBrush(QPalette::Window,QBrush(pixmap));
    //设置窗口背景颜色
    palette.setColor(QPalette::Window,QColor(255, 150, 30));
    this->setPalette(palette);

    //创建 QGraphicsScene
    QGraphicsScene *scene_top = new QGraphicsScene;
    QGraphicsScene *scene_side = new QGraphicsScene;
    QGraphicsScene *scene_back = new QGraphicsScene;

    //加载图像到 QPixmap
    QPixmap pixmap_top(":/img/Car_Top.png");
    QPixmap pixmap_side(":/img/Car_Side.png");
    QPixmap pixmap_back(":/img/Car_Back.png");

    // 创建 QGraphicsPixmapItem 并将图像加载到 QGraphicsPixmapItem 中
    pixmapItem_top = new QGraphicsPixmapItem(pixmap_top);
    pixmapItem_side = new QGraphicsPixmapItem(pixmap_side);
    pixmapItem_back = new QGraphicsPixmapItem(pixmap_back);

    // 设置图像的旋转中心点为图像中心
    pixmapItem_top->setTransformOriginPoint(pixmap_top.width() / 2, pixmap_top.height() / 2);
    pixmapItem_side->setTransformOriginPoint(pixmap_side.width() / 2, pixmap_side.height() / 2);
    pixmapItem_back->setTransformOriginPoint(pixmap_back.width() / 2, pixmap_back.height() / 2);

    // 将 QGraphicsPixmapItem 添加到 QGraphicsScene 中
    scene_top->addItem(pixmapItem_top);
    scene_side->addItem(pixmapItem_side);
    scene_back->addItem(pixmapItem_back);

    // 创建 QGraphicsView，并将 QGraphicsScene 设置为它的场景
    ui->graphicsView_yaw->setScene(scene_side);
    ui->graphicsView_yaw->setRenderHint(QPainter::Antialiasing);
    ui->graphicsView_pitch->setScene(scene_top);
    ui->graphicsView_pitch->setRenderHint(QPainter::Antialiasing);
    ui->graphicsView_roll->setScene(scene_back);
    ui->graphicsView_roll->setRenderHint(QPainter::Antialiasing);

    rotationAngle = 0.0;
}

CCarRotWidget::~CCarRotWidget()
{
    delete ui;
}
