﻿#include "ccarplatewidget.h"
#include <QPainter>
#include <QDebug>
#include <QResizeEvent>

CCarplateWidget::CCarplateWidget(QWidget *parent) :
    QLabel(parent)
{

    setObjectName("CCarplateWidget");

    borderOutColorStart = QColor(255, 255, 255);
    borderOutColorEnd = QColor(166, 166, 166);

    borderCenterColorStart = QColor(192, 192, 192);
    //borderCenterColorEnd = QColor(105, 105, 105);
    borderCenterColorEnd = QColor(192, 192, 192);

    borderInColorStart = QColor(166, 166, 166);
    borderInColorEnd = QColor(255, 255, 255);

    int width=this->width();
    int height=this->height();//移动仪表盘的高度

    m_iside = qMin(width, height);
    //qDebug()<<"CCarplateWidget"<<width<<height<<m_iside;
    m_iradius = m_iside / 2 + 30;

    m_spdegRotate = 0.0;
    m_hgdegRotate = 0.0;

    //setStyleSheet("background-color:#E9C2A6");

}



CCarplateWidget::~CCarplateWidget()
{

}

void CCarplateWidget::resizeEvent(QResizeEvent *e){
    //窗口大小发生变化，需要重新调整参数并绘制
    int width=this->width();
    int height=this->height();//移动仪表盘的高度

    m_iside = qMin(width, height);
    //qDebug()<<"CCarplateWidget"<<width<<height<<m_iside;
    m_iradius = m_iside / 2 + 30;
    update();
}

void CCarplateWidget::paintEvent(QPaintEvent* e)
{
    QPainter painter(this);
    drawSpeedPlate(painter);
    drawAltitudePlate(painter);
    QWidget::paintEvent(e);
}

void CCarplateWidget::drawAltitudePlate(QPainter &painter){
    painter.save();

    int radius= m_iradius;//仪表盘的中心位置
    //移动画笔到中下方
    painter.translate(width() * 1/4, height()/2);
    painter.scale(0.7, 0.7);

    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setPen(Qt::NoPen);

    QColor bgcolor = QColor("#42426F");
    DrawBackground(painter, radius, bgcolor);
    ////绘制外边框
    drawBorderOut(&painter);
    ////绘制中边框
    drawBorderCenter(&painter);
    ////绘制内边框
    drawBorderIn(&painter);

    DrawSmallScaletwo(painter,radius * 0.83, 360, 180, 1.8);//刻度线
    QStringList sldata;
    for (int i = 0; i < 20; i++) {
        sldata.append(genAltitudeDigital(i));
    }
    DrawDigitalTwo(painter,radius * 0.75, 20, 180, sldata, 18);//刻度数字

    DrawCircle(painter,radius * 0.9);      //渐变发光外扇形
    //DrawCircle_arc(painter,radius * 0.88, m_heightdegRotate);//动态扇形环
    //qDebug()<<"introtate"<<m_hgdegRotate;
    QString shl;
    double hhval = 0.0;
    double lhval = 0.0;
    if(m_hgdegRotate < 99.99){
        shl = "99.99";
        hhval = static_cast<int>(m_hgdegRotate) / 10.0;
        lhval = static_cast<int>(m_hgdegRotate * 100) % 100 / 10.0;
    }else if(m_hgdegRotate < 999.9){
        shl = "999.9";
        hhval = static_cast<int>(m_hgdegRotate) / 100.0;
        lhval = static_cast<int>(m_hgdegRotate * 10) % 100 / 10.0;
    }else if(m_hgdegRotate < 9999){
        shl = "9999";
        hhval = static_cast<int>(m_hgdegRotate) / 1000.0;
        lhval = static_cast<int>(m_hgdegRotate) % 100 / 10.0;
    }else{
        hhval = 10.0;
        lhval = 10.0;
    }
    DrawPointer(painter,radius * 0.63, hhval * 18, 90);//指针 小数
    DrawsecondPointer(painter,radius * 0.63, lhval * 18, 90);//指针二 整数
    DrawCircle_line(painter,radius * 0.9, 360); //最外细圆线
    //DrawCircle_bom_big(painter,radius *0.57);//中间大圆
    DrawCircle_bom_shine(painter,radius * 0.34);//渐变发光内圈
    drawAltitudeHL(painter, -radius * 0.40, shl);
    //DrawCircle_bom_small(painter,radius * 0.43);//中间小圆

    DrawUnit(painter,-radius * 0.55, tr("Altimeter"), "m");//单位
    DrawNum(painter, -radius * 0.20, m_hgdegRotate);//时速
    painter.restore();
}

void CCarplateWidget::drawAltitudeHL(QPainter &painter, int radius, QString hl){
    //qDebug()<<"drawAltitudeHL"<<hl;
    painter.setPen(Qt::yellow);
    painter.drawText(-60, radius, 120, 40,Qt::AlignCenter, hl);
}

void CCarplateWidget::drawSpeedPlate(QPainter &painter){
    int width = this->width();
    int height = this->height();
    painter.save();
    int radius= m_iradius;//仪表盘的中心位置
    //移动画笔到中下方
    m_iside = qMin(width, height);
    painter.translate(width* 3/ 4,height/2);

    painter.scale(0.7, 0.7);

    //启用反锯齿
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setPen(Qt::NoPen);
    //设置画刷颜色
    painter.setBrush(QColor(138,43,226));

    //qDebug()<<"radius"<<radius;

    QColor bgcolor = QColor(99, 33, 0);
    DrawBackground(painter, radius, bgcolor);
    //绘制外边框
    drawBorderOut(&painter);
    //绘制中边框
    drawBorderCenter(&painter);
    //绘制内边框
    drawBorderIn(&painter);

    /*DrawSmallScale(painter,radius-60);//刻度线
    DrawDigital(painter,radius-90);//刻度数字
    //所有形状绘画
    //DrawCircle_bom(painter,radius-40);  //扇形大圆
    DrawCircle(painter,radius-35);      //渐变发光外扇形
    DrawCircle_arc(painter,radius - 40);//动态扇形环
    DrawPointer(painter,radius-130);//指针
    DrawCircle_line(painter,radius-35); //最外细圆线
    DrawCircle_bom_big(painter,radius-150);//中间大圆
    DrawCircle_bom_shine(painter,radius - 230);//渐变发光内圈
    DrawCircle_bom_small(painter,radius - 200);//中间小圆

    DrawUnit(painter,radius -390);//单位
    DrawNum(painter,radius - 300);//时速
    */

    DrawSmallScale(painter,radius * 0.83, 121, 210, 2);//刻度线
    QStringList sldata;
    for (int i = 0; i < 13; i++) {
        sldata.append(genSpeedDigital(i));
    }
    DrawDigitalOne(painter,radius * 0.75, 13, 210, sldata, 20);//刻度数字
    //所有形状绘画
    //DrawCircle_bom(painter,radius-40);  //扇形大圆
    DrawCircle(painter,radius * 0.9);      //渐变发光外扇形
    DrawCircle_arc(painter,radius * 0.88, m_spdegRotate);//动态扇形环
    DrawPointer(painter,radius * 0.63, m_spdegRotate, 120);//指针
    DrawCircle_line(painter,radius * 0.9, 240); //最外细圆线
    //DrawCircle_bom_big(painter,radius *0.57);//中间大圆
    DrawCircle_bom_shine(painter,radius * 0.34);//渐变发光内圈
    //DrawCircle_bom_small(painter,radius * 0.43);//中间小圆

    DrawUnit(painter,-radius * 0.55 ,tr("Velometer"), "km/h");//单位
    DrawNum(painter, -radius * 0.20, m_spdegRotate);//时速
    painter.restore();
}

void CCarplateWidget::drawBorderOut(QPainter *painter)
{
    int outradius = m_iradius + 20;
    int centerradius = m_iradius + 10;
    painter->save();
    painter->setPen(Qt::NoPen);
    QLinearGradient borderGradient(0, -outradius, 0, outradius);
    borderGradient.setColorAt(0, borderOutColorStart);
    borderGradient.setColorAt(1, borderOutColorEnd);
    painter->setBrush(borderGradient);
    QPainterPath p1;
    p1.addEllipse(-outradius, -outradius, outradius * 2, outradius * 2);
    QPainterPath p2;
    p2.addEllipse(-centerradius, -centerradius, centerradius * 2, centerradius * 2);
    QPainterPath p3 = p1 - p2;
    painter->drawPath(p3);
    painter->restore();
}

void CCarplateWidget::drawBorderCenter(QPainter *painter)
{
    int outradius = m_iradius + 10;
    int centerradius = m_iradius;
    painter->save();
    painter->setPen(Qt::NoPen);
    QLinearGradient borderGradient(0, -outradius, 0, outradius);
    borderGradient.setColorAt(0, borderCenterColorStart);
    borderGradient.setColorAt(1, borderCenterColorEnd);
    painter->setBrush(borderGradient);
    QPainterPath p1;
    p1.addEllipse(-outradius, -outradius, outradius * 2, outradius * 2);
    QPainterPath p2;
    p2.addEllipse(-centerradius, -centerradius, centerradius * 2, centerradius * 2);
    QPainterPath p3 = p1 - p2;
    painter->drawPath(p3);
    painter->restore();
}

void CCarplateWidget::drawBorderIn(QPainter *painter)
{
    int inradius = m_iradius;
    int centerradius = m_iradius - 10;
    painter->save();
    painter->setPen(Qt::NoPen);
    QLinearGradient borderGradient(0, -inradius, 0, inradius);
    borderGradient.setColorAt(0, borderInColorStart);
    borderGradient.setColorAt(1, borderInColorEnd);
    painter->setBrush(borderGradient);
    QPainterPath p1;
    p1.addEllipse(-inradius, -inradius, inradius * 2, inradius * 2);
    QPainterPath p2;
    p2.addEllipse(-centerradius, -centerradius, centerradius * 2, centerradius * 2);
    QPainterPath p3 = p1 - p2;
    painter->drawPath(p3);
    painter->restore();
}


void CCarplateWidget::DrawBackground(QPainter& painter,int radius, QColor &color){
    //保存绘图对象
    painter.save();
    painter.setPen(Qt::NoPen);
    painter.setBrush(color);
    painter.drawEllipse(-radius, -radius, radius * 2, radius * 2);
    painter.restore();
}

//绘制外圈点
void CCarplateWidget::DrawPoint(QPainter& painter,int radius)
{
    //组装点的路径图
    QPainterPath pointPath;
    pointPath.moveTo(-2,-2);
    pointPath.lineTo(2,-2);
    pointPath.lineTo(2,2);
    pointPath.lineTo(0,4);
    pointPath.lineTo(-2,2);
    //绘制13个小点
    for(int i=0;i<13;++i){
        QPointF point(0,0);
        painter.save();
        painter.setBrush(QColor(250,252,78));
        //计算并移动绘图对象中心点
        point.setX(radius*qCos(((210-i*20)*M_PI)/180));
        point.setY(radius*qSin(((210-i*20)*M_PI)/180));
        //计算并移动绘图对象的中心点
        painter.translate(point.x(),-point.y());
        //计算并选择绘图对象坐标
        painter.rotate(-120+i*20);
        //绘制路径
        painter.drawPath(pointPath);
        painter.restore();
    }
}

//void MainWindow::DrawDigital(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle)
//{
//    //设置画笔，画笔默认NOPEN
//    painter.setPen(QColor(255,255,255));
//    QFont font;
//    font.setFamily("Arial");
//    font.setPointSize(10);
//    font.setBold(true);
//    painter.setFont(font);
//
//    if(sldata.size() < pointnum){
//        return;
//    }
//
//    for(int i=0;i<pointnum;++i){
//        QPointF point(0,0);
//        painter.save();
//        painter.setFont(QFont("Arial", 10));
//        painter.setPen(QPen(QColor(255,255,255)));
//        QFontMetricsF fm = QFontMetricsF(painter.font());
//        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
//        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
//        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
//        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
//        painter.translate(point.x(),-point.y());
//        painter.rotate((90 - startsite) + i*angle);
//        painter.drawText(-25, 0, 50, 20,Qt::AlignCenter,sldata.at(i));
//        painter.restore();
//    }
//    //还原画笔
//    painter.setPen(Qt::NoPen);
//}

void CCarplateWidget::DrawDigitalOne(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle)
{
    painter.save();
    //设置画笔，画笔默认NOPEN
    painter.setPen(QColor(255,255,255));
    QFont font;
    font.setFamily("Arial");
    font.setPointSize(16);
    font.setBold(true);
    painter.setFont(font);
    int Angle = 90;


    if(sldata.size() < pointnum){
        return;
    }

    QFontMetricsF fm = QFontMetricsF(painter.font());

    float gap = (360-Angle* 1.75) / 10;
    for(int i=0; i< pointnum; i+=1)
    {
        int angle = 60 + Angle + gap*i;  //角度,10格子画一个刻度值

        float angleArc =( angle % 360) * 3.14 / 180; //转换为弧度
        float x = (radius - 20 )*cos(angleArc);
        float y = (radius - 20 )*sin(angleArc);

        QString value = QString::number(i * 100);
        float w = (int)fm.width(sldata.at(i));
        float h = (int)fm.height();
        x = x - w/2;
        y = y + h/4;

        //qDebug()<< "x:"<<x<<" y:"<<y<<w<<h;
        //painter.drawPoint(QPointF(x, y));
        painter.drawText(QPointF(x, y),sldata.at(i));
    }
    //还原画笔
    painter.setPen(Qt::NoPen);
    painter.restore();

}

void CCarplateWidget::DrawDigitalTwo(QPainter& painter,int radius, int pointnum, int startsite, QStringList &sldata, float angle)
{
    painter.save();
    //设置画笔，画笔默认NOPEN
    painter.setPen(QColor(255,255,255));
    QFont font;
    font.setFamily("Arial");
    font.setPointSize(16);
    font.setBold(true);
    painter.setFont(font);
    int Angle = 90;


    if(sldata.size() < pointnum){
        return;
    }

    QFontMetricsF fm = QFontMetricsF(painter.font());

    int gap = (360-Angle* 2) / 10;
    for(int i=0; i< pointnum; i+=1)
    {
        int angle = 90 + Angle + gap*i;  //角度,10格子画一个刻度值

        float angleArc =( angle % 360) * 3.14 / 180; //转换为弧度
        int x = (radius - 10 )*cos(angleArc);
        int y = (radius - 10 )*sin(angleArc);

        QString value = QString::number(i*100);
        int w = (int)fm.width(value);
        int h = (int)fm.height();
        x = x - w/4;
        y = y + h/4;

        //qDebug()<< "x:"<<x<<" y:"<<y<<w<<h;
        //painter->drawPoint(QPointF(x, y));
        painter.drawText(QPointF(x, y),sldata.at(i));
    }
    //还原画笔
    painter.setPen(Qt::NoPen);
    painter.restore();

}



QString CCarplateWidget::genSpeedDigital(int i){
    return QString::number(i*20);
}

QString CCarplateWidget::genAltitudeDigital(int i){
    if(i < 11){
        return QString::number(i);
    }else{
        return QString::number(i - 20);
    }
}


void CCarplateWidget::DrawCircle_bom(QPainter& painter,int radius)
{
    //保存绘图对象
    painter.save();
    //计算大小圆路径
    QPainterPath outRing;
    outRing.moveTo(0,0);
    outRing.arcTo(-radius,-radius, 2*radius,2*radius,-30,240);
    outRing.closeSubpath();
    //设置画刷
    painter.setBrush(QColor(14,15,33));

    painter.drawPath(outRing);
    painter.restore();
}

void  CCarplateWidget::DrawCircle_bom_shine(QPainter& painter,int radius)
{
    painter.save();
    QRadialGradient radialGradient(0,0,radius,0,0);
//    radialGradient.setColorAt(0.5,QColor(8,77,197));
        radialGradient.setColorAt(0.5,QColor(10,68,185,150));
    radialGradient.setColorAt(1.0,Qt::transparent);
    painter.setBrush(QBrush(radialGradient));
    painter.drawRect(-radius,-radius,2*(radius),2*(radius));
    painter.restore();

}


void  CCarplateWidget::DrawCircle_bom_big(QPainter& painter,int radius)
{
    //保存绘图对象
    painter.save();
    //计算大小圆路径
    QPainterPath inRing;
    inRing.moveTo(0,0);
    inRing.addEllipse(-radius+50,-radius + 50,2*(radius-50),2*(radius-50));
    //设置画刷
    painter.setBrush(QColor(10,20,30));
    painter.drawPath(inRing);
    painter.restore();
}


void  CCarplateWidget::DrawCircle_bom_small(QPainter& painter,int radius)
{
    //保存绘图对象
    painter.save();
    //计算大小圆路径
    QPainterPath inRing;
    inRing.moveTo(0,0);
    inRing.addEllipse(-radius+50,-radius + 50,2*(radius-50),2*(radius-50));
    //设置画刷
    painter.setBrush(QColor(10,20,30));
    painter.drawPath(inRing);

    painter.restore();
}

void CCarplateWidget::DrawCircle_arc(QPainter& painter,int radius, float degRotate)
{

    QRect rect(-radius,-radius,2*radius,2*radius);
    QConicalGradient Conical(0,0,-70);

    Conical.setColorAt(0.1,QColor(255,88,127,200));//红色
    Conical.setColorAt(0.5,QColor(53,179,251,150));//蓝色
    painter.setBrush(Conical);
    painter.drawPie(rect,210*16,-(degRotate)*16);
}



void CCarplateWidget::DrawCircle(QPainter& painter,int radius)
{
    //保存绘图对象
    painter.save();
    //计算大小圆路径
    QPainterPath outRing;
    QPainterPath inRing;
    outRing.moveTo(0,0);
    inRing.moveTo(0,0);
    outRing.arcTo(-radius,-radius, 2*radius,2*radius,-30,240);
    inRing.addEllipse(-radius+50,-radius + 50,2*(radius-50),2*(radius-50));
    outRing.closeSubpath();
    //设置渐变色k
    QRadialGradient radialGradient(0,0,radius,0,0);
    radialGradient.setColorAt(1,QColor(0,82,199));
    radialGradient.setColorAt(0.92,Qt::transparent);
    //设置画刷
    painter.setBrush(radialGradient);
    //大圆减小圆
    painter.drawPath(outRing.subtracted(inRing));
    painter.restore();
}


void CCarplateWidget::DrawCircle_line(QPainter& painter,int radius, int tangle)
{
    //保存绘图对象
    painter.save();
    //计算大小圆路径
    QPainterPath outRing;
    QPainterPath inRing;
    outRing.moveTo(0,0);
    inRing.moveTo(0,0);
    outRing.arcTo(-radius,-radius, 2*radius,2*radius,-30, tangle);
    inRing.addEllipse(-radius+2,-radius+2,2*(radius-2),2*(radius-2));
    outRing.closeSubpath();

    //设置画刷
    painter.setBrush(QColor(5,228,255));
    //大圆减小圆
    painter.drawPath(outRing.subtracted(inRing));
    painter.restore();
}


//绘制刻度
void CCarplateWidget::DrawSmallScale(QPainter& painter,int radius, int pointnum, int startsite , float angle)
{
    //组装点的路径图
    QPainterPath pointPath_small;
    pointPath_small.moveTo(-2,-2);
    pointPath_small.lineTo(2,-2);
    pointPath_small.lineTo(2,4);
    pointPath_small.lineTo(-2,4);


    QPainterPath pointPath_big;
    pointPath_big.moveTo(-2,-2);
    pointPath_big.lineTo(2,-2);
    pointPath_big.lineTo(2,10);
    pointPath_big.lineTo(-2,10);

    //绘制121个小点
    for(int i=0;i<pointnum;i+=2){
        QPointF point(0,0);
        painter.save();
        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
        painter.translate(point.x(),-point.y());
        painter.rotate( (90 - startsite) +i*angle);

        if(i<80)
        {
          painter.setBrush(QColor(255,255,255));
        }
        if(i>=80)
        {
          painter.setBrush(QColor(235,70,70));
        }

        if(i%5 == 0)
        {
            painter.drawPath(pointPath_big);//绘画大刻度
        }else
        {
            painter.drawPath(pointPath_small);//绘画小刻度
        }
        painter.restore();
    }
}

//绘制刻度
void CCarplateWidget::DrawSmallScaletwo(QPainter& painter,int radius, int pointnum, int startsite , float angle)
{
    //qDebug()<<"pointnum"<<pointnum<<startsite<<angle;
    //组装点的路径图
    QPainterPath pointPath_small;
    pointPath_small.moveTo(-1,-1);
    pointPath_small.lineTo(0,-1);
    pointPath_small.lineTo(0,4);
    pointPath_small.lineTo(-1,4);

    QPainterPath pointPath_center;
    pointPath_center.moveTo(-1,-1);
    pointPath_center.lineTo(1,-1);
    pointPath_center.lineTo(1,8);
    pointPath_center.lineTo(-1,8);

    QPainterPath pointPath_big;
    pointPath_big.moveTo(-2,-2);
    pointPath_big.lineTo(2,-2);
    pointPath_big.lineTo(2,10);
    pointPath_big.lineTo(-2,10);
    int pointloop = pointnum / angle;
    int halfloop = pointloop / 2;

    //绘制121个小点
    for(int i=0;i < pointloop;i++){
        QPointF point(0,0);
        painter.save();
        point.setX(radius*qCos(((startsite-i*angle)*M_PI)/180));
        point.setY(radius*qSin(((startsite-i*angle)*M_PI)/180));
        painter.translate(point.x(),-point.y());
        painter.rotate( (90 - startsite) +i*angle);

        if(i<halfloop)
        {
          painter.setBrush(QColor(255,255,255));
        }
        if(i>=halfloop)
        {
          painter.setBrush(QColor(235,70,70));
        }

        if(i % 10 == 0)
        {
            painter.drawPath(pointPath_big);//绘画大刻度

        }else if( i % 5 == 0)
        {
            painter.drawPath(pointPath_center);//绘画中刻度
        }
        else
        {
            painter.drawPath(pointPath_small);//绘画小刻度
        }
        painter.restore();
    }
}

//绘制文字
void CCarplateWidget::DrawUnit(QPainter& painter,int radius, QString sname, QString sunit)
{
    painter.save();
    painter.setPen(QColor(255,255,255));
    QFont font;
    font.setFamily("Arial");
    font.setPointSize(14);
    font.setBold(true);
    QPen pen;
    pen.setColor(QColor("#63B8FF"));
    painter.setPen(pen);
    painter.setFont(font);
    painter.drawText(-60, radius, 120, 40,Qt::AlignCenter, sname);
    painter.drawText(-50, -radius - 20, 100, 20,Qt::AlignCenter, sunit);
    //painter.drawText(-60, -radius + 130, 120, 40,Qt::AlignCenter,QString(u8"当前车速"));

    //painter.setPen(QColor(255,255,255,50));
    //painter.drawText(-120, -radius + 280, 250, 40,Qt::AlignCenter,QString(u8"-请按space键加速-"));
    painter.restore();
}

//绘制实时时速数字
void CCarplateWidget::DrawNum(QPainter& painter,int radius, float degRotate)
{
    painter.save();
    painter.setPen(QColor(255,255,255));
    QFont font;
    font.setFamily("Arial");
    font.setBold(true);
    font.setPointSize(20);
    QPen pen;
    pen.setColor(QColor("#7DFC00"));
    painter.setPen(pen);
    painter.setFont(font);
    //painter.setBrush(QColor("#FFF8DC"));
    //painter.drawRect(-25, -radius + 20, 50, 30);
    painter.drawText(-75, -radius - 25, 150, 100,Qt::AlignCenter,QString::number(degRotate, 'f', 2));
    painter.restore();
}

//绘制指针
void CCarplateWidget::DrawPointer(QPainter& painter,int radius, float degRotate, int startsite)
{
    //组装点的路径图
    QPainterPath pointPath;
    pointPath.moveTo(10,0);
    pointPath.lineTo(1,-radius);
    pointPath.lineTo(-1,-radius);
    pointPath.lineTo(-10,0);
    pointPath.arcTo(-10,0,20,20,180,180);
    QPainterPath inRing;
    inRing.addEllipse(-5,-5,10,10);
    painter.save();

    //计算并选择绘图对象坐标
    painter.rotate(degRotate - startsite);
    painter.setBrush(QColor(255,255,255));
    painter.drawPath(pointPath.subtracted(inRing));
    painter.restore();
}

//绘制指针
void CCarplateWidget::DrawsecondPointer(QPainter& painter,int radius,float degRotate, int startsite)
{
    //qDebug()<<"draw pointer";
    //组装点的路径图
    //画指针
    painter.save();

    int Angle = 90;
    int percent = 0.8;

    QPolygon pts;
    int r = radius;
    pts.setPoints(5, -4, 0, 4, 0, 2, r/2, 0, r, -2, r/2);
    degRotate += Angle +  (360.0 - Angle - Angle) / 100 * percent;


    painter.rotate(degRotate);
    QRadialGradient haloGradient(0, 0, 60, 0, 0);  //辐射渐变，内部填充颜色
    haloGradient.setColorAt(0, QColor(100, 100, 100));
    haloGradient.setColorAt(1, QColor(250, 50, 50)); //red
    painter.setPen(QColor(250, 150, 150)); // 边框颜色
    painter.setBrush(haloGradient);
    painter.drawConvexPolygon(pts);
    painter.restore();
}

void CCarplateWidget::showspeedview(float fval){
    m_spdegRotate = fval;
    update();
}

void CCarplateWidget::showheightview(float fval){
    m_hgdegRotate = fval;
    update();
}

