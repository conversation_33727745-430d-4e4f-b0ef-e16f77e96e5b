﻿#ifndef CSTATUSLABEL_H
#define CSTATUSLABEL_H

#include <QObject>
#include <QPaintEvent>
#include <QLabel>

/*
* 转台状态展示控件
*/
class CStatusLabel : public QLabel
{
    Q_OBJECT
public:
    explicit CStatusLabel(QWidget *parent = nullptr);
    void paintEvent(QPaintEvent *) override;
    void resizeEvent(QResizeEvent *event) override;
    void paintRangle(QPainter &paint, QPen &pen, double startAngle, double spanAngle, const QBrush &br, const QString &str, const QPointF &sitoffset);
    void statFlush(QStringList sColor,QString stat);
signals:
private:
    QVector<QBrush> m_vBrush;
    QString m_sStatText;

};

#endif // CSTATUSLABEL_H
