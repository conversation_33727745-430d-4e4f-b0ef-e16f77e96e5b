﻿#ifndef CDATAANAPAGE_H
#define CDATAANAPAGE_H

#include <QWidget>
#include <QVector>
#include <cscrolllinechart.h>

namespace Ui {
class CDataAnaPage;
}

/*
* 数据视图页面
*/
class CDataAnaPage : public QWidget
{
    Q_OBJECT

public:
    explicit CDataAnaPage(QWidget *parent = nullptr);
    ~CDataAnaPage();
    void setWidVisibel(int index, bool isshow);
signals:

private slots:
    void on_bt_gencursor_clicked();

    void on_bt_delcursor_clicked();

    void on_cb_widsum_currentIndexChanged(int index);
    void on_cb_showparm_currentIndexChanged(int index);

    void on_bt_stoppaint_clicked();

    void on_bt_startpaint_clicked();

    void on_bt_openfile_clicked();

    void on_bt_loadfile_clicked();

    void slotLoadFile();

    void on_ch_realttdata_stateChanged(int arg1);

    void on_bt_showrange_clicked();

    void on_cb_portn_currentIndexChanged(const QString &arg1);    
    void on_cb_widsize_currentIndexChanged(const QString &arg1);

    void on_cb_widnum_currentIndexChanged(int index);

public slots:
    void slotDataShow(const QStringList datakeys, const QVector<double> dataValues, const QString sportN, const int protoindex);
    void slotReadFileEnd();

private:
    Ui::CDataAnaPage *ui;
    QString m_sComboxPortN;
    QString m_sCurrentPortN;
    int m_iCurrentDataindex;
    QMap<int, QVector<int>> m_mWidDataRalation;   //窗口与数据映射关系
    QVector<CScrollLineChart *> m_vSlcWids;
    QVector<QListWidget *> m_vLwcWids;
    QStringList m_slKeys;
    QStringList m_slLastKeys;
    QString m_sWorkFile;
    QTextCodec *m_tCode;
    QVector<QCheckBox *> m_vCheckbox;
    QDialog *m_keysDlg;
    QVector<QList<double> *> m_vslData;
    bool m_bIsOnline;
    CScrollLineChart *m_slcfirstwid;
    CScrollLineChart *m_slcsecondwid;
    CScrollLineChart *m_slcthirdwid;
    QListWidget *m_lwFirstwid;
    QListWidget *m_lwSecondwid;
    QListWidget *m_lwThirdwid;
    QVector<QStringList> m_vslLwData;
    QVector<QVector<QListWidgetItem *>> m_vvlwItems;
    QVector<double> m_phShowRange;   //水平显示范围
    QVector<double> m_pvShowRange;   //垂直显示范围
    QVector<int> m_vGraphNum;
    float m_fMarinrate;
    int m_iprotoindex;
};

#endif // CDATAANAPAGE_H
