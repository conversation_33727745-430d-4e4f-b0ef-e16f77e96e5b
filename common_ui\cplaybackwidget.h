﻿#ifndef CPLAYBACKWIDGET_H
#define CPLAYBACKWIDGET_H

#include <QWidget>

namespace Ui {
class CPlaybackWidget;
}

/*
*回放进度页面
*/
class CPlaybackWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CPlaybackWidget(QWidget *parent = nullptr);
    ~CPlaybackWidget();
    void setCurrentProcess(float currentnum);
    void reset();

private slots:
    void on_bt_startbt_clicked();
    void on_ch_isfollow_stateChanged(int arg1);
public slots:
    void slotpmoved(float x,int type);
signals:
    void sigFollowChange(bool);

private:
    Ui::CPlaybackWidget *ui;
    bool m_isPlay;
    int m_iTotlen;
    int pressstat;
};

#endif // CPLAYBACKWIDGET_H
