﻿#ifndef C3A0100PROTOCOL_H
#define C3A0100PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"
#include <QMap>
#include <QVector>

class C3a0100Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C3a0100Protocol(QObject *parent = nullptr);
    ~C3a0100Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool sum8CheckSum(const QByteArray msg);
    bool preInit();
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru3a0100{
        unsigned char  head;
        unsigned short sensorid;
        unsigned short cmd;
        unsigned short len;
        uint32_t timestamp;
        float CelebrationAcceleration_x;
        float CelebrationAcceleration_y;
        float CelebrationAcceleration_z;
        float Angularvelocity_x;
        float Angularvelocity_y;
        float Angularvelocity_z;
        float LinearAcceleration_x;
        float LinearAcceleration_y;
        float LinearAcceleration_z;
        float Quaternion_w;
        float Quaternion_x;
        float Quaternion_y;
        float Quaternion_z;
        unsigned short checksum;
        unsigned char fmt0d;
        unsigned char fmt0a;
    }Stru3a0100;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN, const int protoindex);
    void sigAttituUpdate(const QVector<double> attivalues, const QString portn);

private:
};

#endif // C3A0100PROTOCOL_H
