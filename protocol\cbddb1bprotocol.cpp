﻿#include "cbddb1bprotocol.h"
#include <QDebug>
#include <QWidget>
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "ccustomerviewpage.h"
#include "cdataanapage.h"
#include <QThread>
#include <QtMath>
#include <QByteArray>
#include <QBitArray>

Cbddb1bProtocol::Cbddb1bProtocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CCustomerViewPage *custompage = static_cast<CCustomerViewPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_CustomerView"));
    CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));

   if(dripage != NULL){
       qDebug()<<"connect Cbddb1bProtocol";
       connect(this, &Cbddb1bProtocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &Cbddb1bProtocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
       connect(this, &Cbddb1bProtocol::sigNavUpdate,  custompage, &CCustomerViewPage::slotDataShow);
       connect(this, &Cbddb1bProtocol::sigChartsUpdate, danapage, &CDataAnaPage::slotDataShow);
       connect(this, &Cbddb1bProtocol::sigAttituUpdate, custompage, &CCustomerViewPage::slotAttituShow);
   }
   m_slProtoKeys.append(tr("Roll") + ":");
   m_slProtoKeys.append(tr("Pitch") + ":");
   m_slProtoKeys.append(tr("Yaw") + ":");
   m_slProtoKeys.append(tr("Gyro") + "x:");
   m_slProtoKeys.append(tr("Gyro") + "y:");
   m_slProtoKeys.append(tr("Gyro") + "z:");
   m_slProtoKeys.append(tr("Accel") + "x:");
   m_slProtoKeys.append(tr("Accel") + "y:");
   m_slProtoKeys.append(tr("Accel") + "z:");
   m_slProtoKeys.append(tr("Latitude") + ":");
   m_slProtoKeys.append(tr("Longitude") + ":");
   m_slProtoKeys.append(tr("Altitude") + ":");
   m_slProtoKeys.append(tr("Evelocity") + ":");
   m_slProtoKeys.append(tr("Nvelocity") + ":");
   m_slProtoKeys.append(tr("Uvelocity") + ":");
   m_slProtoKeys.append(tr("Positionstatus") + ":");
   m_slProtoKeys.append(tr("Speedstatus") + ":");
   m_slProtoKeys.append(tr("Posturestatus") + ":");
   m_slProtoKeys.append(tr("Headingstatus") + ":");
   m_slProtoKeys.append(tr("Millisecondofweek") + ":");
   m_slProtoKeys.append(tr("Gnssweek") + ":");
   m_slProtoKeys.append(tr("Query_Type") + ":");
   m_slProtoKeys.append(tr("Temp") + ":");
   m_slProtoKeys.append(tr("StartNum") + ":");
   m_slProtoKeys.append(tr("Wheelspeed") + ":");
   m_slProtoKeys.append(tr("Rtkstatus") + ":");
   m_slProtoKeys.append(tr("LatStd") + ":");  //纬度标准差
   m_slProtoKeys.append(tr("LonStd") + ":");  //经度标准差
   m_slProtoKeys.append(tr("HStd") + ":");    //高度标准差
   m_slProtoKeys.append(tr("Vn_std") + ":");  //北向速度标准差
   m_slProtoKeys.append(tr("Ve_std") + ":");  //东向速度标准差
   m_slProtoKeys.append(tr("Vd_std") + ":");  //天向速度标准差
   m_slProtoKeys.append(tr("RollStd") + ":"); //横滚标准差
   m_slProtoKeys.append(tr("PitchStd") + ":");//俯仰标准差
   m_slProtoKeys.append(tr("YawStd") + ":");  //偏航标准差

   m_bshowview = true;

   connect(&m_plattimer, &QTimer::timeout, this, [=](){
       m_bshowview = true;
   });
   m_plattimer.start(100);
}

Cbddb1bProtocol::~Cbddb1bProtocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool Cbddb1bProtocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = sizeof(Strubddb1b);

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(4)<<barr.at(5);
}

bool Cbddb1bProtocol::preInit(){
    CBaseProtocol::preInit();
    emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") +QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail")+ QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
    return true;
}

void Cbddb1bProtocol::paseMsg(const QByteArray msg){
    Strubddb1b st_bddb0b;
    QStringList dataValues;
    QStringList navValues;
    int64_t i64High;
    uint32_t u32Mid;
    int64_t i64Val;
    int ival = 0;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    //qDebug()<< sizeof(Strubddb0b);

    //if(m_bIsNeedDat){
    //    writeDatFile("BDDB0B",msg);
    //}

    m_iReqCount++;

    //异或校验
    if(!sumEorCheck(msg, 0, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sumEorCheck error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    ::memcpy(&st_bddb0b, msg.data(), sizeof(Strubddb1b));

    float faccelx = st_bddb0b.accelx / 2147483648.0 * 50;  //50表示最大50G量程
    float faccely = st_bddb0b.accely / 2147483648.0 * 50;
    float faccelz = st_bddb0b.accelz / 2147483648.0 * 50;
    float fgyrox = st_bddb0b.gyrox / 2147483648.0 * 600;
    float fgyroy = st_bddb0b.gyroy / 2147483648.0 * 600;
    float fgyroz = st_bddb0b.gyroz / 2147483648.0 * 600;   //必须先除后乘，先乘会导致溢出，最终计算结果不正常
    //qDebug()<<st_bddb0b.gyroz<<fgyroz;
    double flatitude = st_bddb0b.latitude * 1.00e-07;
    double flongitude = st_bddb0b.longitude * 1.00e-07;
    double faltitude = st_bddb0b.altitude * 1.00e-03;
    float fpitch = st_bddb0b.pitch * 360.0 / 32768;
    float froll = st_bddb0b.roll * 360.0 / 32768;
    float fazimuth = st_bddb0b.azimuth * 360.0 / 32768;
    float fve = st_bddb0b.ve * 1e2 / 32768.0;
    float fvn = st_bddb0b.vn * 1e2 / 32768.0;
    float fvu = st_bddb0b.vu * 1e2 / 32768.0;

    //生成标定后数据的二进制文件
    QByteArray writeBytes;
    QDataStream stream(&writeBytes, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);

    if(m_bIsNeedDat){
        if(m_iDatType == 1){
            //stream << st_bddb0b.gps_time / 1000.0 * 0.25;
            //ival = fgyrox * qPow(10, 7);
            //stream << ival;
            //ival = fgyroy * qPow(10, 7);
            //stream << ival;
            //ival = fgyroz * qPow(10, 7);
            //stream << ival;
            //ival = faccelx * qPow(10, 7);
            //stream << ival;
            //ival = faccely * qPow(10, 7);
            //stream << ival;
            //ival = faccelz * qPow(10, 7);
            //stream << ival;

            stream << st_bddb0b.gps_time;
            ival = st_bddb0b.gyrox;
            stream << ival;
            ival = st_bddb0b.gyroy;
            stream << ival;
            ival = st_bddb0b.gyroz;
            stream << ival;
            ival = st_bddb0b.accelx;
            stream << ival;
            ival = st_bddb0b.accelx;
            stream << ival;
            ival = st_bddb0b.accelx;
            stream << ival;
            //qDebug()<<"writeBytes:"<<writeBytes.toHex(' ') << ival<<faccelz;
            writeDatFile("BDDB1B", writeBytes);
        }else{
            writeDatFile("BDDB1B",  msg);
        }
    }

    switch (st_bddb0b.type)
    {
        case 0:
            //----定位信息精度
            LatStd = QString::number(qPow(M_E, st_bddb0b.pdata1 / 100.0) * 10000 / 10000, 'f', 4);
            LonStd = QString::number(qPow(M_E, st_bddb0b.pdata2 / 100.0) * 10000 / 10000, 'f', 4);
            HStd =   QString::number(qPow(M_E, st_bddb0b.pdata3 / 100.0) * 10000 / 10000, 'f', 4);
            //qDebug()<<"LatStd:"<<st_bddb0b.pdata1<<" LonStd:"<<st_bddb0b.pdata2<<"HStd:"<<st_bddb0b.pdata3;
            break;
        case 1:
            //定速信息精度
            Vn_std = QString::number(qPow(M_E, st_bddb0b.pdata1 / 100.0) * 10000 / 10000, 'f', 4);
            Ve_std = QString::number(qPow(M_E, st_bddb0b.pdata2 / 100.0) * 10000 / 10000, 'f', 4);
            Vd_std = QString::number(qPow(M_E, st_bddb0b.pdata3 / 100.0) * 10000 / 10000, 'f', 4);
            //qDebug()<<"Vn_std:"<<st_bddb0b.pdata1<<" Ve_std:"<<st_bddb0b.pdata2<<"Vd_std:"<<st_bddb0b.pdata3;

            break;
        case 2:
            //姿态信息精度
            //RollStd =  QString::number(qPow(M_E, st_bddb0b.pdata1 / 100.0) * 10000 / 10000, 'f', 4);
            RollStd = "0.0000";
            PitchStd = QString::number(qPow(M_E, st_bddb0b.pdata2 / 100.0) * 10000 / 10000, 'f', 4);
            YawStd =   QString::number(qPow(M_E, st_bddb0b.pdata3 / 100.0) * 10000 / 10000, 'f', 4);
            //qDebug()<<"pitch:"<<st_bddb0b.pdata2<<" yaw:"<<st_bddb0b.pdata3;
            break;
        case 22:
            //设备内部温度
            InsideTemp = QString::number(qRound(200.0 / 32768.0 * st_bddb0b.pdata1 * 10)/10.0);
            //qDebug()<<"insidetemp:"<<msg.mid(48, 2).toHex(' ')<<InsideTemp<<qRound(200.0 / 32768.0 * st_bddb0b.pdata1 * 10);

            break;
        case 32:
            //GPS状态----输出ID
            GpsPosState = QString::number(st_bddb0b.pdata1);
            GpsSatelNum = QString::number(st_bddb0b.pdata2);
            GpsDirectionState = QString::number(st_bddb0b.pdata3);
            //qDebug()<<"GpsPosState:"<<GpsPosState<<GpsDirectionState;

            break;
        case 33:
            //轮速状态
            WheelType = QString::number(st_bddb0b.pdata2);

            break; ;
        case 34:
            //基本长度
            Baseline = QString::number(st_bddb0b.pdata1 / 1000.0);

            break;
        case 35:
            //定位状态
            rtkState = QString::number(st_bddb0b.pdata1);

            break;
        case 36:
            //跑车标定
            roadTestState = QString::number(st_bddb0b.pdata1);
            //qDebug()<<"roadTestState"<<roadTestState;
            break;
        case 37:
            //标定参数
            i64High = (int64_t)(st_bddb0b.pdata3) << 32;
            u32Mid = (uint32_t)st_bddb0b.pdata2 << 16;
            i64Val = i64High | u32Mid | st_bddb0b.pdata1;
            calibrateVal = QString("%1").arg(i64Val / 10000.0, 10, 'f', 6);
            //qDebug()<<"calibrateVal"<<msg.mid(48, 4).toHex(' ')<<i64Val<<calibrateVal;
            break;

        default:
            break;
    }

    //qDebug()<<"InsideTemp:"<<InsideTemp<<QThread::currentThreadId();

    dataValues.append(QString::number(froll));
    dataValues.append(QString::number(fpitch));
    dataValues.append(QString::number(fazimuth));
    dataValues.append(QString::number(fgyrox, 'f', 6));
    dataValues.append(QString::number(fgyroy, 'f', 6));
    dataValues.append(QString::number(fgyroz, 'f', 6));
    dataValues.append(QString::number(faccelx, 'f', 6));
    dataValues.append(QString::number(faccely, 'f', 6));
    dataValues.append(QString::number(faccelz, 'f', 6));
    dataValues.append(QString::number(flongitude, 'f', 8));
    dataValues.append(QString::number(flatitude, 'f', 8));
    dataValues.append(QString::number(faltitude, 'f', 4));
    dataValues.append(QString::number(fve));
    dataValues.append(QString::number(fvn));
    dataValues.append(QString::number(fvu));
    if(st_bddb0b.status & 0x01){
        dataValues.append(QString::fromLocal8Bit("1"));
    }else{
        dataValues.append(QString::fromLocal8Bit("0"));
    }

    if(st_bddb0b.status & 0x02){
        dataValues.append(QString::fromLocal8Bit("1"));
    }else{
        dataValues.append(QString::fromLocal8Bit("0"));
    }

    if(st_bddb0b.status & 0x04){
        dataValues.append(QString::fromLocal8Bit("1"));
    }else{
        dataValues.append(QString::fromLocal8Bit("0"));
    }


    if(st_bddb0b.status & 0x08){
        heading = QString::fromLocal8Bit("1");
    }else{
        heading = QString::fromLocal8Bit("0");
    }
    dataValues.append(heading);  //航向状态
    dataValues.append(QString::number(st_bddb0b.gps_time / 1000.0, 'f', 4));
    dataValues.append(QString::number(st_bddb0b.gps_week));
    dataValues.append(QString::number(st_bddb0b.type));
    dataValues.append(InsideTemp);
    dataValues.append(GpsSatelNum);
    dataValues.append(WheelType);
    dataValues.append(rtkState);
    dataValues.append(LatStd);
    dataValues.append(LonStd);
    dataValues.append(HStd);
    dataValues.append(Vn_std);
    dataValues.append(Ve_std);
    dataValues.append(Vd_std);
    dataValues.append(RollStd);
    dataValues.append(PitchStd);
    dataValues.append(YawStd);

    navValues.append(QString::number(froll));    //0
    navValues.append(QString::number(fpitch));   //1
    navValues.append(QString::number(fazimuth));      //2
    navValues.append(QString::number(flatitude, 'f', 8));  //3
    navValues.append(QString::number(flongitude, 'f', 8));  //4
    navValues.append(QString::number(faltitude, 'f', 4)); //5
    navValues.append(QString::number(fve)); //6
    navValues.append(QString::number(fvn)); //7
    navValues.append(QString::number(fvu)); //8
    navValues.append(QString::number(st_bddb0b.gps_time / 1000.0, 'f', 4)); //9
    navValues.append(QString::number(st_bddb0b.gps_week));  //10
    if(calibrateVal.isEmpty()){
        navValues.append("0.0");
    }else{
        navValues.append(calibrateVal);   //标定参数11
    }
    if(roadTestState.isEmpty()){
        navValues.append("0");  //跑车状态12
    }else{
        navValues.append(roadTestState);
    }
    if(rtkState.isEmpty()){
        navValues.append("0");
    }else{
        navValues.append(rtkState);       //rtk状态13
    }
    if(Baseline.isEmpty()){
        navValues.append("0");
    }else{
        navValues.append(Baseline);       //基线长14
    }
    navValues.append(GpsPosState);    //gps位置状态15
    if(WheelType.isEmpty()){
        navValues.append("0");
    }else{
        navValues.append(WheelType);         //轮速16
    }
    if(InsideTemp.isEmpty()){
        navValues.append("0.0");
    }else{
        navValues.append(InsideTemp);        //温度17
    }

    if(GpsSatelNum.isEmpty()){
        navValues.append("0");
    }else{
        navValues.append(GpsSatelNum);         //卫星数18
    }
    navValues.append(dataValues.at(15));  //位置19
    navValues.append(dataValues.at(16));  //速度20
    navValues.append(dataValues.at(17));  //姿态21
    navValues.append(heading);           //航向22

    if(!rtkState.isEmpty()){
        writeCvsFile("BDDB1B", m_slProtoKeys, dataValues);
    }

    if(m_bIsNeedShow ){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        emit sigNavUpdate(navValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }

    QVector<double> vplateValue;
    vplateValue.append(froll);
    vplateValue.append(fpitch);
    vplateValue.append(fazimuth);
    vplateValue.append(faltitude);
    vplateValue.append(fve);
    vplateValue.append(fvn);

    if(m_bshowview){
        emit sigAttituUpdate(vplateValue, m_sPortN);
        m_bshowview = false;
    }

    QStringList charKeys = m_slProtoKeys.mid(0, 15);
    charKeys.append(tr("StartNum"));
    charKeys.append(tr("Positionstatus"));
    charKeys.append(tr("Speedstatus"));
    charKeys.append(tr("Posturestatus"));
    charKeys.append(tr("Headingstatus"));
    charKeys.append(tr("Baselen"));
    QVector<double> charValuse;
    for(int i = 0; i < dataValues.size(); i++){
        if( i < 15){
            charValuse.append(dataValues.at(i).toDouble());
        }
    }
    charValuse.append(GpsSatelNum.toDouble());
    charValuse.append(dataValues.at(0).toDouble());
    charValuse.append(dataValues.at(1).toDouble());
    charValuse.append(dataValues.at(2).toDouble());
    charValuse.append(heading.toDouble());
    charValuse.append(Baseline.toDouble());
    //qDebug()<<charKeys<<charValuse;
    emit sigChartsUpdate(charKeys, charValuse, m_sPortN, m_iProtoIndex);
}


