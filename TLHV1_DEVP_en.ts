<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>C3B020BProtocol</name>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="16"/>
        <source>Time_stamp</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="17"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="18"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="19"/>
        <source>Cali_accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="20"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="21"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="22"/>
        <source>Angular_velocity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="23"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="24"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="25"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="26"/>
        <source>Quaternion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="39"/>
        <source>FLoss</source>
        <oldsource>Frame_Loss</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3b020bprotocol.cpp" line="40"/>
        <location filename="protocol/c3b020bprotocol.cpp" line="53"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>C3a0100Protocol</name>
    <message>
        <location filename="protocol/c3a0100protocol.cpp" line="68"/>
        <source>ChFail</source>
        <oldsource>Check fail</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/c3a0100protocol.cpp" line="75"/>
        <source>Except</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>C3dDisplayPage</name>
    <message>
        <location filename="service_ui/c3ddisplaypage.ui" line="16"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>C4A020A01Protocol</name>
    <message>
        <location filename="protocol/c4a020a01protocol.cpp" line="67"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>C4A020B01Protocol</name>
    <message>
        <location filename="protocol/c4a020b01protocol.cpp" line="69"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CA5A500Protocol</name>
    <message>
        <location filename="protocol/ca5a500protocol.cpp" line="100"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CA6A600Protocol</name>
    <message>
        <location filename="protocol/ca6a600protocol.cpp" line="122"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CAboutDlg</name>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="14"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="79"/>
        <source>About Us</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="92"/>
        <source>High-precision Inertial Navigation Satellite Integrated Navigation and Positioning System</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="125"/>
        <source>Email：<EMAIL></source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="135"/>
        <source>Website：www.i-nav.com.cn</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="145"/>
        <source>Phone：0755-23570274/18138811842</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="155"/>
        <source>3rd Floor, Building A2, Zhihui Advanced Manufacturing Industrial Park, Shajing Street, Bao&apos;an District, Shenzhen City, Guangdong Province</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="105"/>
        <source>V1.0.4</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="115"/>
        <source>Shenzhen I-Nav Technology Co., Ltd.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/caboutdlg.ui" line="168"/>
        <source>Copyright 2024-2035 The I-NAV Company Ltd. All rights reserved.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CAlgDebugPage</name>
    <message>
        <location filename="service_ui/calgdebugpage.ui" line="16"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CAttitudeWidget</name>
    <message>
        <location filename="common_ui/cattitudewidget.cpp" line="128"/>
        <source>Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cattitudewidget.cpp" line="140"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CBaseProtocol</name>
    <message>
        <location filename="protocol/cbaseprotocol.cpp" line="106"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbaseprotocol.cpp" line="107"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbaseprotocol.cpp" line="108"/>
        <source>Except</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CCDDC0BProtocol</name>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="17"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="18"/>
        <source>Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="19"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="20"/>
        <location filename="protocol/ccddc0bprotocol.cpp" line="21"/>
        <location filename="protocol/ccddc0bprotocol.cpp" line="22"/>
        <source>Gyro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="23"/>
        <location filename="protocol/ccddc0bprotocol.cpp" line="24"/>
        <location filename="protocol/ccddc0bprotocol.cpp" line="25"/>
        <source>Cali_accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="38"/>
        <source>FLoss</source>
        <oldsource>Frame_Loss</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/ccddc0bprotocol.cpp" line="39"/>
        <location filename="protocol/ccddc0bprotocol.cpp" line="52"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CCarRotWidget</name>
    <message>
        <location filename="controls/ccarrotwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/ccarrotwidget.ui" line="62"/>
        <source>Pitch:0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/ccarrotwidget.ui" line="113"/>
        <source>Yaw:0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/ccarrotwidget.ui" line="164"/>
        <source>Roll:0.00</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CCarplateWidget</name>
    <message>
        <location filename="controls/ccarplatewidget.cpp" line="119"/>
        <source>Altimeter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/ccarplatewidget.cpp" line="190"/>
        <source>Velometer</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CCollectRecordDialog</name>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="14"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="21"/>
        <source>No.</source>
        <oldsource>序号</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="37"/>
        <source>FileName</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="50"/>
        <source>SampTime</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="63"/>
        <source>FileSize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="81"/>
        <source>Reload</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="95"/>
        <source>All Files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.ui" line="100"/>
        <source>Today Files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="60"/>
        <source>View driving trajectory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="61"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="62"/>
        <source>Open the file location</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="63"/>
        <source>Copy the file pathname</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="64"/>
        <source>Open the file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="80"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="80"/>
        <source>Non sports car trajectory file, unable to replay trajectory!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="111"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ccollectrecorddialog.cpp" line="111"/>
        <source>Fail to open file</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CConfigManager</name>
    <message>
        <location filename="common/cconfigmanager.cpp" line="150"/>
        <source>Data file save dir is occupied,please change app dir!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cconfigmanager.cpp" line="157"/>
        <source>Mapcache dir is occupied,please change app dir!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CCustomerViewPage</name>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="65"/>
        <source>Date Time:</source>
        <oldsource>Date Time</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="87"/>
        <source>---</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="116"/>
        <source>Gnssweek:</source>
        <oldsource>Gnssweek</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="138"/>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="167"/>
        <source>Gnsssec:</source>
        <oldsource>Gnsssec</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="189"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="970"/>
        <source>0.0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="218"/>
        <source>Pitch:</source>
        <oldsource>Pitch</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="240"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="313"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="386"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="459"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="532"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="605"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="678"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="751"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="824"/>
        <source>0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="262"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="335"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="408"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="481"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="554"/>
        <source>°</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="291"/>
        <source>Roll:</source>
        <oldsource>Roll</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="364"/>
        <source>Yaw:</source>
        <oldsource>Yaw</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="437"/>
        <source>Lngitude:</source>
        <oldsource>Lngitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="510"/>
        <source>Latitude:</source>
        <oldsource>Latitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="583"/>
        <source>Altitude:</source>
        <oldsource>Altitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="627"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1065"/>
        <source>m</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="656"/>
        <source>Evelocity:</source>
        <oldsource>Evelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="700"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="773"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="846"/>
        <source>m/s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="729"/>
        <source>Nvelocity:</source>
        <oldsource>Nvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="802"/>
        <source>Uvelocity:</source>
        <oldsource>Uvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="875"/>
        <source>StartNum:</source>
        <oldsource>StartNum</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="916"/>
        <source>Pie</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="945"/>
        <source>Temp:</source>
        <oldsource>Temp</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="992"/>
        <source>℃</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1021"/>
        <source>Baselen:</source>
        <oldsource>Baselen</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1094"/>
        <source>Alignstate:</source>
        <oldsource>Alignstate</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1116"/>
        <source>0,0,0,0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1145"/>
        <source>Rtkstatus:</source>
        <oldsource>Rtkstatus</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1193"/>
        <source>Wspstatus:</source>
        <oldsource>Wspstatus</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1241"/>
        <source>Carcali:</source>
        <oldsource>Carcali</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1289"/>
        <source>Posprec:</source>
        <oldsource>Posprec</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1370"/>
        <source>InstView</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1389"/>
        <source>DrvTraj</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.ui" line="1480"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1487"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1494"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1501"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1508"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1515"/>
        <location filename="service_ui/ccustomerviewpage.ui" line="1522"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="22"/>
        <source>WSS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="23"/>
        <source>POS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="24"/>
        <source>SPD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="25"/>
        <source>ATT</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="26"/>
        <source>CAL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="27"/>
        <source>HDG</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="234"/>
        <location filename="service_ui/ccustomerviewpage.cpp" line="302"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="234"/>
        <source>The file is being replayed, please do not repeat the operation!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="240"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="240"/>
        <source>Fail to open file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="302"/>
        <source>Track Record</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccustomerviewpage.cpp" line="302"/>
        <source>Replay completed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CDataAnaPage</name>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="70"/>
        <source>Win Num:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="78"/>
        <source>Single Win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="83"/>
        <source>Double Win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="88"/>
        <source>Three win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="108"/>
        <source>Win No:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="116"/>
        <source>First Win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="121"/>
        <source>Second Win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="126"/>
        <source>Third Win</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="146"/>
        <source>Disp Params:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="160"/>
        <source>Overlay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="167"/>
        <source>Real-time data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="184"/>
        <source>Explicit Cursor</source>
        <oldsource>Explicit Cursor </oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="191"/>
        <source>Cancel cursor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="213"/>
        <source>Real-Time Analysis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="234"/>
        <source>Pause refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="241"/>
        <source>Port:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="249"/>
        <source>1000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="254"/>
        <source>500</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="259"/>
        <source>2000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="270"/>
        <source>Start refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="277"/>
        <source>Win range:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="287"/>
        <source>Offline Analysis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="307"/>
        <source>Import File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="332"/>
        <source>Load File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="341"/>
        <source>Display range</source>
        <oldsource>Display range:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.ui" line="348"/>
        <source>Gen Img</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="256"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="256"/>
        <source>Maximum support</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="256"/>
        <source>Waveform overlay display</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="301"/>
        <source>Open File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="301"/>
        <source>Csv files (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="312"/>
        <location filename="service_ui/cdataanapage.cpp" line="319"/>
        <location filename="service_ui/cdataanapage.cpp" line="562"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="312"/>
        <source>Fail to open file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="319"/>
        <source>File read failure</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="351"/>
        <location filename="service_ui/cdataanapage.cpp" line="531"/>
        <source>Sure</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="500"/>
        <source>Horizontal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="500"/>
        <source>Vertical</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="508"/>
        <source>Minimum</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="509"/>
        <source>Maximum</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="510"/>
        <source>Margin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="532"/>
        <source>Reset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="533"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdataanapage.cpp" line="562"/>
        <source>Invalid input range!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CDeviceConfigWidget</name>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="43"/>
        <source>Protocol type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="57"/>
        <source>3A010000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="62"/>
        <source>BB00DBBD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="67"/>
        <source>3A020000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="72"/>
        <source>4A010000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="77"/>
        <source>AA660000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="82"/>
        <source>A5A50000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="87"/>
        <source>A6A60000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="105"/>
        <source>Additional type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="154"/>
        <source>Gyro type:</source>
        <oldsource>Gyrostype:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="188"/>
        <source>Gyro freq:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="250"/>
        <source>Sampling freq:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="317"/>
        <source>FPGA frame count:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="116"/>
        <source>MEMS-SCH630</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="121"/>
        <source>MEMS-ADI330</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="126"/>
        <source>MEMS-HD6089</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="131"/>
        <source>DACC-XIAN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="136"/>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="170"/>
        <source>ORIDATA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="165"/>
        <source>MEM</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="202"/>
        <source>50HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="207"/>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="294"/>
        <source>100HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="212"/>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="299"/>
        <source>200HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="217"/>
        <source>300HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="222"/>
        <source>400HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="227"/>
        <source>500HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="232"/>
        <source>1000HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="264"/>
        <source>0HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="269"/>
        <source>0.1HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="274"/>
        <source>0.2HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="279"/>
        <source>0.5HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="284"/>
        <source>1HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="289"/>
        <source>10HZ</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="331"/>
        <source>500</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="336"/>
        <source>200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceconfigwidget.ui" line="341"/>
        <source>20</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CDeviceParmWidget</name>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="46"/>
        <source>Debug mode:</source>
        <oldsource>Debug模式：</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="123"/>
        <source>FOG</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="128"/>
        <source>MEMS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="189"/>
        <source>1、GPS Fusion</source>
        <oldsource>1、GPS融合</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="57"/>
        <source>Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="20"/>
        <source>Debug options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="62"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="112"/>
        <source>Gyro type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="178"/>
        <source>GPS type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="194"/>
        <source>2. Kinematic model</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="199"/>
        <source>3. Kinematic model+wheel speed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="204"/>
        <source>4. Disconnect GPS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="254"/>
        <source>Data output:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="265"/>
        <source>0. Combined navigation data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="270"/>
        <source>1. Integrated navigation data+IMU+GPS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="275"/>
        <source>FF、 Pause output</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="320"/>
        <source>Data output(HZ)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="366"/>
        <source>Baud rate:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="402"/>
        <source>200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="407"/>
        <source>500</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="412"/>
        <source>1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="417"/>
        <source>5</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="422"/>
        <source>10</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="427"/>
        <source>25</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="432"/>
        <source>50</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="437"/>
        <source>100</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="442"/>
        <source>1000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="501"/>
        <source>38400</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="506"/>
        <source>57600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="511"/>
        <source>96000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="516"/>
        <source>115200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="521"/>
        <source>460800</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="526"/>
        <source>921600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="531"/>
        <source>2000000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="554"/>
        <source>Output freq:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="567"/>
        <source>User coordinate axis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="591"/>
        <source>Coordinate:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="647"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1421"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1690"/>
        <source>Axial:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="748"/>
        <source>GNSS arm parameters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="775"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="894"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1286"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1440"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1602"/>
        <source>X-axis:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="797"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="916"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1305"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1383"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1580"/>
        <source>Y-axis:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="819"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="938"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1324"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1405"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1674"/>
        <source>Z-axis:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="867"/>
        <source>Antenna installation angle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="986"/>
        <source>GNSS initial value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1016"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1189"/>
        <source>Pitch:</source>
        <oldsource>Pitch</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1032"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1227"/>
        <source>Yaw:</source>
        <oldsource>Yaw</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1051"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1208"/>
        <source>Roll:</source>
        <oldsource>Roll</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1092"/>
        <source>Heading:</source>
        <oldsource>Heading</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1114"/>
        <source>Altitude:</source>
        <oldsource>Altitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1133"/>
        <source>Latitude:</source>
        <oldsource>Latitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1152"/>
        <source>Longitude:</source>
        <oldsource>Longitude</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1162"/>
        <source>Misalignment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1259"/>
        <source>Position vector</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1356"/>
        <source>Gyro calibration factor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1528"/>
        <source>Add calibration factor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1700"/>
        <source>SD Card Operation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1724"/>
        <source>File Type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1741"/>
        <source>GPS File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1746"/>
        <source>Oridata File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1751"/>
        <source>All File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1765"/>
        <source>Oprate Type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1788"/>
        <source>Begin Store</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1793"/>
        <source>Stop Store</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1798"/>
        <source>Read Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1803"/>
        <source>Stop Read</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1808"/>
        <source>Clear Files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1851"/>
        <source>Static of zero bias time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1875"/>
        <source>Time:</source>
        <oldsource>Time</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="608"/>
        <source>X,Y,Z</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="613"/>
        <source>X,Z,Y</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="618"/>
        <source>Y,X,Z</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="623"/>
        <source>Y,Z,X</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="628"/>
        <source>Z,X,Y</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="633"/>
        <source>Z,Y,X</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="670"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1482"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1622"/>
        <source>1,1,1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="675"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1502"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1642"/>
        <source>-1,1,1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="680"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1492"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1632"/>
        <source>1,-1,1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="685"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1487"/>
        <source>1,1,-1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="690"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1512"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1652"/>
        <source>-1,-1,1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="695"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1497"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1637"/>
        <source>1,-1,-1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="700"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1507"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1647"/>
        <source>-1,1,-1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="705"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1517"/>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1657"/>
        <source>-1,-1,-1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1627"/>
        <source>1, 1, -1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.ui" line="1885"/>
        <source>0-600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.cpp" line="36"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cdeviceparmwidget.cpp" line="36"/>
        <source>Before modifying the output frequency, please confirm whether the baud rate meets the output bandwidth requirements!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CDriTestPage</name>
    <message>
        <location filename="service_ui/cdritestpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdritestpage.cpp" line="16"/>
        <location filename="service_ui/cdritestpage.cpp" line="62"/>
        <source>FLoss</source>
        <oldsource>Frame_Loss</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdritestpage.cpp" line="21"/>
        <location filename="service_ui/cdritestpage.cpp" line="63"/>
        <source>ChFail</source>
        <oldsource>Check Fail</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdritestpage.cpp" line="26"/>
        <location filename="service_ui/cdritestpage.cpp" line="64"/>
        <source>Except</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cdritestpage.cpp" line="31"/>
        <location filename="service_ui/cdritestpage.cpp" line="156"/>
        <source>Freq</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CDriveTestPage</name>
    <message>
        <location filename="service_ui/cdrivetestpage.ui" line="14"/>
        <source>MainWindow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CEquipmentPage</name>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="24"/>
        <source>参数</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="48"/>
        <source>杆臂X:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="73"/>
        <source>杆臂Y:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="92"/>
        <source>杆臂Z:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="111"/>
        <source>偏航角:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="130"/>
        <source>滚转角：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="149"/>
        <source>俯仰角：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="168"/>
        <source>东向速度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="187"/>
        <source>北向速度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="206"/>
        <source>天向速度</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="225"/>
        <source>经度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="244"/>
        <source>纬度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="269"/>
        <source>高度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="294"/>
        <source>主惯导更新周期：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="316"/>
        <source>主惯导更新延迟：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="326"/>
        <source>主惯导更新周期</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="333"/>
        <source>主惯导更新延迟</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="345"/>
        <source>查询/操作</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="360"/>
        <source>命令字：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="371"/>
        <source>自检查询</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="376"/>
        <source>软件版本查询</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="381"/>
        <source>炮口查询</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="386"/>
        <source>射检</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="391"/>
        <source>自标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="396"/>
        <source>恢复出厂设置</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="410"/>
        <source>操作模式：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="421"/>
        <source>无操作</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="426"/>
        <source>装定对准</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="431"/>
        <source>自对准</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="436"/>
        <source>传递对准</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="441"/>
        <source>转导航</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="455"/>
        <source>惯性导航高度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="466"/>
        <source>椭球高度</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="471"/>
        <source>海拔高度</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="482"/>
        <source>状态</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="497"/>
        <source>操作状态：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="520"/>
        <source>导航状态：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="550"/>
        <source>I M U状态：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="580"/>
        <source>导航串口：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cequipmentpage.ui" line="590"/>
        <source>执行</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CFileFieldConfigWidget</name>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="34"/>
        <source>Protocol type:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="42"/>
        <source>BB00DBBD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="47"/>
        <source>3A0100</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="52"/>
        <source>BDDB0B</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="57"/>
        <source>A5A5</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="62"/>
        <source>B55B</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="67"/>
        <source>A6A6</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="75"/>
        <source>File Path:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="89"/>
        <source>Binary files</source>
        <oldsource>Generate binary data files</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="97"/>
        <source>All data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="102"/>
        <source>Calibrated IMU data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="123"/>
        <source>SelAll</source>
        <oldsource>Select All</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="130"/>
        <source>InvSel</source>
        <oldsource>Select Invert</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="161"/>
        <source>Divisor：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="188"/>
        <source>Filed:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="198"/>
        <source>Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.ui" line="171"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="177"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="177"/>
        <source>The input factor format is incorrect. The two factors should be separated by a space. Example</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="196"/>
        <source>Filed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="196"/>
        <source>not belong current protocol，please confirm data or change protocol!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="207"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="207"/>
        <source>records were successfully applied.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CFirmUpgPage</name>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="279"/>
        <source>...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="45"/>
        <source>Output parameters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="46"/>
        <source>Baud rate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="47"/>
        <source>Output frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="48"/>
        <source>System working mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="49"/>
        <source>GNSS arm parameters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="50"/>
        <source>Antenna installation angle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="51"/>
        <source>GNSS initial value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="52"/>
        <source>Position vector</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="53"/>
        <source>Misalignment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="54"/>
        <source>User coordinate axis</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="55"/>
        <source>Static of zero bias time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="56"/>
        <source>Cure Param</source>
        <oldsource>Cure Parameter</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="360"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="59"/>
        <source>Read Back</source>
        <oldsource>Parameter readback</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="60"/>
        <source>GPS interruption type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="61"/>
        <source>Data output type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="62"/>
        <source>Debug mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="63"/>
        <source>Gyro type</source>
        <oldsource>Gyroscope type</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="32"/>
        <source>Reminder information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="62"/>
        <source>Clear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="77"/>
        <source>FileName</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="85"/>
        <source>Upgrade files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="90"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="64"/>
        <source>Calibration parameters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="95"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="65"/>
        <source>temperature compensation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="100"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="66"/>
        <source>Kalman filter Q matrix</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="105"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="67"/>
        <source>Kalman filter R matrix</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="110"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="68"/>
        <source>Indirect filtering coefficient</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="136"/>
        <source>Import File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="161"/>
        <source>Versions Query</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="209"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1013"/>
        <source>Temp Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="202"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="181"/>
        <source>Cali Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="195"/>
        <source>Parms Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="168"/>
        <source>Start Upg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="175"/>
        <source>Exit Upg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="319"/>
        <source>Old Dev</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="339"/>
        <source>Back View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="346"/>
        <source>Cure Parm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="353"/>
        <source>Def Parm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.ui" line="367"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="58"/>
        <source>All Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="57"/>
        <source>Def Param</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="69"/>
        <source>Software upgrade begins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="70"/>
        <source>Send upgrade package</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="71"/>
        <source>Upgrade package completed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="72"/>
        <source>Upgrade termination</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="73"/>
        <source>Gyro scale factor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="74"/>
        <source>Add scale factor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="75"/>
        <source>SD Card Operation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="99"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="102"/>
        <source>Open File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="99"/>
        <source>xlsx files(*.xlsx)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="102"/>
        <source>bin files(*.bin)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="143"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="982"/>
        <source>Please verify the parameter file data!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="143"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="181"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="982"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1013"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1460"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1464"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1467"/>
        <source>##</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="143"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="982"/>
        <source>The parameter file has been imported</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="181"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1013"/>
        <source>Button for calibration parameter issuance!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="181"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1013"/>
        <source>After verification, please click</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1028"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1032"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1045"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1051"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1085"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1121"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1186"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1455"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1465"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1468"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1984"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1990"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1028"/>
        <source>Upgrade file format error, please select bin file!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1032"/>
        <source>Already in the process of upgrading, please do not repeat the operation!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1045"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1108"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1135"/>
        <source>Please open the upgrade serial port first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1051"/>
        <source>Please select the upgrade package file first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1081"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1099"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1108"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1135"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1223"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1419"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1458"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1838"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1099"/>
        <source>Not in the process of upgrading!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1121"/>
        <source>Already in the process of querying/upgrading, please do not repeat the operation!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1186"/>
        <source>Please check the parameter options!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1223"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1838"/>
        <source>The coordinate combination is invalid, please reset the combination!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1243"/>
        <source>GNSS lever arm parameters are invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1257"/>
        <source>The antenna installation angle parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1272"/>
        <source>The attitude angle parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1276"/>
        <source>The longitude parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1280"/>
        <source>The latitude parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1299"/>
        <source>Inertial navigation installation deviation parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1314"/>
        <source>Inertial navigation rear wheel vector parameters are invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1329"/>
        <source>The static zero bias time parameter is invalid!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1435"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1883"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1935"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1966"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="2080"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="2128"/>
        <source>Configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1435"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1883"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1935"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1966"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="2080"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="2128"/>
        <source>Message information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1455"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1458"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1465"/>
        <source>Parameter command operation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1455"/>
        <source>SD Data Read successful!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1458"/>
        <source>operation failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1460"/>
        <source>Parameter issuance failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1464"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1465"/>
        <source>Parameter solidification successful!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1467"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1468"/>
        <source>The parameters have been successfully issued. If you need to solidify them, please click the &quot;Solidify Parameters&quot; button!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cfirmupgpage.cpp" line="1984"/>
        <location filename="service_ui/cfirmupgpage.cpp" line="1990"/>
        <source>Please open the serial port first!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CFlyViewPage</name>
    <message>
        <location filename="common_ui/cflyviewpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cflyviewpage.ui" line="51"/>
        <source>Altitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cflyviewpage.ui" line="135"/>
        <source>Speed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CGyroCalibPage</name>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="29"/>
        <source>重复性因数：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="48"/>
        <source>零偏：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="59"/>
        <source>°/h</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="64"/>
        <source>°/s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="81"/>
        <source>零偏稳定性：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="107"/>
        <source>信息</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="133"/>
        <location filename="service_ui/cgyrocalibpage.ui" line="400"/>
        <source>标度因数：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="163"/>
        <source>标度因数非线性度：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="193"/>
        <source>标度因数不对称性：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="223"/>
        <source>标度因数重复性：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="249"/>
        <source>阀值：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="272"/>
        <source>分辨率：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="295"/>
        <source>零偏重复性：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="318"/>
        <source>陀螺仪标定：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="328"/>
        <location filename="service_ui/cgyrocalibpage.ui" line="355"/>
        <source>查看结果</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="345"/>
        <source>加速度计标定：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="380"/>
        <source>标定参数</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="410"/>
        <location filename="service_ui/cgyrocalibpage.ui" line="453"/>
        <location filename="service_ui/cgyrocalibpage.ui" line="496"/>
        <source>1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="421"/>
        <source>采集数据量：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="431"/>
        <source>3600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="442"/>
        <source>采样频率：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="458"/>
        <location filename="service_ui/cgyrocalibpage.ui" line="506"/>
        <source>100</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="463"/>
        <source>200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="468"/>
        <source>300</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="473"/>
        <source>500</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="485"/>
        <source>平滑时间：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="501"/>
        <source>10</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="538"/>
        <source>坐标轴：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="546"/>
        <source>x轴</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="551"/>
        <source>y轴</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="556"/>
        <source>z轴</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="564"/>
        <source>标定目标：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="572"/>
        <source>标度因数</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="577"/>
        <source>阀值</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="582"/>
        <source>分辨率</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="587"/>
        <source>零偏稳定性</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="592"/>
        <source>陀螺仪标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="597"/>
        <source>加速度计标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="602"/>
        <source>标度因数重复性</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="607"/>
        <source>零偏重复性</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="612"/>
        <source>零偏</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="633"/>
        <source>导入文件</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="653"/>
        <source>开始标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.ui" line="660"/>
        <source>撤销</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/cgyrocalibpage.cpp" line="196"/>
        <source>Csv files (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CInputDialog</name>
    <message>
        <location filename="controls/cinputdialog.cpp" line="7"/>
        <source>PwdInput</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cinputdialog.cpp" line="9"/>
        <source>Please input a password:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CLoadFileTaskDlg</name>
    <message>
        <location filename="common/cloadfiletaskdlg.cpp" line="30"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CMagnetPage</name>
    <message>
        <location filename="service_ui/cmagnetpage.ui" line="16"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CMapConfigWidget</name>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="49"/>
        <source>Driving View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="54"/>
        <source>Flight View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="78"/>
        <source>Refresh frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="108"/>
        <source>Playback mode:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="122"/>
        <source>Inter time:</source>
        <oldsource>Interval time:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="130"/>
        <source>Auto play</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="135"/>
        <source>Sync play</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="140"/>
        <source>Fast play</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="145"/>
        <source>Slow Play</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="153"/>
        <source>Refresh time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="160"/>
        <source>Inter points:</source>
        <oldsource>Interval points:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="190"/>
        <source>Map trajectory style</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="199"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="617"/>
        <source>Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="232"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="349"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="466"/>
        <source>Dot color:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="252"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="369"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="486"/>
        <source>Dot Thk:</source>
        <oldsource>Dot thickness:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="268"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="385"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="502"/>
        <source>Line color:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="288"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="405"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="522"/>
        <source>Line Thk:</source>
        <oldsource>Line thickness:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="304"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="421"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="538"/>
        <source>Line Style:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="319"/>
        <source>The second trajectory style</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="436"/>
        <source>The third trajectory style</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="553"/>
        <source>Map center point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="570"/>
        <source>City:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="583"/>
        <source>MapHie:</source>
        <oldsource>Map hierarchy:</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="596"/>
        <source>Latitude:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="609"/>
        <source>Longitude:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="622"/>
        <source>Shenzhen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="627"/>
        <source>Guangzhou</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="632"/>
        <source>Beijing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="637"/>
        <source>Shanghai</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="642"/>
        <source>Wuhan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="647"/>
        <source>Foshan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="652"/>
        <source>Dongguan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="657"/>
        <source>Suzhou</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="662"/>
        <source>Hangzhou</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="667"/>
        <source>Chengdu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="672"/>
        <source>Chongqing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="87"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="94"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="101"/>
        <source>200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="26"/>
        <source>Dashboard Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="41"/>
        <source>Dashboard Mode:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="115"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="167"/>
        <source>ms</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="217"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="334"/>
        <location filename="common_ui/cmapconfigwidget.ui" line="451"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="681"/>
        <source>14</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="686"/>
        <source>13</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="691"/>
        <source>15</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.ui" line="696"/>
        <source>16</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.cpp" line="32"/>
        <source>Solid line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.cpp" line="33"/>
        <source>Dashed line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.cpp" line="34"/>
        <source>Dotted line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cmapconfigwidget.cpp" line="35"/>
        <source>Center line</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CMapViewPage</name>
    <message>
        <location filename="common_ui/cmapviewpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>COutLineParService</name>
    <message>
        <location filename="service_imp/coutlineparservice.cpp" line="34"/>
        <source>File read failure:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/coutlineparservice.cpp" line="49"/>
        <source>File read terminated</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/coutlineparservice.cpp" line="56"/>
        <source>File read completed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CPlateViewPage</name>
    <message>
        <location filename="common_ui/cplateviewpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.ui" line="92"/>
        <source>Pitch：0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.ui" line="152"/>
        <source>Yaw：0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.ui" line="212"/>
        <source>Roll：0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.ui" line="242"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.cpp" line="103"/>
        <source>Pich</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.cpp" line="104"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplateviewpage.cpp" line="105"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CPlaybackWidget</name>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="107"/>
        <source>...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="117"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="143"/>
        <source>864</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="166"/>
        <source>0.00</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/cplaybackwidget.ui" line="203"/>
        <source>Follow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CProtoParamDialog</name>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="17"/>
        <source>Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="25"/>
        <source>DevCfg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="32"/>
        <source>TimCfg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="39"/>
        <source>FileCfg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="46"/>
        <source>MapCfg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="105"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotoparamdialog.ui" line="128"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CProtocolFactory</name>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="230"/>
        <source>Frame_num</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="231"/>
        <source>FPGA_version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="232"/>
        <source>WATCH_version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="233"/>
        <location filename="common/cprotocolfactory.cpp" line="274"/>
        <source>Gears</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="234"/>
        <location filename="common/cprotocolfactory.cpp" line="276"/>
        <source>Flwheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="235"/>
        <location filename="common/cprotocolfactory.cpp" line="277"/>
        <source>Frwheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="236"/>
        <location filename="common/cprotocolfactory.cpp" line="278"/>
        <source>Blwheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="237"/>
        <location filename="common/cprotocolfactory.cpp" line="279"/>
        <source>Brwheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="238"/>
        <location filename="common/cprotocolfactory.cpp" line="275"/>
        <source>Caninfocounter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="239"/>
        <location filename="common/cprotocolfactory.cpp" line="240"/>
        <location filename="common/cprotocolfactory.cpp" line="241"/>
        <location filename="common/cprotocolfactory.cpp" line="242"/>
        <location filename="common/cprotocolfactory.cpp" line="243"/>
        <location filename="common/cprotocolfactory.cpp" line="244"/>
        <source>Gyro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="242"/>
        <location filename="common/cprotocolfactory.cpp" line="243"/>
        <location filename="common/cprotocolfactory.cpp" line="244"/>
        <location filename="common/cprotocolfactory.cpp" line="248"/>
        <source>Temp</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="245"/>
        <location filename="common/cprotocolfactory.cpp" line="246"/>
        <location filename="common/cprotocolfactory.cpp" line="247"/>
        <location filename="common/cprotocolfactory.cpp" line="248"/>
        <source>Accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="249"/>
        <location filename="common/cprotocolfactory.cpp" line="250"/>
        <location filename="common/cprotocolfactory.cpp" line="251"/>
        <source>Reserve</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="252"/>
        <source>Gnssweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="253"/>
        <source>Millisecondofweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="255"/>
        <source>Ppsdelay10ns</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="256"/>
        <source>StartNum</source>
        <oldsource>Gpsstarnumber</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="257"/>
        <source>Rtkstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="258"/>
        <source>Speedstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="259"/>
        <source>Truenorthtrack</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="260"/>
        <location filename="common/cprotocolfactory.cpp" line="299"/>
        <source>Nvelocity</source>
        <oldsource>Northvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="261"/>
        <location filename="common/cprotocolfactory.cpp" line="298"/>
        <source>Evelocity</source>
        <oldsource>Eastvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="254"/>
        <source>Gnsssec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="262"/>
        <location filename="common/cprotocolfactory.cpp" line="300"/>
        <source>Uvelocity</source>
        <oldsource>Upvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="263"/>
        <source>positionstatus</source>
        <oldsource>Positionstatus</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="264"/>
        <source>directionoflat</source>
        <oldsource>Directionoflat</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="265"/>
        <location filename="common/cprotocolfactory.cpp" line="296"/>
        <source>Latitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="266"/>
        <source>directionoflon</source>
        <oldsource>Directionoflon</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="267"/>
        <location filename="common/cprotocolfactory.cpp" line="295"/>
        <source>Longitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="268"/>
        <location filename="common/cprotocolfactory.cpp" line="297"/>
        <source>Altitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="269"/>
        <source>Headingstate</source>
        <oldsource>Headingstatus</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="270"/>
        <source>Baselen</source>
        <oldsource>Baselength</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="271"/>
        <location filename="common/cprotocolfactory.cpp" line="302"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="272"/>
        <location filename="common/cprotocolfactory.cpp" line="301"/>
        <source>Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="273"/>
        <location filename="common/cprotocolfactory.cpp" line="303"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="280"/>
        <source>Timeprec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="281"/>
        <source>Verticalprec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="282"/>
        <source>Horizontalprec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="283"/>
        <source>Northprec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="284"/>
        <source>Eastprec</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="285"/>
        <source>Endheightangle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="286"/>
        <source>StanDeviat_Lat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="287"/>
        <source>StanDeviat_Lon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="288"/>
        <source>StanDeviat_Alt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="289"/>
        <source>StanDeviat_Heading</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="290"/>
        <source>StanDeviat_Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="291"/>
        <source>Sol_Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="292"/>
        <source>Pos_Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="294"/>
        <source>Frame_count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="304"/>
        <location filename="common/cprotocolfactory.cpp" line="305"/>
        <location filename="common/cprotocolfactory.cpp" line="306"/>
        <source>Cali_gyro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="307"/>
        <location filename="common/cprotocolfactory.cpp" line="308"/>
        <location filename="common/cprotocolfactory.cpp" line="309"/>
        <source>Cali_accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="310"/>
        <source>FpgA_Internum</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="311"/>
        <source>Pack_count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/cprotocolfactory.cpp" line="312"/>
        <source>Sytem_status</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CRevoCtrPage</name>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="37"/>
        <source>外轴</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="77"/>
        <location filename="service_ui/crevoctrpage.ui" line="534"/>
        <source>使能</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="90"/>
        <location filename="service_ui/crevoctrpage.ui" line="547"/>
        <source>当前位置（°）</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="106"/>
        <location filename="service_ui/crevoctrpage.ui" line="141"/>
        <location filename="service_ui/crevoctrpage.ui" line="325"/>
        <location filename="service_ui/crevoctrpage.ui" line="563"/>
        <location filename="service_ui/crevoctrpage.ui" line="598"/>
        <location filename="service_ui/crevoctrpage.ui" line="756"/>
        <source>0.0000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="125"/>
        <location filename="service_ui/crevoctrpage.ui" line="582"/>
        <source>当前速率（°/ s）</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="188"/>
        <location filename="service_ui/crevoctrpage.ui" line="645"/>
        <source>TextLabel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="198"/>
        <source>外轴控制</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="232"/>
        <location filename="service_ui/crevoctrpage.ui" line="689"/>
        <source>运行方式</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="246"/>
        <location filename="service_ui/crevoctrpage.ui" line="703"/>
        <source>位置</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="251"/>
        <location filename="service_ui/crevoctrpage.ui" line="708"/>
        <source>速率</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="256"/>
        <location filename="service_ui/crevoctrpage.ui" line="713"/>
        <source>摇摆</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="261"/>
        <location filename="service_ui/crevoctrpage.ui" line="718"/>
        <source>找零</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="299"/>
        <location filename="service_ui/crevoctrpage.ui" line="851"/>
        <source>目标位置</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="332"/>
        <location filename="service_ui/crevoctrpage.ui" line="824"/>
        <source>目标加速度</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="339"/>
        <location filename="service_ui/crevoctrpage.ui" line="346"/>
        <location filename="service_ui/crevoctrpage.ui" line="770"/>
        <location filename="service_ui/crevoctrpage.ui" line="777"/>
        <source>10.0000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="379"/>
        <location filename="service_ui/crevoctrpage.ui" line="784"/>
        <source>°</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="386"/>
        <location filename="service_ui/crevoctrpage.ui" line="763"/>
        <source>目标速率</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="406"/>
        <location filename="service_ui/crevoctrpage.ui" line="791"/>
        <source>°/s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="413"/>
        <location filename="service_ui/crevoctrpage.ui" line="831"/>
        <source>°/ss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="456"/>
        <location filename="service_ui/crevoctrpage.ui" line="907"/>
        <source>执行</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="469"/>
        <location filename="service_ui/crevoctrpage.ui" line="920"/>
        <source>停车</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="494"/>
        <source>内轴</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="655"/>
        <source>内轴控制</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="953"/>
        <source>自动测试</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="982"/>
        <source>文件名：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1003"/>
        <source>导入文件</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1023"/>
        <source>编辑脚本</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1046"/>
        <source>脚本名:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1060"/>
        <source>参数标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1065"/>
        <source>陀螺阈值&amp;分辨率</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1077"/>
        <source>开始采集</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.ui" line="1097"/>
        <source>停止采集</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/crevoctrpage.cpp" line="686"/>
        <source>Text or Xlsx files(*.txt *.xlsx)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CScrollLineChart</name>
    <message>
        <location filename="controls/cscrolllinechart.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CSingleStatusLable</name>
    <message>
        <location filename="controls/csinglestatuslable.cpp" line="9"/>
        <source>Wheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CSseriService</name>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="20"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="72"/>
        <source>Text</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="111"/>
        <source>Event</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="113"/>
        <source>Odd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="245"/>
        <source>Start querying device version information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="245"/>
        <source>***!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="295"/>
        <source>Failed to receive version query response, failed to query version information!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="361"/>
        <source>Received version query response, current version number is</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="439"/>
        <location filename="service_imp/csseriservice.cpp" line="447"/>
        <source>Failed to read upgrade package, please check upgrade package, upgrade process failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="454"/>
        <source>Notify the device to start upgrading</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="473"/>
        <source>Start pushing upgrade packages!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="497"/>
        <source>Data packet retransmission failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="568"/>
        <location filename="service_imp/csseriservice.cpp" line="640"/>
        <source>Receiving device upgrade package response fail!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="570"/>
        <location filename="service_imp/csseriservice.cpp" line="642"/>
        <source>Receiving device upgrade package response error!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="581"/>
        <source>Push upgrade installation package</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="588"/>
        <source>Upgrade package push completed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="649"/>
        <source>Upgrade process executed successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_imp/csseriservice.cpp" line="651"/>
        <source>Upgrade process execution failed!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CTimerConfigWidget</name>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="31"/>
        <source>Time：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="64"/>
        <source>Number of cycles:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="97"/>
        <source>Interval time/min:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="134"/>
        <source>Timed collection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common_ui/ctimerconfigwidget.ui" line="47"/>
        <source>hh:mm:ss</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CTurntableJEditDialog</name>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="14"/>
        <source>编辑脚本</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="34"/>
        <source>脚本信息：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="63"/>
        <source>(文件前缀w或g开头，第二个字符表示坐标轴，可为x或y或z)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="104"/>
        <source>保存</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="127"/>
        <source>重置</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="controls/cturntablejeditdialog.ui" line="150"/>
        <source>取消</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CYawIndWidge</name>
    <message>
        <location filename="common_ui/cyawindwidge.cpp" line="112"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Caa5500Protocol</name>
    <message>
        <location filename="protocol/caa5500protocol.cpp" line="135"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/caa5500protocol.cpp" line="145"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Caa6600Protocol</name>
    <message>
        <location filename="protocol/caa6600protocol.cpp" line="95"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/caa6600protocol.cpp" line="105"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Cb55b00Protocol</name>
    <message>
        <location filename="protocol/cb55b00protocol.cpp" line="80"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Cbb0000Protocol</name>
    <message>
        <location filename="protocol/cbb0000protocol.cpp" line="66"/>
        <location filename="protocol/cbb0000protocol.cpp" line="99"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbb0000protocol.cpp" line="67"/>
        <location filename="protocol/cbb0000protocol.cpp" line="111"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbb0000protocol.cpp" line="68"/>
        <location filename="protocol/cbb0000protocol.cpp" line="118"/>
        <source>Except</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Cbddb0bProtocol</name>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="27"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="28"/>
        <source>Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="29"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="30"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="31"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="32"/>
        <source>Gyro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="33"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="34"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="35"/>
        <source>Accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="36"/>
        <source>Latitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="37"/>
        <source>Longitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="38"/>
        <source>Altitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="39"/>
        <source>Evelocity</source>
        <oldsource>Eastvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="40"/>
        <source>Nvelocity</source>
        <oldsource>Northvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="41"/>
        <source>Uvelocity</source>
        <oldsource>Upvelocity</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="42"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="401"/>
        <source>Positionstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="43"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="402"/>
        <source>Speedstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="44"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="403"/>
        <source>Posturestatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="45"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="404"/>
        <source>Headingstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="46"/>
        <source>Millisecondofweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="47"/>
        <source>Gnssweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="48"/>
        <source>Query_Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="49"/>
        <source>Temp</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="50"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="400"/>
        <source>StartNum</source>
        <oldsource>Gpsstarnumber</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="51"/>
        <source>Wheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="52"/>
        <source>Rtkstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="53"/>
        <source>LatStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="54"/>
        <source>LonStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="55"/>
        <source>HStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="56"/>
        <source>Vn_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="57"/>
        <source>Ve_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="58"/>
        <source>Vd_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="59"/>
        <source>PitchStd</source>
        <oldsource>RollStd</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="60"/>
        <source>YawStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="61"/>
        <source>PpsDelay</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="87"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="88"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="116"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="89"/>
        <location filename="protocol/cbddb0bprotocol.cpp" line="125"/>
        <source>Except</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb0bprotocol.cpp" line="405"/>
        <source>Baselen</source>
        <oldsource>Baselength</oldsource>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Cbddb1bProtocol</name>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="27"/>
        <source>Roll</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="28"/>
        <source>Pitch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="29"/>
        <source>Yaw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="30"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="31"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="32"/>
        <source>Gyro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="33"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="34"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="35"/>
        <source>Accel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="36"/>
        <source>Latitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="37"/>
        <source>Longitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="38"/>
        <source>Altitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="39"/>
        <source>Evelocity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="40"/>
        <source>Nvelocity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="41"/>
        <source>Uvelocity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="42"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="393"/>
        <source>Positionstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="43"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="394"/>
        <source>Speedstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="44"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="395"/>
        <source>Posturestatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="45"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="396"/>
        <source>Headingstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="46"/>
        <source>Millisecondofweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="47"/>
        <source>Gnssweek</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="48"/>
        <source>Query_Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="49"/>
        <source>Temp</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="50"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="392"/>
        <source>StartNum</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="51"/>
        <source>Wheelspeed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="52"/>
        <source>Rtkstatus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="53"/>
        <source>LatStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="54"/>
        <source>LonStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="55"/>
        <source>HStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="56"/>
        <source>Vn_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="57"/>
        <source>Ve_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="58"/>
        <source>Vd_std</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="59"/>
        <source>RollStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="60"/>
        <source>PitchStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="61"/>
        <source>YawStd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="87"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="88"/>
        <location filename="protocol/cbddb1bprotocol.cpp" line="115"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbddb1bprotocol.cpp" line="397"/>
        <source>Baselen</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CcserialPage</name>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="205"/>
        <source>8</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="210"/>
        <source>7</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="215"/>
        <source>6</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="220"/>
        <source>5</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="249"/>
        <source>1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="254"/>
        <source>1.5</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="259"/>
        <source>2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="26"/>
        <source>Serial port assistant</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="57"/>
        <source>Serial port simulation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="62"/>
        <source>Serial communication</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="79"/>
        <source>Packet capture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="95"/>
        <source>Clear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="123"/>
        <source>Self Customization</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="128"/>
        <source>Read configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="133"/>
        <source>Restart the algorithm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="138"/>
        <source>Enter calibration mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="143"/>
        <source>Exit calibration mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="151"/>
        <source>Add CRLF</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="167"/>
        <source>Send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="180"/>
        <source>Online</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="197"/>
        <source>Baud rate:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="228"/>
        <source>Data bit:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="280"/>
        <source>Stop bit：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="288"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="293"/>
        <source>Odd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="298"/>
        <source>Event</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="306"/>
        <source>Data format:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="316"/>
        <source>Parity:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="324"/>
        <source>115200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="329"/>
        <source>96000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="334"/>
        <source>38400</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="339"/>
        <source>57600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="344"/>
        <source>460800</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="349"/>
        <source>921600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="354"/>
        <source>2000000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="363"/>
        <source>Hex</source>
        <oldsource>HEX</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="368"/>
        <source>Text</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="376"/>
        <source>Port：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="388"/>
        <source>Offline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="399"/>
        <source>FileName</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="426"/>
        <source>Import File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="445"/>
        <source>Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="456"/>
        <source>Save Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="518"/>
        <source>Exit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="51"/>
        <location filename="service_ui/ccserialpage.cpp" line="59"/>
        <location filename="service_ui/ccserialpage.cpp" line="153"/>
        <location filename="service_ui/ccserialpage.cpp" line="264"/>
        <location filename="service_ui/ccserialpage.cpp" line="270"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="51"/>
        <location filename="service_ui/ccserialpage.cpp" line="59"/>
        <source>Failed to write serial port, please check if the serial port configuration is correct! Serial port number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="505"/>
        <location filename="service_ui/ccserialpage.cpp" line="122"/>
        <location filename="service_ui/ccserialpage.cpp" line="135"/>
        <location filename="service_ui/ccserialpage.cpp" line="155"/>
        <location filename="service_ui/ccserialpage.cpp" line="172"/>
        <location filename="service_ui/ccserialpage.cpp" line="378"/>
        <location filename="service_ui/ccserialpage.cpp" line="508"/>
        <source>Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="153"/>
        <source>Serial port opening failed, please check if the serial port configuration is correct!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="162"/>
        <location filename="service_ui/ccserialpage.cpp" line="376"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="264"/>
        <location filename="service_ui/ccserialpage.cpp" line="270"/>
        <source>Please open the serial port first!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="312"/>
        <source>send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="359"/>
        <source>Recv</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="408"/>
        <source>Open binary data file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="408"/>
        <source>Dat files (*.dat)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.ui" line="433"/>
        <location filename="service_ui/ccserialpage.cpp" line="423"/>
        <location filename="service_ui/ccserialpage.cpp" line="440"/>
        <source>Start parsing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="434"/>
        <source>File read completed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="447"/>
        <location filename="service_ui/ccserialpage.cpp" line="451"/>
        <source>Read byte count</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="451"/>
        <source>Total number of bytes read</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="service_ui/ccserialpage.cpp" line="465"/>
        <source>Stop parsing</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CxyDisplayPage</name>
    <message>
        <location filename="service_ui/cxydisplaypage.ui" line="16"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CyzDisplayPage</name>
    <message>
        <location filename="service_ui/cyzdisplaypage.ui" line="14"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapGraphicsView</name>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="62"/>
        <source>Clear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="64"/>
        <source>Mark</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="66"/>
        <source>Sat map</source>
        <oldsource>Satellite map</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="70"/>
        <source>Road</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="73"/>
        <source>Operate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="74"/>
        <source>Ranging</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="75"/>
        <source>StartPt</source>
        <oldsource>Starting point</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="76"/>
        <source>EndPt</source>
        <oldsource>End point</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="mapview/mapgraphicsview.cpp" line="77"/>
        <source>Dist</source>
        <oldsource>Distance</oldsource>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="common_ui/cfilefieldconfigwidget.cpp" line="155"/>
        <source>Open Directory</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TlhV1Window</name>
    <message>
        <location filename="common/tlhv1window.ui" line="14"/>
        <source>MainWindow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="52"/>
        <source>原始数据</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="121"/>
        <source>...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="201"/>
        <source>Port：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="252"/>
        <source>Baud：</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="269"/>
        <source>115200</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="274"/>
        <source>96000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="279"/>
        <source>38400</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="284"/>
        <source>57600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="289"/>
        <source>460800</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="294"/>
        <source>921600</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="299"/>
        <source>2000000</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="309"/>
        <location filename="common/tlhv1window.cpp" line="184"/>
        <location filename="common/tlhv1window.cpp" line="200"/>
        <location filename="common/tlhv1window.cpp" line="216"/>
        <location filename="common/tlhv1window.cpp" line="227"/>
        <source>Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="316"/>
        <source>Setting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="336"/>
        <source>Collect Records</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="343"/>
        <source>Parameter Setting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="363"/>
        <source>WorkModel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="367"/>
        <source>DevMode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="379"/>
        <source>CaliMode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="395"/>
        <source>Help</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="402"/>
        <source>Language</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="409"/>
        <source>Tools</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="427"/>
        <source>ViewMode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="436"/>
        <location filename="common/tlhv1window.cpp" line="68"/>
        <source>Firmparam</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="445"/>
        <location filename="common/tlhv1window.cpp" line="66"/>
        <source>DataView</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="487"/>
        <source>陀螺标定</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="492"/>
        <location filename="common/tlhv1window.ui" line="495"/>
        <source>Developer</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="500"/>
        <source>File Tools</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="505"/>
        <source>caidan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="510"/>
        <source>Message Tools</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="515"/>
        <source>Calibration Tool</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="468"/>
        <source>转台控制</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="454"/>
        <source>DebugView</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="459"/>
        <source>About</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="473"/>
        <source>Chinese</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.ui" line="478"/>
        <source>English</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="62"/>
        <source>UserView</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="63"/>
        <source>SingleDebug</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="64"/>
        <source>MultiDebug</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="172"/>
        <source>I-NAV Platform</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="181"/>
        <location filename="common/tlhv1window.cpp" line="197"/>
        <location filename="common/tlhv1window.cpp" line="223"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="219"/>
        <location filename="common/tlhv1window.cpp" line="379"/>
        <location filename="common/tlhv1window.cpp" line="390"/>
        <location filename="common/tlhv1window.cpp" line="401"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="219"/>
        <source>Serial port opening failed, please check if the serial port configuration is correct!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="345"/>
        <location filename="common/tlhv1window.cpp" line="350"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="345"/>
        <source>The program is about to restart!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="350"/>
        <source>The program will switch languages on the next startup!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="common/tlhv1window.cpp" line="379"/>
        <location filename="common/tlhv1window.cpp" line="390"/>
        <location filename="common/tlhv1window.cpp" line="401"/>
        <source>Can not find exe file!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>cbb11dbbdProtocol</name>
    <message>
        <location filename="protocol/cbb11dbbdProtocol.cpp" line="131"/>
        <source>FLoss</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="protocol/cbb11dbbdProtocol.cpp" line="142"/>
        <source>ChFail</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
