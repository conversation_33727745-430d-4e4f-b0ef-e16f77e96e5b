﻿#include "cmapconfigwidget.h"
#include "ui_cmapconfigwidget.h"
#include <QColorDialog>
#include <QDebug>
#include <QStandardItemModel>
#include <QPainter>
#include "cconfigmanager.h"
#include "csubpagemanager.h"
#include "ccustomerviewpage.h"

CMapConfigWidget::CMapConfigWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CMapConfigWidget)
{
    ui->setupUi(this);

    int cbwidth = 60;
    ui->cb_flineboldnum->setIconSize(QSize(60, 16));
    ui->cb_slineboldnum->setIconSize(QSize(60, 16));
    ui->cb_tlineboldnum->setIconSize(QSize(60, 16));
    for(int bnum:m_vdbnum){
        QIcon dbicon = createLineIcon(cbwidth, 20, bnum, Qt::SolidLine);
        ui->cb_flineboldnum->addItem(dbicon, QString("%1").arg(bnum));
        ui->cb_slineboldnum->addItem(dbicon, QString("%1").arg(bnum));
        ui->cb_tlineboldnum->addItem(dbicon, QString("%1").arg(bnum));
        ui->cb_fdotboldnum->addItem(QString::number(bnum));
        ui->cb_sdotboldnum->addItem(QString::number(bnum));
        ui->cb_tdotboldnum->addItem(QString::number(bnum));
    }

    QVector<QString> vdstylename;
    vdstylename.append(tr("Solid line"));
    vdstylename.append(tr("Dashed line"));
    vdstylename.append(tr("Dotted line"));
    vdstylename.append(tr("Center line"));
    ui->cb_flinestyle->setIconSize(QSize(60, 16));
    ui->cb_slinestyle->setIconSize(QSize(60, 16));
    ui->cb_tlinestyle->setIconSize(QSize(60, 16));
    for(int i = 0; i < m_vdstyle.size();i++){
        QIcon dbicon = createLineIcon(cbwidth, 20, 2, m_vdstyle.at(i));
        ui->cb_flinestyle->addItem(dbicon, vdstylename.at(i));
        ui->cb_slinestyle->addItem(dbicon, vdstylename.at(i));
        ui->cb_tlinestyle->addItem(dbicon, vdstylename.at(i));
    }

    MapParmSt &mapst = CProtoParamData::getMapParmSt();
    ui->cb_showpreci->setCurrentIndex(mapst.m_idashMode);
    m_pflinestyle = mapst.m_pflinestyle;
    m_pslinestyle = mapst.m_pslinestyle;
    m_ptlinestyle = mapst.m_ptlinestyle;
    ui->cb_flinestyle->setCurrentIndex(mapst.m_pflinestyle);
    ui->cb_slinestyle->setCurrentIndex(mapst.m_pslinestyle);
    ui->cb_tlinestyle->setCurrentIndex(mapst.m_ptlinestyle);
    qDebug()<<"style:"<<mapst.m_pflinestyle<<mapst.m_pflinestyle<<mapst.m_pslinestyle<<mapst.m_sfdotColor<<mapst.m_iDotNum;

    m_flinewidth = mapst.m_cflinewidth;
    m_slinewidth = mapst.m_cslinewidth;
    m_tlinewidth = mapst.m_ctlinewidth;

    m_fdotwidth = mapst.m_cfdotwidth;
    m_sdotwidth = mapst.m_csdotwidth;
    m_tdotwidth = mapst.m_ctdotwidth;

    ui->cb_flineboldnum->setCurrentIndex(mapst.m_cflinewidth);
    ui->cb_slineboldnum->setCurrentIndex(mapst.m_cslinewidth);
    ui->cb_tlineboldnum->setCurrentIndex(mapst.m_ctlinewidth);

    ui->cb_fdotboldnum->setCurrentIndex(mapst.m_cfdotwidth);
    ui->cb_sdotboldnum->setCurrentIndex(mapst.m_csdotwidth);
    ui->cb_tdotboldnum->setCurrentIndex(mapst.m_ctdotwidth);

    m_fdotColor = mapst.m_sfdotColor;
    m_flineColor = mapst.m_sflineColor;
    m_sdotColor = mapst.m_ssdotColor;
    m_slineColor = mapst.m_sslineColor;
    m_tdotColor = mapst.m_stdotColor;
    m_tlineColor = mapst.m_stlineColor;
    QString fdotColor = QString("background-color:%1").arg(m_fdotColor);
    ui->bt_fdotcolor->setStyleSheet(fdotColor);
    QString flineColor = QString("background-color:%1").arg(m_flineColor);
    ui->bt_flinecolor->setStyleSheet(flineColor);
    QString sdotColor = QString("background-color:%1").arg(m_sdotColor);
    ui->bt_sdotcolor->setStyleSheet(sdotColor);
    QString slineColor = QString("background-color:%1").arg(m_slineColor);
    ui->bt_slinecolor->setStyleSheet(slineColor);
    QString tdotColor = QString("background-color:%1").arg(m_tdotColor);
    ui->bt_tdotcolor->setStyleSheet(tdotColor);
    QString tlineColor = QString("background-color:%1").arg(m_tlineColor);
    ui->bt_tlinecolor->setStyleSheet(tlineColor);

    m_vLngLat.append(QPointF(113.80979,22.751116)); //默认
    m_vLngLat.append(QPointF(114.06667, 22.61667)); //深圳
    m_vLngLat.append(QPointF(113.23333, 23.16667)); //广州
    m_vLngLat.append(QPointF(116.41667, 39.91667)); //北京
    m_vLngLat.append(QPointF(121.43333, 34.50000)); //上海
    m_vLngLat.append(QPointF(114.31667, 30.51667)); //武汉
    m_vLngLat.append(QPointF(113.15733, 23.06339)); //佛山
    m_vLngLat.append(QPointF(114.07092, 22.84553)); //东莞
    m_vLngLat.append(QPointF(120.61046, 31.33090)); //苏州
    m_vLngLat.append(QPointF(120.20000, 30.26667)); //杭州
    m_vLngLat.append(QPointF(104.06667, 30.66667)); //成都
    m_vLngLat.append(QPointF(106.45000, 29.56667)); //重庆

    //ui->lb_pridetail->setText("<99.99," + tr("The representation value is(") + tr("White pointer") + "*10)+("+ tr("Red pointer") + "/10)");

    m_pDefSite = m_vLngLat.at(0);

    if(mapst.m_pDefSite.x() < 0){
        ui->le_lng->setText(QString("%1").arg(m_vLngLat.at(0).x(), 12, 'f', 8));
        ui->le_lat->setText(QString("%1").arg(m_vLngLat.at(0).y(), 12, 'f', 8));
    }else{
        ui->le_lng->setText(QString("%1").arg(mapst.m_pDefSite.x(), 12, 'f', 8));
        ui->le_lat->setText(QString("%1").arg(mapst.m_pDefSite.y(), 12, 'f', 8));
    }

}

CMapConfigWidget::~CMapConfigWidget()
{
    delete ui;
}

QIcon CMapConfigWidget::createLineIcon(int width, int height, int lineWidth, Qt::PenStyle lineStyle) {
    QPixmap pixmap(width, height);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setPen(QPen(Qt::black, lineWidth, lineStyle));
    painter.drawLine(0, height / 2, width, height / 2); // 绘制水平直线
    //qDebug()<<"drawline width:"<<width;

    return QIcon(pixmap);
}

void CMapConfigWidget::setAllStyle(QLabel *lab, QString &color, int widthindex, QString &lcolor, int lwidthindex,  int styleindex){
    int w = lab->width();
    int h = lab->height();
    QPixmap pixmap(lab->width(), lab->height());
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setPen(QPen(QColor(lcolor), m_vdbnum.at(lwidthindex), m_vdstyle.at(styleindex)));
    painter.drawLine(10, h / 2, w-10, h / 2); // 绘制水平直线
    painter.setPen(QPen(QColor(color), m_vdbnum.at(widthindex)));
    painter.drawEllipse(lab->width()/2, lab->height()/2, 1, 1);
    lab->setPixmap(pixmap);
}

void CMapConfigWidget::setColor(QPushButton *bt, QString &scolor){
    QColorDialog dlg(this); //设置颜色对话框

    dlg.setWindowTitle("Color Editor");  //颜色对话框标题
    dlg.setCurrentColor(Qt::green);
    dlg.setCurrentColor(QColor(111,222,255));
    if( dlg.exec() == QColorDialog::Accepted )
    {
        QColor color = dlg.selectedColor();
        qDebug()<<"setColor:"<<scolor<<color.name();
        scolor = color.name();
        QString cmdcolor = QString("background-color:%1").arg(scolor);
        bt->setStyleSheet(cmdcolor);

    }
}


void CMapConfigWidget::showEvent(QShowEvent *event){
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}


void CMapConfigWidget::on_cb_city_currentIndexChanged(int index)
{
    m_pDefSite = m_vLngLat.at(index);
    ui->le_lng->setText(QString("%1").arg(m_vLngLat.at(index).x(), 12, 'f', 8));
    ui->le_lat->setText(QString("%1").arg(m_vLngLat.at(index).y(), 12, 'f', 8));
}

void CMapConfigWidget::getMapParms(MapParmSt &mapparmst){
    mapparmst.m_idashMode = ui->cb_showpreci->currentIndex();
    mapparmst.m_iFreshNum = ui->le_frushfrq->text().toUInt();
    mapparmst.m_iPlaystyle = ui->cb_lineshow->currentText().toInt();
    mapparmst.m_iInterTime = ui->le_intertime->text().toInt();
    mapparmst.m_iInterPoint = ui->le_interpoint->text().toInt();
    mapparmst.m_sfdotColor = m_fdotColor;
    mapparmst.m_ssdotColor = m_sdotColor;
    mapparmst.m_stdotColor = m_tdotColor;
    mapparmst.m_cfdotwidth = m_fdotwidth;
    mapparmst.m_csdotwidth = m_sdotwidth;
    mapparmst.m_ctdotwidth = m_tdotwidth;
    mapparmst.m_sflineColor = m_flineColor;
    mapparmst.m_sslineColor = m_slineColor;
    mapparmst.m_stlineColor = m_tlineColor;
    mapparmst.m_pflinestyle = m_pflinestyle;
    mapparmst.m_pslinestyle = m_pslinestyle;
    mapparmst.m_ptlinestyle = m_ptlinestyle;
    mapparmst.m_pDefSite = m_pDefSite;
    mapparmst.m_iShowLevel = ui->cb_maplevel->currentText().toInt();
}

void CMapConfigWidget::on_cb_lineshow_currentIndexChanged(int index)
{
    switch (index) {
    case 1://同步
        ui->le_frushfrq->setText("200");
        ui->le_intertime->setText("1000");
        ui->le_interpoint->setText("200");
        break;
    case 2://快放
        ui->le_frushfrq->setText("200");
        ui->le_intertime->setText("100");
        ui->le_interpoint->setText("200");
        break;
    case 3://慢放
        ui->le_frushfrq->setText("200");
        ui->le_intertime->setText("1000");
        ui->le_interpoint->setText("100");
        break;
    case 0://正常，默认
    default:
        ui->le_frushfrq->setText("200");
        ui->le_intertime->setText("200");
        ui->le_interpoint->setText("200");
    }
}

void CMapConfigWidget::on_cb_showpreci_currentIndexChanged(int index)
{
    CCustomerViewPage *cviewpage = static_cast<CCustomerViewPage *>(CSubPageManager::GetInstance()->GetObjectbyName("CCustomerViewPage"));
    connect(this, &CMapConfigWidget::sigParmsChange, cviewpage, &CCustomerViewPage::slotViewChange);
    emit sigParmsChange(index);
    disconnect(this, &CMapConfigWidget::sigParmsChange, cviewpage, &CCustomerViewPage::slotViewChange);
}

void CMapConfigWidget::on_bt_fdotcolor_clicked()
{
    setColor(ui->bt_fdotcolor, m_fdotColor);
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
}

void CMapConfigWidget::on_cb_fdotboldnum_currentIndexChanged(int index)
{
    m_fdotwidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
}

void CMapConfigWidget::on_bt_flinecolor_clicked()
{
    setColor(ui->bt_flinecolor, m_flineColor);
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
}

void CMapConfigWidget::on_cb_flineboldnum_currentIndexChanged(int index)
{
    m_flinewidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
}

void CMapConfigWidget::on_cb_flinestyle_currentIndexChanged(int index)
{
    m_pflinestyle = m_vdstyle.at(index);
    setAllStyle(ui->lb_fshow, m_fdotColor, m_fdotwidth, m_flineColor, m_flinewidth, m_pflinestyle);
}

void CMapConfigWidget::on_bt_sdotcolor_clicked()
{
    setColor(ui->bt_sdotcolor, m_sdotColor);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
}

void CMapConfigWidget::on_cb_sdotboldnum_currentIndexChanged(int index)
{
    m_sdotwidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
}

void CMapConfigWidget::on_bt_slinecolor_clicked()
{
    setColor(ui->bt_slinecolor, m_slineColor);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
}

void CMapConfigWidget::on_cb_slineboldnum_currentIndexChanged(int index)
{
    m_slinewidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
}

void CMapConfigWidget::on_cb_slinestyle_currentIndexChanged(int index)
{
    m_pslinestyle = m_vdstyle.at(index);
    setAllStyle(ui->lb_sshow, m_sdotColor, m_sdotwidth, m_slineColor, m_slinewidth, m_pslinestyle);
}

void CMapConfigWidget::on_bt_tdotcolor_clicked()
{
    setColor(ui->bt_tdotcolor, m_tdotColor);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}

void CMapConfigWidget::on_cb_tdotboldnum_currentIndexChanged(int index)
{
    m_tdotwidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}

void CMapConfigWidget::on_bt_tlinecolor_clicked()
{
    setColor(ui->bt_tlinecolor, m_tlineColor);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}

void CMapConfigWidget::on_cb_tlineboldnum_currentIndexChanged(int index)
{
    m_tlinewidth = m_vdbnum.at(index);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}

void CMapConfigWidget::on_cb_tlinestyle_currentIndexChanged(int index)
{
    m_ptlinestyle = m_vdstyle.at(index);
    setAllStyle(ui->lb_tshow, m_tdotColor, m_tdotwidth, m_tlineColor, m_tlinewidth, m_ptlinestyle);
}
