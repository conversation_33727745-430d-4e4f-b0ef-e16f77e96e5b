﻿#include <QDebug>
#include "caa6600protocol.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "csubpagemanager.h"

Caa6600Protocol::Caa6600Protocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect Caa6600Protocol";
       connect(this, &Caa6600Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &Caa6600Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
       connect(this, &Caa6600Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);

   }
   m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源陀螺-x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源陀螺-y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源陀螺-z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源加速度-X:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源加速度-Y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("源加速度-Z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("温度-x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("温度-y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("温度-z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-X:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Z:"));

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);

}

Caa6600Protocol::~Caa6600Protocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool Caa6600Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = (unsigned char)barr.at(4) + ((unsigned char)barr.at(5) << 8);

    if(m_uMsgLen != sizeof(Struaa6600)){
        qDebug()<<"length parse error";
        m_uMsgLen = sizeof(Struaa6600);
        return false;
    }

    return true;
    //m_uMsgLen = 408;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.mid(5, 1).toHex(' ')<<barr.mid(4,1).toHex(' ')<<(barr.at(5) << 8)<<len<<len2;
}

void Caa6600Protocol::paseMsg(const QByteArray msg){
    Struaa6600 st_aa6600;

    m_iReqCount++;

    QStringList dataValues;
    int gyrodre = m_sGyroDiret.toInt() == 0 ? 1: -1; //正向则无需取反，反向则需要取反
    //qDebug()<<sizeof (Struaa6600)<<msg.size()<<m_uMsgLen;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < m_uMsgLen){ //254
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size()<<msg.toHex(' ');
        return;
    }

    ::memcpy(&st_aa6600, msg.data(), sizeof(Struaa6600));
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;
    uint16_t currentindx = st_aa6600.st921info.selftestingcode;
    //qDebug()<<"code:"<<st_aa6600.st921info.selftestingcode;
    if(m_iFramIndx != -1 && ((m_iFramIndx + 1) % m_iFpgaFc != currentindx)){
        qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN;
        qDebug()<< m_sMsgCache.toHex();
        qDebug()<< msg.toHex();
        m_iLossFrameSum++;
        setFrameErr(E_FRAME_LOSS);
        if(m_bIsNeedShowL){
            emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") + QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
            m_bIsNeedShowL = false;
        }
    }

    if(!sum16CheckSum(msg)){
        qDebug()<<QTime::currentTime().toString()<<"sum16CheckSum error";
        m_iCheckFailSum++;
        setFrameErr(E_FRAME_CHECK);
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    m_sMsgCache.clear();
    m_sMsgCache.append(msg);
    m_iFramIndx = currentindx;
    dataValues.append(QString::number(currentindx));
    if(m_sGyrotype == "ORIDATA"){
        dataValues.append(QString::number(st_aa6600.st921info.fogx));
        dataValues.append(QString::number(st_aa6600.st921info.fogy));
        dataValues.append(QString::number(st_aa6600.st921info.fogz));
    }else{
        dataValues.append(QString::number(st_aa6600.st921info.fogx / 748986.469396));
        dataValues.append(QString::number(st_aa6600.st921info.fogy / 757839.137248));
        dataValues.append(QString::number(st_aa6600.st921info.fogz / 755024.395768 * gyrodre));
    }

    if(m_sAcceltype == "ORIDATA"){
        dataValues.append(QString::number(st_aa6600.st921info.accelerometery.faccel));
        dataValues.append(QString::number(st_aa6600.st921info.accelerometerx.faccel));
        dataValues.append(QString::number(st_aa6600.st921info.accelerometerz.faccel));
    }else{
        int accx, accy, accz;
        if(m_sAcceltype == "MEMS-HD6089"){
            //根据第24 位判断是否为负数
            if((st_aa6600.st921info.accelerometerx.u32accel & 0x800000) >> 23){
                //accx = (st_aa6600.st921info.accelerometerx.u32accel & 0x00FFFFFF) | 0xFF000000;
                //低24位取反
                accx = -1 * complement2original(st_aa6600.st921info.accelerometerx.u32accel & 0x00FFFFFF);
            }else{
                accx = (st_aa6600.st921info.accelerometerx.u32accel & 0x00FFFFFF);
            }

            if((st_aa6600.st921info.accelerometery.u32accel & 0x800000) >> 23){
                //accy = (st_aa6600.st921info.accelerometery.u32accel & 0x00FFFFFF) | 0xFF000000;
                accy = -1 * complement2original(st_aa6600.st921info.accelerometery.u32accel & 0x00FFFFFF);
            }else{
                accy = (st_aa6600.st921info.accelerometery.u32accel & 0x00FFFFFF);
            }

            if((st_aa6600.st921info.accelerometerz.u32accel & 0x800000) >> 23){
                //accz = (st_aa6600.st921info.accelerometerz.u32accel & 0x00FFFFFF) << 8 | 0xFF000000;
                accz = -1 * complement2original(st_aa6600.st921info.accelerometerz.u32accel & 0x00FFFFFF);
            }else{
                accz = (st_aa6600.st921info.accelerometerz.u32accel & 0x00FFFFFF);
            }

            dataValues.append(QString::number(accx / 400000.00));
            dataValues.append(QString::number(accy / 400000.00));
            dataValues.append(QString::number(accz / 400000.00));
        }else if(m_sAcceltype == "DACC-XIAN"){
            //DACC-西安傲航 直接转成浮点数x200.00
            dataValues.append(QString::number(st_aa6600.st921info.accelerometerx.faccel * 200.00));
            dataValues.append(QString::number(st_aa6600.st921info.accelerometery.faccel * -200.00));
            dataValues.append(QString::number(st_aa6600.st921info.accelerometerz.faccel * 200.00));
        }
    }

    dataValues.append(QString::number(st_aa6600.st921info.fogtemperaturex * 0.0625));
    dataValues.append(QString::number(st_aa6600.st921info.fogtemperaturey * 0.0625));
    dataValues.append(QString::number(st_aa6600.st921info.fogtemperaturez * 0.0625));

    dataValues.append(QString::number(st_aa6600.st921info.alg1_fogx));
    dataValues.append(QString::number(st_aa6600.st921info.alg1_fogy));
    dataValues.append(QString::number(st_aa6600.st921info.alg1_fogz));

    dataValues.append(QString::number(st_aa6600.st921info.alg1_accx));
    dataValues.append(QString::number(st_aa6600.st921info.alg1_accy));
    dataValues.append(QString::number(st_aa6600.st921info.alg1_accz));

    writeCvsFile("AA66", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        //qDebug()<<"time:"<<QTime::currentTime().toString()<<stime;
        if(m_bIsNeedKeys){
            emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        }else{
            emit sigDataUpdate(QStringList(), dataValues, m_sPortN, m_iProtoIndex);
        }

        m_bIsNeedShow = false;
    }

}

