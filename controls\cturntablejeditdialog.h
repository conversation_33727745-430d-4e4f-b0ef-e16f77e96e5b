﻿#ifndef CTURNTABLEJEDITDIALOG_H
#define CTURNTABLEJEDITDIALOG_H

#include <QDialog>
#include "cturntparseservice.h"

namespace Ui {
class CTurntableJEditDialog;
}

/*
*  转台自动脚本对话框
*/
class CTurntableJEditDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CTurntableJEditDialog(QWidget *parent = nullptr);
    ~CTurntableJEditDialog();
    void setJobContent(const QString &str);
    bool axleParse(const QStringList &lstr);
    bool turnTableTextParse(const QString &text);
    const QList<ES_PAIR> &getParseData();
    void showEvent(QShowEvent *) override;

private slots:
    void on_bt_save_clicked();

    void on_bt_cancel_clicked();

    void on_bt_reset_clicked();

private:
    Ui::CTurntableJEditDialog *ui;
    CTurnTParseService *m_turntser;
    QString m_sWorkData;
    QList<ES_PAIR> m_mCmdMulMap;
};

#endif // CTURNTABLEJEDITDIALOG_H
