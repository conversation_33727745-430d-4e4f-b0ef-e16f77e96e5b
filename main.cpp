﻿#include "tlhv1window.h"

#include <QApplication>
#include <QTranslator>
#include <QProcess>
#include <QDir>
#include "cconfigmanager.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    QString slang = G_CONFIG.getInstance().getValue("System.LANG").toString();
    QTranslator translator;
    if(slang == "CN"){
        if(!translator.load(":/qm/TLHV1_DEVP_cn.qm")){
            qDebug()<<"load fail";
        }
        QApplication::installTranslator(&translator);
    }
    TlhV1Window w;
    w.show();
    return a.exec();
}

