﻿#ifndef C3A0200PROTOCOL_H
#define C3A0200PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class C3a0200Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C3a0200Protocol(QObject *parent = nullptr);
    ~C3a0200Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru3a0200{
        unsigned char  head;
        unsigned short sensorid;
        unsigned short cmd;
        unsigned short len;
        uint32_t timestamp1;
        float CAccel_x;
        float CAccel_y;
        float CAccel_z;
        float Alocity_x;
        float Alocity_y;
        float Alocity_z;
        float LAcce_x;
        float LAcce_y;
        float LAcce_z;
        float Quaternion_w;
        float Quaternion_x;
        float Quaternion_y;
        float Quaternion_z;
        uint32_t timestamp2;
        float oriAccel_x;
        float oriAccel_y;
        float oriAccel_z;
        float oriGyro_x;
        float oriGyro_y;
        float oriGyro_z;
        float OriMagnet_x;
        float OriMagnet_y;
        float OriMagnet_z;
        float Temperature_due;
        float Temperature_uno;
        unsigned short checksum;
        unsigned char fmt0d;
        unsigned char fmt0a;
    }Stru3a0200;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList &datakeys, const QStringList dataValues, const QString sportN, const int protoindex);

private:

};

#endif // C3A0200PROTOCOL_H
