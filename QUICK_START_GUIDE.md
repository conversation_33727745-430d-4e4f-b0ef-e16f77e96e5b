# TLHV1_DEVP TCP通讯功能快速启动指南

## 快速开始

### 1. 编译项目
```bash
# 方法1: 使用构建脚本 (推荐)
build_project.bat

# 方法2: 使用Qt Creator
# 打开 TLHV1_DEVP_TCP.pro 文件，点击构建

# 方法3: 手动编译
qmake TLHV1_DEVP_TCP.pro
nmake
```

### 2. 启动TCP服务器
1. 运行编译好的程序
2. 点击主界面的"串口设置"按钮（现在是通讯设置）
3. 在"通讯类型"下拉框中选择"TCP"
4. 确认TCP配置：
   - IP地址：***************
   - 端口：8080
5. 点击"打开"按钮启动TCP服务器

### 3. 测试连接
```bash
# 使用Python测试脚本
python test_tcp_communication.py

# 使用telnet测试
telnet 192.168.************

# 发送十六进制测试数据
AA55010203040506070809
```

## 主要功能

### 通讯方式
- **串口通讯**: 保持原有功能不变
- **TCP通讯**: 新增TCP服务器功能，IP: ***************

### 支持的操作
- 数据解析和处理
- 参数配置
- 设备升级
- 温度补偿
- 设备标定
- 多客户端同时连接

## 文件说明

### 核心新增文件
- `service_imp/icommservice.h` - 通讯接口基类
- `service_imp/ctcpservice.h/cpp` - TCP服务实现
- `service_ui/ccommpage.h/cpp/ui` - 统一通讯界面
- `TLHV1_DEVP_TCP.pro` - 新项目配置文件

### 测试工具
- `test_tcp_client.cpp` - Qt TCP测试客户端
- `test_tcp_communication.py` - Python TCP测试脚本

## 常见问题

### Q: TCP服务器启动失败
A: 检查：
- IP地址是否正确
- 端口是否被占用
- 防火墙设置

### Q: 客户端无法连接
A: 确认：
- 网络连通性
- 服务器是否运行
- IP和端口配置

### Q: 数据传输异常
A: 验证：
- 数据格式是否正确
- 协议头是否匹配
- 网络稳定性

## 技术支持

如有问题，请查看：
1. `TCP_COMMUNICATION_README.md` - 详细技术文档
2. 程序控制台输出的调试信息
3. 测试工具的运行结果

## 版本信息

- 基于原TLHV1_DEVP项目
- 新增TCP通讯支持
- 保持所有原有功能
- 支持Qt 5.x版本
