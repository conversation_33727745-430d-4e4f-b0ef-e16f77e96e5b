#pragma once
#ifdef CALI_EXPORTS
#define CALI_API __declspec(dllexport)
#else
#define CALI_API __declspec(dllexport)
#endif

#ifndef CALI_H
#define CALI_H
#define  MAXMatrixSize 7
#define Pi 3.141592653589793238

#ifdef __cplusplus
extern "C" {
#endif

    typedef struct {
		double K;
		double F_0;
	}Scale_Factor;
	CALI_API void matcpy(double* A, const double* B, int n, int m);
	CALI_API void matmul(const char* tr, int n, int k, int m, double alpha, const double* A, const double* B, double beta, double* C);
	CALI_API int ludcmp(double* A, int n, int* indx, double* d);
	CALI_API void lubksb(const double* A, int n, const int* indx, double* b);
	CALI_API int matinv(double* A, int n);
	CALI_API void matrixSum(double* a, double* b, int n, int m, double coef, double* ab);
	CALI_API void Mat_Tr(int n, const double* A, double* A_T);
	CALI_API double MAX(double* arr, int len);
	CALI_API void  Gyro_Cali(double* w, int LenOfW, double* L_plus_x, double* L_minus_x, double* L_plus_y, double* L_minus_y, double* L_plus_z, double* L_minus_z, double* S_m);
	CALI_API void Gyro_Compensate();
	CALI_API void Acc_Cali(double Longitude, double* L_plus_x, double* L_minus_x, double* L_plus_y, double* L_minus_y, double* L_plus_z, double* L_minus_z, double* S_m);
	CALI_API Scale_Factor Scale(double* W, int LenOfW, double* F_j);
	CALI_API double Gyro_Scale(double* w, int LenOfW, double* F_plus, double* F_minus, double Fr);
	CALI_API double Gyro_Scale_Nonlin(double* w, int LenOfW, double* F_plus, double* F_minus, double Fr, double Fm);
	CALI_API double Gyro_Scale_Asym(double* w, int LenOfW, double* F_plus, double* F_minus, double Fr);
	CALI_API double STD(double* data, int Len);
	CALI_API double Scale_Repeat(double* K, int Len);
	CALI_API int Cal_Threshold(const double* B, int Len, double K, double beta, double* result, int* result_len);
	CALI_API int Cal_Resolution(const double* B, int Len, double K, double beta, double* result, int* result_len);
	CALI_API double Bias(double* F, int Len, double K);
	CALI_API void Smooth_10s(double* F, int Len, int Hz, int times, double* Ft);
	CALI_API double Bias_Stability(double* F, int Len, int Hz, int times, double K);
	CALI_API double Bias_Repeat(double* B, int Len);
#ifdef __cplusplus
}
#endif

#endif
