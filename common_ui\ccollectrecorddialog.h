﻿#ifndef CCOLLECTRECORDDIALOG_H
#define CCOLLECTRECORDDIALOG_H

#include <QDialog>
#include "cprotoparamdata.h"
#include <QDateTime>

namespace Ui {
class CCollectRecordDialog;
}

typedef struct _FILEINFO_ST_{
    QString filename;
    QDateTime filetime;
    qint64 filesize;
}FILEINFO_ST;

/*
*采集记录对话框类
*/
class CCollectRecordDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CCollectRecordDialog(QWidget *parent = nullptr);
    ~CCollectRecordDialog();

public slots:
    void showContextMenu(const QPoint &pos);

private:
    Ui::CCollectRecordDialog *ui;
    FileParmSt *m_pfilest;

signals:
    void sigLngLatFile(int , QString &);

private slots:
    void on_bt_refresh_clicked();
};

#endif // CCOLLECTRECORDDIALOG_H
