﻿/***************************************************************************
**                                                                        **
**  QCustomPlot, an easy to use, modern plotting widget for Qt            **
**  Copyright (C) 2011-2022 Emanuel <PERSON>hammer                            **
**                                                                        **
**  This program is free software: you can redistribute it and/or modify  **
**  it under the terms of the GNU General Public License as published by  **
**  the Free Software Foundation, either version 3 of the License, or     **
**  (at your option) any later version.                                   **
**                                                                        **
**  This program is distributed in the hope that it will be useful,       **
**  but WITHOUT ANY WARRANTY; without even the implied warranty of        **
**  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         **
**  GNU General Public License for more details.                          **
**                                                                        **
**  You should have received a copy of the GNU General Public License     **
**  along with this program.  If not, see http://www.gnu.org/licenses/.   **
**                                                                        **
****************************************************************************
**           Author: Emanuel Eichhammer                                   **
**  Website/Contact: https://www.qcustomplot.com/                         **
**             Date: 06.11.22                                             **
**          Version: 2.1.1                                                **
****************************************************************************/

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "qcustomplot.h"
#include "caxistag.h"

namespace Ui {
class CScrollLineChart;

}

enum RANGE_TYPE{ALL_RANGE, AVG_RANGE};

/*
* 数据视图中曲线窗口控件
*/
class CScrollLineChart : public QWidget
{
  Q_OBJECT

public:
  explicit CScrollLineChart(QWidget *parent = 0);
  ~CScrollLineChart();

  void setupPlot();
  void mouseMoveEvent(QMouseEvent *e);
  void resizeEvent(QResizeEvent *e);
  void genCursor();
  void delCursor();
  void appendData(double ddata, int);
  void reset();
  void stopFlush();
  void startFlush();
  void setTitleName(QString tname, int);
  void setFlushStatus(bool status){
      m_bIsRealFlush = status;
  }
  void setValidScrollBar(bool val);
  void ressetHRange(qint64, qint64, float);
  void ressetVRange(double, double, float);
  void getDataRange(QVector<double> &vrange);
  void setTotalGraph(int);
  void setWindRange(int widthrange);

private slots:
  void horzScrollBarChanged(int value);
  void vertScrollBarChanged(int value);
  void xAxisChanged(QCPRange range);
  void yAxisChanged(QCPRange range);
  void slotMousePress(QMouseEvent* e);
  void slotMouseWheel(QWheelEvent* e);
  void slotTImeOut();
private:
  Ui::CScrollLineChart *ui;
  CAxisTag *mTag1;
  bool m_hasCursor;
  int m_iTimerId;
  QCPBars *m_bars;
  RANGE_TYPE m_eRangeType;
  bool m_bIsFreeze;
  bool m_bIsRealFlush;
  long m_lCurrentRang;
  qint64 m_dLowerX;
  qint64 m_dUpperX;
  double m_dLowerY;
  double m_dUpperY;
  float m_fmarinx;
  float m_fmariny;
  double m_dCurrentCenter;
  long m_lMaxCount;
  int m_graphNum = 0;
  QCPItemTracer *m_itemTracer;
  QVector<QPen> m_vPens = {QPen(QColor(250, 120, 0)), QPen(QColor(255,0,255)), QPen(QColor(100, 120, 255)), QPen(QColor(100, 255, 0)), QPen(QColor(0,255,255))};
  QElapsedTimer t;
  QTimer m_tflushTimer;
  int m_iWindRange;
  qint64 m_i64Counter;
};

#endif // MAINWINDOW_H
