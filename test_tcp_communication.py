#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP通讯测试脚本
用于测试TLHV1_DEVP项目的TCP服务器功能
"""

import socket
import time
import threading

class TcpTestClient:
    def __init__(self, host='***************', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.running = False

    def connect(self):
        """连接到TCP服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            print(f"正在连接到 {self.host}:{self.port}...")
            self.socket.connect((self.host, self.port))
            self.connected = True
            print("连接成功!")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
                print("连接已断开")
            except:
                pass

    def send_data(self, data):
        """发送数据"""
        if not self.connected:
            print("未连接到服务器")
            return False

        try:
            if isinstance(data, str):
                # 如果是十六进制字符串，转换为字节
                if all(c in '0123456789ABCDEFabcdef ' for c in data):
                    data = bytes.fromhex(data.replace(' ', ''))
                else:
                    data = data.encode('utf-8')

            self.socket.send(data)
            print(f"发送数据: {data.hex().upper()}")
            return True
        except Exception as e:
            print(f"发送数据失败: {e}")
            return False

    def receive_data(self):
        """接收数据"""
        while self.running and self.connected:
            try:
                data = self.socket.recv(1024)
                if data:
                    print(f"接收数据: {data.hex().upper()}")
                else:
                    print("服务器断开连接")
                    break
            except socket.timeout:
                continue
            except Exception as e:
                print(f"接收数据错误: {e}")
                break

        self.connected = False

    def start_receive_thread(self):
        """启动接收线程"""
        self.running = True
        receive_thread = threading.Thread(target=self.receive_data)
        receive_thread.daemon = True
        receive_thread.start()
        return receive_thread

    def send_test_data(self):
        """发送测试数据"""
        test_data_list = [
            "AA55010203040506070809",  # 测试数据1
            "BB66010203040506070809",  # 测试数据2
            "CC77010203040506070809",  # 测试数据3
            "DD88010203040506070809",  # 测试数据4
        ]

        for data in test_data_list:
            if not self.connected:
                break
            self.send_data(data)
            time.sleep(2)

def main():
    print("TCP通讯测试程序")
    print("================")

    # 创建测试客户端
    client = TcpTestClient()

    # 连接到服务器
    if not client.connect():
        print("无法连接到服务器，请确保:")
        print("1. TLHV1_DEVP程序正在运行")
        print("2. TCP服务器已启动")
        print("3. IP地址和端口配置正确")
        return

    # 启动接收线程
    client.start_receive_thread()

    try:
        while True:
            print("\n选择操作:")
            print("1. 发送测试数据")
            print("2. 发送自定义数据")
            print("3. 自动测试")
            print("4. 退出")

            choice = input("请输入选择 (1-4): ").strip()

            if choice == '1':
                client.send_test_data()

            elif choice == '2':
                data = input("请输入十六进制数据 (如: AA55010203): ").strip()
                if data:
                    client.send_data(data)

            elif choice == '3':
                print("开始自动测试...")
                for i in range(10):
                    if not client.connected:
                        break
                    test_data = f"AA55{i:02d}0203040506070809"
                    client.send_data(test_data)
                    time.sleep(1)
                print("自动测试完成")

            elif choice == '4':
                break

            else:
                print("无效选择")

    except KeyboardInterrupt:
        print("\n用户中断")

    finally:
        client.disconnect()
        print("测试结束")

if __name__ == "__main__":
    main()
