﻿#ifndef CBASEPROTOCOL_H
#define CBASEPROTOCOL_H

#include <QObject>
#include <QFile>
#include <QDateTime>
#include <QMap>
#include <QTimer>
#include <QDebug>
#include "ctlhtools.h"
#include "cprotoparamdata.h"

#define c_txmsgdatasize	8

union DataS32{
    float faccel;
    uint32_t u32accel;
    int32_t i32accel;
};

typedef struct _Vector3i32 {
    int x;
    int y;
    int z;
} Vector3i32;

typedef struct _Vector3f {
    float x;
    float y;
    float z;
} Vector3f;


typedef enum _E_FRAM_ERR{E_FRAME_OK, E_FRAME_LOSS, E_FRAME_CHECK}E_FRAM_ERR;

typedef enum _E_PAGE_SIG{E_PAGE_COMCHG} E_PAGE_SIG;

/*顺序需要与CProtocolFactory中的E_HEADPROTO一致*/
static QStringList g_sProtocolHead = {"aa55", "aa33", "a5a5", "aa66", "b55b","4a0100", "3a0100", "3a0200", "bddb0b", "bccb0b", "a55a", \
                                      "55aa", "bb00dbbd", "bb11dbbd", "a6a6", "4a020a01", "4a020b01","3b020b", "cddc0b", "bddb1b"};

class CBaseProtocol : public QObject
{
    Q_OBJECT
public:
    explicit CBaseProtocol(QObject *parent = nullptr);
    ~CBaseProtocol();
    bool sum16CheckSum(const QByteArray msg);
    int complement2original(int num);
    static int getProtoIndex(QString protoname);
    int readProtocolIndex(){
        return m_iProtoIndex;
    }
    void writeProtocoIndex(int index){
        m_iProtoIndex = index;
    }

    bool lossFrameCheck(int framdindx);
public:
    typedef struct _txmsgserialport {
        unsigned char head0;
        unsigned char head1;
        short len;				//sizeof(txmsgserialport_t)
        unsigned char cmd;
        int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
        int parameter1;
        int msglistindex;
        int msgindex;
        unsigned char data[c_txmsgdatasize];
        unsigned char checksum;
        unsigned char tail;
    } txmsgserialport_t;
    typedef struct _txmsgonerecord {
        int willbesend;
        int breceived;
        int msglistindex;
        int msgindex;
        int sendtimes;
        unsigned int sendtimetick;
        txmsgserialport_t msg;
    } txmsgonerecord_t;
protected:
    QByteArray m_bMsgHead;
    int m_uMsgLen;
    QByteArray m_bMsgBody;
    bool m_bIsNeedShow;
    bool m_bIsNeedShowL;
    bool m_bIsNeedShowC;
    QString m_sPortN;
    QStringList m_slProtoKeys;
    QStringList m_slProtoValues;
    QString m_sAcceltype;
    QString m_sGyrotype;
    QString m_sGyroDiret;
    double m_dCatFreq;
    double m_dGyrFreq;
    int m_icatFreqCounts;
    int m_iCheckFailSum;
    int m_iLossFrameSum;
    bool m_bIsNeedKeys;
    uint32_t m_uRunTimes;
    int m_iFramIndx = -1;
    QByteArray m_sMsgCache;
    QString m_sFilePrefix;
    QString m_sFileName;
    int m_iProtoIndex;
    QVector<int> m_vWriteFields;
    int m_iFildSize;
    bool m_bIsNeedDat;
    int m_iDatType;
    int m_iFpgaFc;
    int m_iTimeout;
    int m_iReqCount;
    int m_iCurrentFeq;
    uint32_t m_iGnsstime;
    int m_iMaxFramIndx = -1;
public:
    virtual bool setProtoLength(const QByteArray &barr);
    virtual bool preInit();
    virtual void paseMsg(const QByteArray msg);
    virtual void writeCvsFile(QString fileName, QStringList &vskey, QStringList &dataList, int filetype = 0);
    virtual void writeDatFile(const QString &prefix,const QByteArray &arrdata);
public:
    int getProtoLength(){
        return m_uMsgLen;
    }

    QByteArray getProtoHead(){
        return m_bMsgHead;
    }

    bool filePrefixChange(QString prefilex, QStringList &vskey, int filetype = 0);
    QString genDtFileName(QString prefix, QString suffix);
    QFile *getFileHandle(int filetype);
    void setFileHandle(QFile *filehandle, int filetype);
    void setFileSave(bool filesave);
    void setPortN(QString sportn);
    void seriFeedback(unsigned char cmd, int parameter0, int parameter1);
    E_FRAM_ERR getFrameErr(){
        return m_eFrameErr;
    }

    void setFrameErr(E_FRAM_ERR err){
        m_eFrameErr = err;
    }

    void setProtoParam(const QMap<QString, QString> &map);
    QString getRunTimes();
    void setRunTimes(uint32_t times);
    bool sumEorCheck(const QByteArray msg, int startpos, int endpos);
    bool sum8CheckSum(const QByteArray msg, int bpos, int endpos);
    void reserverData(QByteArray &src, int pos, int size);
    bool sum8to16CheckSum(const QByteArray msg, int bpos, int endpos);
    QString getSysStatDesByC(const char cstat);
    void calculateEulerAngles(const double qw, const double qx, const double qy, const double qz, double &roll, double &pitch, double &yaw);

signals:
    void sigDestory();
    void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
public slots:
    void slotFromDriPage(const int &type, const QStringList &params);
private:
    QFile *m_pcvsfile;
    QFile *m_freqpcvsfile;
    QFile *m_forifile;
    int m_bisSaveFile;
    E_FRAM_ERR m_eFrameErr;
    QTextCodec *m_tCode;
    QTimer m_UpgTimer;
    qint64 m_writedLine;
    FileParmSt *pFilest;

};

#endif // CBASEPROTOCOL_H
