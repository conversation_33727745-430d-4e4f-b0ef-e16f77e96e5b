﻿#ifndef CDEVICEPARMWIDGET_H
#define CDEVICEPARMWIDGET_H

#include <QWidget>
#include <QButtonGroup>

namespace Ui {
class CDeviceParmWidget;
}

/*
*固件参数设置子页面
*/
class CDeviceParmWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CDeviceParmWidget(QWidget *parent = nullptr);
    ~CDeviceParmWidget();
    void setBaurdParm(int &ibuard);
    void getBaurdParm(int &ibuard);  //串口波特率
    void setFrqParm(int &ifrq);
    void getFrqParm(int &ifrq);  //输出频率
    void setSitParm(int isit, int isec);
    void getSitParm(int &isit, int &isec);  //位置
    void setLevelArmParm(float &ix, float &iy, float &iz);
    void getLevelArmParm(float &ix, float &iy, float &iz); //杆臂
    void setAnteParm(float &ix, float &iy, float &iz);
    void getAnteParm(float &ix, float &iy, float &iz);    //天线安装角度
    void setGnssInitParm(QVector<float> &vParm);
    void getGnssInitParm(QVector<float> &vParm); //gnss初始值
    void setAngDevParm(float &ix, float &iy, float &iz);  //角度偏差
    void getAngDevParm(float &ix, float &iy, float &iz);
    void setAfterWheelParm(float &ix, float &iy, float &iz);  //惯导后轮中心位置矢量
    void getAfterWheelParm(float &ix, float &iy, float &iz);
    void setBiasTimeParm(int &itime);
    void getBiasTimeParm(int &itime);  //静态零偏时间
    void setDebugFlagParm(int &iflag);
    void getDebugFlagParm(int &iflag);  //调试模式
    void setFogTypeParm(int &itype);
    void getFogTypeParm(int &itype);  //陀螺类型
    void setGpsTypeParm(int &itype);
    void getGpsTypeParm(int &itype);  //gps类型
    void setDataTypeParm(int &itype);
    void getDataTypeParm(int &itype);  //数据输出类型
    void getGyroCaliParm(double &ix, double &iy, double &iz);
    void setGyroCaliParm(double &ix, double &iy, double &iz);
    void getAddiCaliParm(double &ix, double &iy, double &iz);
    void setAddiCaliParm(double &ix, double &iy, double &iz);
    void getSdCardOpParm(uint8_t &ivf, uint8_t &ivp);
    int getCheckedParm();

private:
    Ui::CDeviceParmWidget *ui;
    QButtonGroup *m_btGroup;
    int m_iadirect[8][3] = {{1, 1, 1}, {1, 1, -1}, {1, -1, 1}, {1, -1, -1}, {-1, 1, 1}, {-1, 1, -1}, {-1, -1, 1}, {-1, -1, -1}};
};

#endif // CDEVICEPARMWIDGET_H
