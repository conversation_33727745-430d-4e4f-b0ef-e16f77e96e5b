﻿#include "cturntablejeditdialog.h"
#include "ui_cturntablejeditdialog.h"
#include "ctlhtools.h"
#include <QMessageBox>
#include <QDebug>

CTurntableJEditDialog::CTurntableJEditDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CTurntableJEditDialog)
{
    ui->setupUi(this);
    m_turntser = new CTurnTParseService(this);
}

CTurntableJEditDialog::~CTurntableJEditDialog()
{
    delete ui;
}

void CTurntableJEditDialog::setJobContent(const QString &str){
    ui->pe_Autotask->clear();
    ui->pe_Autotask->appendPlainText(str);
}

//读取脚本文件并保存
void CTurntableJEditDialog::on_bt_save_clicked()
{
    if(m_mCmdMulMap.size() != 0){
        QMessageBox::critical(this, QString::fromLocal8Bit("保存失败"), QString::fromLocal8Bit("已有自动任务信息，请先在未采集的状态下先点击重置后重新导入！"));
        return;
    }

    m_sWorkData = ui->pe_Autotask->toPlainText();

    if( !CTlhTools::isValidString(m_sWorkData, QString(".,;-_#\\r\\n")) || !turnTableTextParse(m_sWorkData)){
        QMessageBox::critical(this, QString::fromLocal8Bit("校验失败"), QString::fromLocal8Bit("自动任务校验失败，请检查任务信息！"));
        m_sWorkData.clear();
        return;
    }else{
        qDebug()<<"m_mCmdMulMap size:"<<m_mCmdMulMap.size();
        close();
    }
}

//将命令字符串解析成为可执行的命令序列
bool CTurntableJEditDialog::turnTableTextParse(const QString &text){
     return m_turntser->turnTableTextParse(text, m_mCmdMulMap);
}

//或者解析后的命令序列
const QList<ES_PAIR> &CTurntableJEditDialog::getParseData(){
    if(m_mCmdMulMap.size() == 0){
        on_bt_save_clicked();
        m_mCmdMulMap.append(ES_PAIR(E_INSTOP, QStringList()<<""));
        m_mCmdMulMap.append(ES_PAIR(E_OUTSTOP, QStringList()<<""));
        m_mCmdMulMap.append(ES_PAIR(E_DELY, QStringList()<<"UntilReach"));
        m_mCmdMulMap.append(ES_PAIR(E_INPOS, QStringList()<<"0.0"<<"10.0"<<"10.0"));
        m_mCmdMulMap.append(ES_PAIR(E_OUTPOS, QStringList()<<"0.0"<<"10.0"<<"10.0"));
        m_mCmdMulMap.append(ES_PAIR(E_DELY, QStringList()<<"UntilReach"));
        m_mCmdMulMap.append(ES_PAIR(E_INSTOP, QStringList()<<""));
        m_mCmdMulMap.append(ES_PAIR(E_OUTSTOP, QStringList()<<""));
    }
    return m_mCmdMulMap;
}

void CTurntableJEditDialog::on_bt_cancel_clicked()
{
    close();
}

//弹出编辑框时
void CTurntableJEditDialog::showEvent(QShowEvent *){
    if(!m_sWorkData.isEmpty()){
        ui->pe_Autotask->clear();
        ui->pe_Autotask->appendPlainText(m_sWorkData);
    }
}

//清理所有脚本数据
void CTurntableJEditDialog::on_bt_reset_clicked()
{
    m_sWorkData.clear();
    m_mCmdMulMap.clear();
    ui->pe_Autotask->clear();
}
