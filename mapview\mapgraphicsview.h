﻿#ifndef MAPGRAPHICSVIEW_H
#define MAPGRAPHICSVIEW_H

#include "graphicsitemgroup.h"
#include "mapStruct.h"
#include <QGraphicsView>
#include <QPushButton>
#include <QToolButton>
#include <QSlider>
#include <QCheckBox>
#include <QComboBox>
#include <QLabel>
#include <QLineEdit>
#include <QHBoxLayout>
#include <QFile>
#include "cplaybackwidget.h"
#include "cprotoparamdata.h"

/*
* 地图轨迹类
*/
class CMapTrajLine{
public:
    CMapTrajLine(QGraphicsScene *scene, int level);

    void drawLine();
    void appendData(QPointF &);
    void clear();
    void setColor(QColor);
    QColor getColor();
    void setlevel(int level){
        m_level = level;
    }
    void setPen(QPen&, QPen &);
    QPointF addMark(){
        if(m_vLinepos.size() != 0){
            m_vMarkpos.append(m_vLinepos.last());
            return m_vLinepos.last();
        }
        return QPointF(0,0);
    }
private:
    int m_iLineId;
    QGraphicsPathItem *m_itemP;
    QGraphicsPathItem *m_itemD;
    QGraphicsPixmapItem *m_itemMak;
    QVector<QGraphicsPixmapItem *> m_vAllmark;
    QVector<QString> m_vMarkTips;
    QGraphicsTextItem *m_itemSite;
    QList<QPointF> m_vLinepos;
    QList<QPointF> m_vMarkpos;
    int m_level;
    QGraphicsScene *m_scene;
    //QColor m_cLineColor;
    QPen m_cLinePen;
    //QColor m_cDotColor;
    QPen m_cDotPen;
};


/*
* 地图视图类
*/
class MapGraphicsView : public QGraphicsView
{
    Q_OBJECT
public:
    explicit MapGraphicsView(QWidget* parent = nullptr);
    ~MapGraphicsView() override;

    void setRect(int level);
    void drawImg(const ImageInfo& info);
    void drawLine(int id, QPointF pf, float process);
    void drawLine();
    void clear();
    void urlChange();
    void addRoad();
public slots:
    void slotShowPbWid();
    void slotSliderChange(int);

signals:
    void updateImage(const ImageInfo& info);   // 添加瓦片图
    void showRect(QRect rect);
    void mousePos(QPoint pos);

protected:
    void mousePressEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    void showEvent(QShowEvent* event) override;

private:
    void getShowRect();   // 获取显示范围

private:
    QGraphicsScene* m_scene = nullptr;
    int m_level = 14;           // 当前显示瓦片等级
    bool m_moveView = false;   // 鼠标移动显示视图
    QPointF m_pos;   //窗口坐标
    QPointF m_scenePos;  //左上角虚拟坐标
    QPointF m_initscenePos;  //窗口中间位置虚拟坐标
    QPointF m_initlnglatPos;  //窗口中间位置经纬度坐标
    QHash<quint16, GraphicsItemGroup*> m_itemGroup;   // 瓦片图元组
    QHash<quint16, GraphicsItemGroup*> m_overitemGroup;
    QPushButton *m_clearbt;
    QPushButton *m_markbt;
    QPushButton *m_satellitebt;
    QCheckBox *m_chRoad;
    QFrame *btframe;
    QSlider *mapslider;
    QToolButton *m_addtb;
    QToolButton *m_subtb;
    int currUrlType;
    bool showRoad;
    CPlaybackWidget *m_playbwd;
    QToolButton *m_tHidePb;
    bool m_bPbisShow;
    bool m_bDrwLnBg;
    bool m_bWidShowed;
    QMap<int, CMapTrajLine *> m_mCmapTlines;
    QVector<QColor> m_vLineColor;
    int m_ifirstrow;
    bool m_bisFollow;
    int m_beforelevel;
    QVector<QPen> m_vLinePens;
    QVector<QPen> m_vDotpen;
    int m_iDotNum;
    int m_iFrushFrq = 1;
    int m_iFrushSum = 1;
    QLabel *m_lbstartpos;
    QLabel *m_lbstoppos;
    QLabel *m_lbdistance;
    QLineEdit *m_lestartpos;
    QLineEdit *m_lestoppos;
    QLineEdit *m_ledistance;
    QComboBox *m_opcbox;
    QHBoxLayout *m_hlayout;
    QString m_markfilename;
    QFile *m_markfile;
};



#endif   // MAPGRAPHICSVIEW_H
