﻿#include "ccustomerviewpage.h"
#include "ui_ccustomerviewpage.h"
#include "cloadfiletaskdlg.h"
#include "geturl.h"
#include <QDateTime>
#include <QMessageBox>
#include <QTabWidget>
#include <QFile>
#include <QtConcurrent>
#include "cprotoparamdata.h"
#include <QDockWidget>
#include "cconfigmanager.h"
#include "csubpagemanager.h"

CCustomerViewPage::CCustomerViewPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CCustomerViewPage)
{
    ui->setupUi(this);
    m_fstframscale = 3.333;
    ui->lb_rtk->settext("RTK");
    ui->lb_wheelspeed->settext(tr("WSS"));
    ui->lb_site->settext(tr("POS"));
    ui->lb_speed->settext(tr("SPD"));
    ui->lb_attitude->settext(tr("ATT"));
    ui->lb_calibration->settext(tr("CAL"));
    ui->lb_heading->settext(tr("HDG"));
    ui->lb_heading->setVisible(false);
    ui->stw_customview->setCurrentIndex(1);

    cplatevpg = static_cast<CPlateViewPage *>(ui->stw_customview->widget(1));
    cmapvpg = static_cast<CMapViewPage *>(ui->stw_customview->widget(0));
    cflypg = static_cast<CFlyViewPage *>(ui->stw_customview->widget(2));
    cflypg->setAttribute(Qt::WA_StyledBackground, true);

    offsettest = 0.000001f;
    m_fFastProcess = 0.0;

    connect(this, &CCustomerViewPage::sigLngLatinfo, this, &CCustomerViewPage::slotLngLatinfo);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::setprocess, this, [=](float p){
        m_fFastProcess = p;
    });
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::setplaystat, this, [=](bool b){
        m_bPlaystat = b;
    });
    m_bPlaystat = true;
    m_rownum = -2;
    m_iViewMode = G_CONFIG.getValue("Map.m_iDashMode").toInt(); //默认显示行驶视图

    if(m_iViewMode == 0){
        ui->stw_customview->setCurrentIndex(1);
    }else{
        ui->stw_customview->setCurrentIndex(2);
    }

    setObjectName("CCustomerViewPage");
    CSubPageManager::GetInstance()->RegisterPage("CCustomerViewPage", this);

}

CCustomerViewPage::~CCustomerViewPage()
{
    delete ui;
}

void CCustomerViewPage::resizeEvent(QResizeEvent *event){
    ui->fm_navstatus->resize(ui->fm_navstatus->width(), ui->fm_navstatus->width() / m_fstframscale);
    qDebug()<<"status fram:"<<ui->fm_navstatus->width()<<ui->fm_navstatus->width() / m_fstframscale;
    QList<QLabel *> childWidget= findChildren<QLabel *>();
    for(QLabel *lab:childWidget){
        if(lab->objectName().startsWith("lb_unit")) {
            lab->setStyleSheet("font-size:14px;");
        }
    }
}

void CCustomerViewPage::slotViewChange(int index){
    m_iViewMode = index;
    ui->stw_customview->setCurrentIndex(1 + index);
}

void CCustomerViewPage::on_bt_showplate_clicked()
{
    if(m_iViewMode == 0){
        ui->stw_customview->setCurrentIndex(1);
    }else{
        ui->stw_customview->setCurrentIndex(2);
    }
}

void CCustomerViewPage::on_bt_showmap_clicked()
{
    ui->stw_customview->setCurrentIndex(0);
}

QColor CCustomerViewPage::getColorByVal(int val){
    switch(val){
    case 0:
        return Qt::gray;
    case 1:
        return Qt::yellow;
    case 2:
        return QColor("#18D950");
    case 3:
        return  Qt::red;
    case 4:
        return Qt::blue;
    case 5:
        return Qt::white;
    default:
        return Qt::gray;
    }
}

void CCustomerViewPage::slotDataShow(const QStringList dataValues, const QString portn, const int protoindex){
    QDateTime currentTime = QDateTime::currentDateTime();

    if(!m_mLineIdMap.contains(portn)){
        for(int i = 0; i < m_mLineIdMap.size(); i++){
            if(currentTime.toSecsSinceEpoch() - m_mLineIdMap.values().at(i).lasttimes > 3){
                int index = ui->cb_showportn->findText(m_mLineIdMap.keys().at(i));
                ui->cb_showportn->removeItem(index);
                m_mLineIdMap.remove(m_mLineIdMap.keys().at(i));
            }
        }

        m_mLineIdMap[portn].id = m_mLineIdMap.size();
        m_mLineIdMap[portn].lasttimes = currentTime.toSecsSinceEpoch();
        ui->cb_showportn->addItem(portn);
        m_sPortN = portn;
        ui->cb_showportn->setCurrentText(portn);
    }

    m_mLineIdMap[portn].lasttimes = currentTime.toSecsSinceEpoch();

    //过滤掉初始化时的无效值,经度大于0.0001时才绘制
    double lng = dataValues.at(3).toDouble();
    double lat = dataValues.at(4).toDouble();
    if(qAbs(lng) > 0.0001){
        cmapvpg->showmapview(m_mLineIdMap[portn].id, lat, lng, 0);
    }

    if(m_sPortN != portn){
        //qDebug()<<"ui->cb_showportn->currentText():"<<ui->cb_showportn->currentText()<<portn<<dataValues.at(17);
        return;
    }

    ui->le_datetime->setText(currentTime.toString("yy-MM-dd HH:mm:ss"));
    ui->le_gssweek->setText(dataValues.at(10));
    ui->le_gsssecond->setText(dataValues.at(9));
    ui->le_pitch->setText(dataValues.at(1));          //俯仰
    ui->le_roll->setText(dataValues.at(0));           //横滚
    ui->le_heading->setText(dataValues.at(2));        //航向
    ui->le_longitude->setText(dataValues.at(4));      //经度
    ui->le_latitude->setText(dataValues.at(3));       //纬度
    ui->le_altitude->setText(dataValues.at(5));       //高度
    ui->le_eastspeed->setText(dataValues.at(6));      //东向速度
    ui->le_northspeed->setText(dataValues.at(7));    //北向速度
    ui->le_velocityspeed->setText(dataValues.at(8)); //天向速度
    ui->le_starnum->setText(dataValues.at(18));       //卫星数
    ui->le_sysstat->setText(dataValues.at(19) +"," + dataValues.at(20) + "," + dataValues.at(21) + "," + dataValues.at(22));
    ui->le_temp->setText(dataValues.at(17));
    ui->le_baselinelen->setText(dataValues.at(14));
    ui->le_posstat->setText(dataValues.at(13));
    ui->le_speedstat->setText(dataValues.at(16));
    ui->le_caliparm->setText(dataValues.at(11));

    int rtkstat = dataValues.at(13).toInt();
    if(rtkstat == 4){
        ui->lb_rtk->setBgColor(getColorByVal(2));
    }else if(rtkstat == 3){
        ui->lb_rtk->setBgColor(getColorByVal(1));
    }else{
        ui->lb_rtk->setBgColor(getColorByVal(0));
    }

    if(dataValues.at(19) == "1"){
        ui->lb_site->setBgColor(getColorByVal(2));
    }else{
        ui->lb_site->setBgColor(getColorByVal(0));
    }

    if(dataValues.at(20) == "1"){
        ui->lb_speed->setBgColor(getColorByVal(2));
    }else{
        ui->lb_speed->setBgColor(getColorByVal(0));
    }

    if(dataValues.at(21) == "1"){
        ui->lb_attitude->setBgColor(getColorByVal(2));
    }else{
        ui->lb_attitude->setBgColor(getColorByVal(0));
    }

    if(dataValues.at(16) == "1"){
        ui->lb_wheelspeed->setBgColor(getColorByVal(2));
    }else{
        ui->lb_wheelspeed->setBgColor(getColorByVal(0));
    }

    ui->lb_calibration->setBgColor(getColorByVal(dataValues.at(12).toInt()));

    if(dataValues.at(22) == "1"){
        ui->lb_heading->setBgColor(getColorByVal(2));
    }else{
        ui->lb_heading->setBgColor(getColorByVal(0));
    }

    //}else{//在地图上显示数据
        //offsettest = offsettest + 0.0003f;
        //double lng1 = dataValues.at(4).toDouble();
        //double lng = lng1 + offsettest;
        //double lat = dataValues.at(3).toDouble() + offsettest;
        //qDebug()<<"lng-lat:"<<dataValues.at(4).toFloat()<<lng1<<lng<<lat<<offsettest;

        //static double st_lng = 113.80979;
        //st_lng += 0.00002;
        //static double st_lat = 22.751116;
        //st_lat -= 0.000005;
        //cmapvpg->showmapview(0, st_lng, st_lat, 0);
    //}
}

void CCustomerViewPage::slotDataPlayback(int rownum, QString &filename){
    m_fFastProcess = 0;
    qDebug()<<"slotDataPlayback:"<<filename;
    //切换到地图窗口
    if(!ui->mapviewpage->isVisible()){
        emit sigShowSelf();
    }

    //已经在回放中的文件不处理
    if(m_rownum == rownum){
        QMessageBox::critical(this, tr("Info"), tr("The file is being replayed, please do not repeat the operation!") + QString::number(rownum));
        return;
    }

    QFile file(filename);
    if(!file.open(QIODevice::ReadOnly)){
        QMessageBox::critical(this, tr("Error"), tr("Fail to open file") + ":" + file.errorString());
        return;
    }
    file.close();

    MapParmSt &sMapParmst = CProtoParamData::getMapParmSt();
    QtConcurrent::run([=](){
        QFile file(filename);
        file.open(QIODevice::ReadOnly);
        file.readLine();
        QString readarr;
        qint64 linenum = 0;
        qint64 filesize = 0l;
        qint64 readsize = 0l;
        float currentprocess = 0.0;
        filesize = file.size();
        readarr = file.readLine();
        //qDebug()<<"fileline:"<<readarr;
        readsize += readarr.size();
        do{
            if(!m_bPlaystat){
                QThread::usleep(100000);
                continue;
            }
            linenum++;
            if(readarr.size() == 0){
                break;
            }
            readarr.remove("\n");
            readarr.remove("\"");
            readarr.remove(":");
            auto keysread = readarr.split("\",\"");
            if(keysread.size() < 2){
                keysread = readarr.split(',');
            }
            //qDebug()<<"keysread"<<keysread.size();
            readarr = file.readLine();
            readsize += readarr.size();
            if(linenum == 1 || linenum % sMapParmst.m_iInterPoint == 0 || readarr.size() == 0){
                currentprocess = readsize * 100.0 / filesize ;
                emit sigLngLatinfo(rownum, keysread.at(9).toDouble(), keysread.at(10).toDouble(), currentprocess);
                if(m_fFastProcess < currentprocess){
                    QThread::usleep(sMapParmst.m_iInterTime * 1000);
                }
            }
        }while(readarr.size() != 0);
        //emit sigLngLatinfo(rownum, 116.397571,39.906931, 99.9);
        emit sigLngLatinfo(rownum, 0.0,0.0, 100.0);
        file.close();
    });

    //QMessageBox::critical(this, QString::fromLocal8Bit("轨迹回放"), res.result());
}

void CCustomerViewPage::slotLngLatinfo(int rownum, double lng, double lat, float process){
    //qDebug()<<"process:"<<process;

    if(process < 100.00){
        cmapvpg->showmapview(rownum, lng, lat, process);
        ui->le_longitude->setText(QString::number(lng, 'f', 8));
        ui->le_latitude->setText(QString::number(lat, 'f', 8));
    }else{
        QMessageBox::information(this, tr("Info"), tr("Track Record") + QString("%1").arg(rownum) + tr("Replay completed"));
        return;
    }
}



void CCustomerViewPage::on_cb_showportn_currentTextChanged(const QString &arg1)
{
    m_sPortN = arg1;
}

void CCustomerViewPage::slotAttituShow(const QVector<double> attituValues, const QString portn){
    if(!m_sPortN.isEmpty() && m_sPortN != portn){
        return;
    }
    //合成速度
    double synSpeed = qSqrt(qPow(attituValues.at(4), 2) + qPow(attituValues.at(5), 2));
    double Speedval = qRound(3.6 * synSpeed * 100) / 100;//换算为km/h

    //在仪表盘显示数据
    if(m_iViewMode == 0){
        cplatevpg->showplateview(attituValues.at(1), attituValues.at(2), attituValues.at(0), Speedval, attituValues.at(3));
    }else{
        cflypg->showplateview(attituValues.at(1), attituValues.at(2), attituValues.at(0), Speedval, attituValues.at(3));
    }
}
