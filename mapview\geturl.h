﻿#ifndef GETURL_H
#define GETURL_H

#include "mapStruct.h"
#include <qfuture.h>
#include <qset.h>
#include <QObject>

const QString g_sGDRoadMap = "http://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}";
const QString g_sGDVectorMap = "http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}";
const QString g_sGDSateMap = "http://wprd01.is.autonavi.com/appmaptile?&style=6&lang=zh_cn&scl=1&ltype=0&x={x}&y={y}&z={z}";

const QString g_sGGRoadMap = "http://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}";
const QString g_sGGVectorMap = "http://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}";
//const QString g_sVectorMap = "http://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
const QString g_sGGSateMap = "http://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}";

/*
* 地图页面公共通信接口
*/

class GetUrlInterface : public QObject
{
    Q_OBJECT
public:
    static GetUrlInterface* getInterface()
    {
        static GetUrlInterface tinterface;
        return &tinterface;
    }

signals:
    void update(ImageInfo info);             // 传出下载的瓦片图信息
    void updateTitle(int x, int y, int z);   // 传出下载的瓦片编号

    void showRect(QRect rect);   // 设置显示像素范围
    void setLevel(int level);    // 设置瓦片层级
    void showPos(QPointF, QPointF);   // 显示地址
    void showLine(QPointF pf);
    void urlChange(int urltype);
    void setprocess(float p);
    void setplaystat(bool);
};

/*
* 获取地图数据类
*/
class GetUrl : public QObject
{
    Q_OBJECT
public:
    explicit GetUrl(QObject* parent = nullptr);
    ~GetUrl();

    void urlinit(int urltype, int layer);
    void setUrl(QString url, int urltype);   // 设置获取瓦片地图的源地址
    void getImg(QRect rect, int level);
    void showRect(QRect rect);
    void setLevel(int level);   // 设置瓦片层级

    void urlChange(int urltype);
    static QString getCacheDir();

private:
    void getTitle(QRect rect, int level);    // 获取所有需要下载的瓦片地图编号
    void getUrl();                           // 获取用于请求瓦片地图的信息
    void clear();                            // 清空内容
    void quit();                             // 退出下载
    void updateTitle(int x, int y, int z);   // 传出下载的瓦片编号

private:
    QThread* m_thread = nullptr;
    QFuture<void> m_future;
    QRect m_rect;      // 显示瓦片地图像素范围
    int m_level = 14;   // 瓦片地图层级
    QString m_url;
    QSet<quint64> m_exist;        // 已经存在的瓦片地图编号
    QVector<ImageInfo> m_infos;   // 需要下载的瓦片地图信息
    int m_urltype;
    int m_urllayer;
    QPointF m_llcentersit;   //经纬度中心
    QPoint m_pixcentersit;   //像素中心
    QPoint m_tilelcentersit; //瓦片地图中心
    static QString m_scachedir;
    QString m_sVectorMap;
    QString m_sSateMap;
    QString m_sRoadMap;
};

#endif   // GETURL_H
