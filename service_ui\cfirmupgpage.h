﻿#ifndef CFIRMUPGPAGE_H
#define CFIRMUPGPAGE_H

#include <QWidget>
#include "ccserialpage.h"
#include "csseriservice.h"
#include "QXlsx.h"

namespace Ui {
class CFirmUpgPage;
}

/*
*  固件参数升级配置页面
*/
class CFirmUpgPage : public QWidget
{
    Q_OBJECT

public:
    explicit CFirmUpgPage(QWidget *parent = nullptr);
    ~CFirmUpgPage();
    void setSeriPage(CcserialPage *seripage);
    void parseCalibrationFile(QString, QVector<double> &);
    QByteArray parseNormalTemp(QXlsx::Document &xlsx, QString sheetname);
    QByteArray parseAllTemp(QXlsx::Document &xlsx, QString sheetname, int type);
    QByteArray parseNeuralNet(QXlsx::Document &xlsx, QString sheetname, int type);
    void parseWarmsuppleFile(QString, QVector<QByteArray> &vParseByte);
    void parseFilterRmatrixFile(QString, QVector<double> &);
    void parseFilterQmatrixFile(QString, QVector<double> &);
    void parseFilterFactorFile(QString, QVector<double> &);
    void siteChangeo2n(int old, int &newx, int &newy);
    void siteChangen2o(int newx, int newy, int& old);
    void onlyCmdInform(QByteArray cmdbyte);
    bool parmsPortConnect();
    void parmsPortDisconnect();
    QString getNameByCmd(QByteArray &cmd);
    void setButtonStatus(bool stat);
    bool permitPwdCheck(int id);

private slots:
    void on_bt_clear_clicked();

    void on_bt_openfile_clicked();

    void on_bt_startupdate_clicked();

    void slotEndUpdate(int status, QString result);

    void on_bt_stopupdate_clicked();

    void on_bt_getfirmvir_clicked();

    void on_tb_isHide_clicked();

    void on_bt_Issu_clicked();

    void slotParmsEnd(int status, QByteArray cmd, QByteArray bytearry);

    void on_bt_Issuall_clicked();

    void on_bt_ParmRead_clicked();

    void on_bt_FixParm_clicked();

    void on_bt_defaultparm_clicked();

    void on_bt_backview_clicked();

    void on_comboBox_currentIndexChanged(int index);

    void on_bt_warmsupple_clicked();

    void on_bt_calibration_clicked();

signals:
    void sigStartUpdate(QString filename);
    void sigParmsUpdate(QByteArray cmd, QByteArray parms, bool);
    void sigMultilParmsUpdate(QByteArray cmd, QVector<QByteArray> vparms, bool);
    void sigVersionQuery(bool);
    void sigStopUpdate();
    void sigResetUpdate();
private:
    Ui::CFirmUpgPage *ui;
    QString m_sWorkFile;
    CcserialPage *m_oseriPage;
    bool m_bIsUpdating;
    QPointer<CSseriService> m_sSeriser;
    bool m_bParmScroHide;
    QPixmap m_pixleft;
    QPixmap m_pixright;
    QVector<double> m_vMatrixparms;
    QVector<QByteArray> m_vByteParms;
    QVector<QString> m_parmNames;

};

#endif // CFIRMUPGPAGE_H
