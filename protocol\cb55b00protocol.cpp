﻿#include "cb55b00protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cdataanapage.h"
#include "cgyrocalibpage.h"
#include "ctlhtools.h"
#include "cprotocolfactory.h"
#include <QDebug>

Cb55b00Protocol::Cb55b00Protocol(QObject *parent) : CBaseProtocol(parent)
{
    qRegisterMetaType<QVector<double>>("QVector<double>");
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));
    CGyroCalibPage *gyropage = static_cast<CGyroCalibPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_GyroCalib"));
    if(dripage != NULL){
        qDebug()<<"connect Cb55b00Protocol";
        connect(this, &Cb55b00Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &Cb55b00Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
        connect(this, &Cb55b00Protocol::sigChartsUpdate, danapage, &CDataAnaPage::slotDataShow);
        connect(this, &Cb55b00Protocol::sigFreqUpdate, gyropage, &CGyroCalibPage::slotDataShow);
        connect(this, &Cb55b00Protocol::sigDestory, gyropage, &CGyroCalibPage::slotDestory);
    }

    m_icatFreqCounts = 0;
    m_lcatCurrCounts = 0l;
    m_davggyrx = 0.0;
    m_davggyry = 0.0;
    m_davggyrz = 0.0;
    m_davgcalix = 0.0;
    m_davgcaliy = 0.0;
    m_davgcaliz = 0.0;
    m_davgtempf = 0.0;
    m_davgtempu = 0.0;
}

bool Cb55b00Protocol::setProtoLength(const QByteArray &barr){
    m_slProtoKeys =  CProtocolFactory::getProKeys(m_iProtoIndex);
    m_slFreqKeys.append(m_slProtoKeys.mid(8, 8));
    m_bMsgHead = barr.mid(0, 2);
    m_uMsgLen = sizeof (Strub55b00);

    return true;
}

Cb55b00Protocol::~Cb55b00Protocol(){
    qDebug()<<"~CA5A500Protocol";
}

void Cb55b00Protocol::paseMsg(const QByteArray msg){
    Strub55b00 st_b55b00;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < 10){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    //qDebug()<<"CA5A500Protocol1111"<<sizeof (st_a5a500);
    //数据是大端
    QByteArray tempmsg = msg;
    reserverData(tempmsg, 2, 2);
    reserverData(tempmsg, 4, 2);
    reserverData(tempmsg, 6, 4);
    reserverData(tempmsg, 10, 2);
    reserverData(tempmsg, 12, 2);
    reserverData(tempmsg, 14, 2);
    reserverData(tempmsg, 16, 2);
    reserverData(tempmsg, 18, 2);

    ::memcpy(&st_b55b00, tempmsg.data(), m_uMsgLen);

    //和校验
    if(!sum8CheckSum(msg, 2, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sum8CheckSum error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }
    //qDebug()<<"CA5A500Protocol1113"<<st_a5a500.temp<<st_a5a500.flog;

    double fogx = 0.0;
    double fogy = 0.0;
    double fogz = 0.0;
    double calix = 0.0;
    double caliy = 0.0;
    double caliz = 0.0;
    QMap<QString, QString> divisor;
    CProtoParamData::getProtocolDivisor(m_iProtoIndex, divisor);

    //qDebug()<<"divisor"<<divisor;

    if(divisor.contains(u8"陀螺x:")){
        fogx = CTlhTools::calDivisor(st_b55b00.fogx, divisor[u8"陀螺x:"]);
    }else{
        //fogx = st_b55b00.fogx/80.00;
        fogx = st_b55b00.fogx;
    }

    if(divisor.contains(u8"陀螺y:")){
        fogy = CTlhTools::calDivisor(st_b55b00.fogy, divisor[u8"陀螺y:"]);
    }else{
        //fogy = st_b55b00.fogy/80.00;
        fogy = st_b55b00.fogy;
    }

    if(divisor.contains(u8"陀螺z:")){
        fogz = CTlhTools::calDivisor(st_b55b00.fogz, divisor[u8"陀螺z:"]);
    }else{
        //fogz = st_b55b00.fogz/669450.00;
        fogz = st_b55b00.fogz;
    }

    if(divisor.contains(u8"加计x:")){
        calix = CTlhTools::calDivisor(st_b55b00.accx, divisor[u8"加计x:"]);
    }else{
        //calix = st_b55b00.accx/4905.00;
        calix = st_b55b00.accx;
    }

    if(divisor.contains(u8"加计y:")){
        caliy = CTlhTools::calDivisor(st_b55b00.accy, divisor[u8"加计y:"]);
    }else{
        //calix = st_b55b00.accy/4905.00;
        caliy = st_b55b00.accy;
    }

    if(divisor.contains(u8"加计z:")){
        caliz = CTlhTools::calDivisor(st_b55b00.accz, divisor[u8"加计z:"]);
    }else{
        //caliz = st_b55b00.accz/4905.00;
        caliz = st_b55b00.accz;
    }

    dataValues.append(QString::number(fogx, 'f', 6));
    dataValues.append(QString::number(fogy, 'f', 6));
    dataValues.append(QString::number(fogz, 'f', 6));
    dataValues.append(QString::number(calix, 'f', 6));
    dataValues.append(QString::number(caliy, 'f', 6));
    dataValues.append(QString::number(caliz, 'f', 6));

    //dataValues.append(QString::number(st_b55b00.fogx/80.00, 'f', 8));
    //dataValues.append(QString::number(st_b55b00.fogy/80.00, 'f', 8));
    //dataValues.append(QString::number(st_b55b00.fogz/669450.00, 'f', 8));
    //dataValues.append(QString::number(st_b55b00.accx/4905.00, 'f', 8));
    //dataValues.append(QString::number(st_b55b00.accy/4905.00, 'f', 8));
    //dataValues.append(QString::number(st_b55b00.accz/4905.00, 'f', 8));
    float temp = 0;
    float flogtemp = 0.0;

    if(st_b55b00.ftemp >> 15){
        temp = -1 * complement2original(st_b55b00.ftemp & 0x0000FFFF);
    }else{
        temp = st_b55b00.ftemp;
    }

    if(divisor.contains(u8"陀螺温度:")){
        flogtemp = CTlhTools::calDivisor(temp, divisor[u8"陀螺温度:"]);
    }else{
        //flogtemp = temp/16.00;
        flogtemp = temp;
    }

    float acceltemp = 0.0;
    if(st_b55b00.utemp >> 15){
        temp = -1 * complement2original(st_b55b00.utemp & 0x0000FFFF);
    }else{
        temp = st_b55b00.utemp;
    }

    if(divisor.contains(u8"加计温度:")){
        acceltemp = CTlhTools::calDivisor(temp, divisor[u8"加计温度:"]);
    }else{
        acceltemp = temp;
    }

    //qDebug()<<"temp1:"<<st_a5a500.temp<<temp;
    dataValues.append(QString::number(flogtemp, 'f', 2));
    dataValues.append(QString::number(acceltemp, 'f', 2));
    QStringList orikeys;
    orikeys.append(m_slProtoKeys.mid(0, 8));
    writeCvsFile("B55B", orikeys, dataValues);

    //生成频率采样文件
    //qDebug()<<"m_icatFreqCounts:"<<m_lcatCurrCounts<<m_icatFreqCounts;
    QStringList slwriteval;
    if(m_icatFreqCounts != 0 && (++m_lcatCurrCounts % m_icatFreqCounts == 0) ){
        slwriteval.clear();
        m_vGyrXDatas.append(fogx);
        m_vGyrYDatas.append(fogy);
        m_vGyrZDatas.append(fogz);
        m_vCaliXDatas.append(calix);
        m_vCaliYDatas.append(caliy);
        m_vCaliZDatas.append(caliz);
        m_vTempFDatas.append(flogtemp);
        m_vTempUDatas.append(acceltemp);
        m_davggyrx = CTlhTools::getAverage(m_vGyrXDatas);
        m_davggyry = CTlhTools::getAverage(m_vGyrYDatas);
        m_davggyrz = CTlhTools::getAverage(m_vGyrZDatas);
        m_davgcalix = CTlhTools::getAverage(m_vCaliXDatas);
        m_davgcaliy = CTlhTools::getAverage(m_vCaliYDatas);
        m_davgcaliz = CTlhTools::getAverage(m_vCaliZDatas);
        m_davgtempf = CTlhTools::getAverage(m_vTempFDatas);
        m_davgtempu = CTlhTools::getAverage(m_vTempUDatas);
        slwriteval <<QString::number(m_davggyrx, 'f', 6) <<QString::number(m_davggyry, 'f', 6) <<QString::number(m_davggyrz, 'f', 6)  \
                   <<QString::number(m_davgcalix, 'f', 6) <<QString::number(m_davgcaliy, 'f', 6) <<QString::number(m_davgcaliz, 'f', 6) \
                   <<QString::number(m_davgtempf, 'f', 2) <<QString::number(m_davgtempu, 'f', 2);
        QVector<double> vddatas;
        vddatas.append(m_davggyrx);
        vddatas.append(m_davggyry);
        vddatas.append(m_davggyrz);
        vddatas.append(m_davgcalix);
        vddatas.append(m_davgcaliy);
        vddatas.append(m_davgcaliz);
        vddatas.append(m_davgtempf);
        vddatas.append(m_davgtempu);
        //qDebug()<<slwriteval;
        writeCvsFile("B55B_C" + QString::number(m_icatFreqCounts), m_slFreqKeys, slwriteval, 1);
        emit sigFreqUpdate(m_slFreqKeys, vddatas, m_sPortN, m_iProtoIndex);
        m_vGyrXDatas.clear();
        m_vGyrYDatas.clear();
        m_vGyrZDatas.clear();
        m_vCaliXDatas.clear();
        m_vCaliYDatas.clear();
        m_vCaliZDatas.clear();
        m_vTempFDatas.clear();
        m_vTempUDatas.clear();
    }else{
        m_vGyrXDatas.append(fogx);
        m_vGyrYDatas.append(fogy);
        m_vGyrZDatas.append(fogz);
        m_vCaliXDatas.append(calix);
        m_vCaliYDatas.append(caliy);
        m_vCaliZDatas.append(caliz);
        m_vTempFDatas.append(flogtemp);
        m_vTempUDatas.append(acceltemp);
    }

    dataValues.append(QString::number(m_davggyrx, 'f', 4));
    dataValues.append(QString::number(m_davggyry, 'f', 4));
    dataValues.append(QString::number(m_davggyrz, 'f', 4));
    dataValues.append(QString::number(m_davgcalix, 'f', 4));
    dataValues.append(QString::number(m_davgcaliy, 'f', 4));
    dataValues.append(QString::number(m_davgcaliz, 'f', 4));
    dataValues.append(QString::number(m_davgtempf, 'f', 2));
    dataValues.append(QString::number(m_davgtempu, 'f', 2));

    if(m_bIsNeedShow){
        //qDebug()<<"datashow:"<<m_slProtoKeys<<dataValues;
        if(m_slProtoKeys.size() != dataValues.size()){
            return;
        }
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }else{

    }
    m_bIsNeedKeys = false;

    //绘图数据
    QVector<double> vddata;
    //vddata.append(st_a5a500.pgidex);
    vddata.append(fogx);
    vddata.append(fogy);
    vddata.append(fogz);
    vddata.append(calix);
    vddata.append(calix);
    vddata.append(calix);
    vddata.append(flogtemp);
    vddata.append(acceltemp);
    vddata.append(m_davggyrx);
    vddata.append(m_davggyry);
    vddata.append(m_davggyrz);
    vddata.append(m_davgcalix);
    vddata.append(m_davgcaliy);
    vddata.append(m_davgcaliz);
    vddata.append(m_davgtempf);
    vddata.append(m_davgtempu);

    emit sigChartsUpdate(m_slProtoKeys, vddata, m_sPortN, m_iProtoIndex);

}

bool Cb55b00Protocol::preInit(){
    CBaseProtocol::preInit();
    if(m_dCatFreq == 0){
        m_icatFreqCounts = 0;
        return true;
    }
    m_icatFreqCounts = m_dGyrFreq / m_dCatFreq;
    qDebug()<<"m_catFreqCounts"<<m_icatFreqCounts<<m_dCatFreq;
    return true;
}

