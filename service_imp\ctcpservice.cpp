#include "ctcpservice.h"
#include <QDebug>
#include <QHostAddress>
#include <QCoreApplication>
#include <QTimer>

#define SERI_TIMEOUT 100

bool CTcpService::m_isReadSD = false;

CTcpService::CTcpService(QObject *parent) : ICommService(parent)
{
    m_sServerIP = "***************";
    m_sServerPort = "8080";
    m_IsOpen = false;

    m_workthread = new QThread;
    m_workthread->start();

    m_resolvTask = new CResolvingTask;
    m_resolvTask->start();
    m_bIsWorking = false;
    m_timeStatus = 0;      //0-等待状态，1-工作状态

    moveToThread(m_workthread);

    m_tcpServer = new QTcpServer;
    m_currentClient = nullptr;
    m_checkSize = 0;
    m_TcpBuff.clear();
    m_tcpServer->moveToThread(m_workthread);

    m_bIsUpdating = false;
    m_TimerId = -1;
    m_bEnd.append(4, 0x0f);

    qDebug()<<"CTcpService:"<<QThread::currentThreadId()<<this<<m_resolvTask;
}

CTcpService::~CTcpService()
{
    qDebug()<<"~CTcpService:"<<this;
    if(m_tcpServer && m_tcpServer->isListening()){
        m_tcpServer->close();
    }
    
    // 清理所有客户端连接
    for(auto client : m_clientList){
        if(client){
            client->disconnectFromHost();
            client->deleteLater();
        }
    }
    m_clientList.clear();
    
    if(m_resolvTask){
        m_resolvTask->deleteLater();
    }
    
    if(m_workthread){
        m_workthread->quit();
        m_workthread->wait();
        m_workthread->deleteLater();
    }
}

bool CTcpService::CommInit(const QString &serverIP, const QString &serverPort, const QString &config3,
                          const QString &config4, const QString &config5, const QString &config6, 
                          CProtoParamData *parm)
{
    qDebug()<<"TCP CommInit:"<<QThread::currentThreadId();
    m_sServerIP = serverIP;
    m_sServerPort = serverPort;
    m_pparm = parm;

    if(!m_resolvTask){
        m_resolvTask = new CResolvingTask;
        m_resolvTask->start();
    }

    connect(this, &CTcpService::sigDataRead, m_resolvTask, &CResolvingTask::slotAppendData);
    connect(parm, &CProtoParamData::sigParamChange, m_resolvTask, &CResolvingTask::slogParamChange);
    
    m_icatRound = parm->getTimeRound();
    m_iTimeOut = parm->getTimeOut();
    m_iIntvTimes = parm->getIntvTimes();

    // 设置TCP服务器
    connect(m_tcpServer, &QTcpServer::newConnection, this, &CTcpService::slotNewConnection);

    return true;
}

bool CTcpService::startTcpServer()
{
    if(m_tcpServer->isListening()){
        return true;
    }

    QHostAddress address(m_sServerIP);
    quint16 port = m_sServerPort.toUShort();
    
    if(!m_tcpServer->listen(address, port)){
        qDebug()<<"TCP server start fail:"<<m_tcpServer->errorString();
        return false;
    }
    
    qDebug()<<"TCP server started on"<<m_sServerIP<<":"<<m_sServerPort;
    return true;
}

void CTcpService::stopTcpServer()
{
    if(m_tcpServer->isListening()){
        m_tcpServer->close();
        qDebug()<<"TCP server stopped";
    }
    
    // 断开所有客户端
    for(auto client : m_clientList){
        if(client){
            client->disconnectFromHost();
        }
    }
}

void CTcpService::slotNewConnection()
{
    while(m_tcpServer->hasPendingConnections()){
        QTcpSocket *client = m_tcpServer->nextPendingConnection();
        m_clientList.append(client);
        
        // 如果是第一个客户端，设为当前客户端
        if(!m_currentClient){
            m_currentClient = client;
        }
        
        connect(client, &QTcpSocket::readyRead, this, &CTcpService::commReadData);
        connect(client, &QTcpSocket::disconnected, this, &CTcpService::slotClientDisconnected);
        connect(client, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                this, &CTcpService::slotTcpError);
        
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        qDebug()<<"New client connected:"<<clientInfo;
        emit sigClientConnected(clientInfo);
    }
}

void CTcpService::slotClientDisconnected()
{
    QTcpSocket *client = qobject_cast<QTcpSocket*>(sender());
    if(client){
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        qDebug()<<"Client disconnected:"<<clientInfo;
        
        m_clientList.removeAll(client);
        
        if(m_currentClient == client){
            m_currentClient = m_clientList.isEmpty() ? nullptr : m_clientList.first();
        }
        
        client->deleteLater();
        emit sigClientDisconnected(clientInfo);
    }
}

void CTcpService::slotTcpError(QAbstractSocket::SocketError error)
{
    QTcpSocket *client = qobject_cast<QTcpSocket*>(sender());
    if(client){
        QString errorString = client->errorString();
        qDebug()<<"TCP error:"<<error<<errorString;
        emit sigTcpError(errorString);
    }
}

void CTcpService::slotCommOpenOrClose(const QString config, const int optype, const QString fileprefix)
{
    if(optype == 0 && !m_bIsWorking){
        m_resolvTask->clearBuff();
        m_resolvTask->setPortN("TCP_" + m_sServerIP + ":" + m_sServerPort);
        m_resolvTask->setFilePrefix(fileprefix);
        
        if(!startTcpServer()){
            qDebug()<<"TCP server start fail:"<<QThread::currentThreadId();
        }else{
            qDebug()<<"TCP server started"<<QThread::currentThreadId();
            m_bIsWorking = true;
        }
    }else{
        if(m_bIsWorking){
            qDebug()<<"TCP server stop"<<QThread::currentThreadId();
            stopTcpServer();
            m_bIsWorking = false;
        }
    }

    if(m_iTimeOut > 0){
        startTimeWork();
    }
}

void CTcpService::commReadData()
{
    QTcpSocket *client = qobject_cast<QTcpSocket*>(sender());
    if(!client){
        return;
    }
    
    QByteArray data = client->readAll();
    if(!data.isEmpty()){
        m_TcpBuff.append(data);
        emit sigDataRead(data);
        qDebug()<<"TCP received data size:"<<data.size();
    }
}

bool CTcpService::commWrite(QByteArray arr)
{
    if(!m_currentClient || m_currentClient->state() != QAbstractSocket::ConnectedState){
        qDebug()<<"No connected client for writing";
        return false;
    }
    
    qint64 reslen = m_currentClient->write(arr);
    if(reslen == arr.size()){
        return true;
    }else{
        qDebug()<<"write TCP fail:"<<reslen;
        return false;
    }
}

void CTcpService::slotCommWrite(const char * arr, int &len)
{
    if(!m_currentClient || m_currentClient->state() != QAbstractSocket::ConnectedState){
        qDebug()<<"No connected client for writing";
        len = 0;
        return;
    }
    
    qint64 reslen = 0;
    //写TCP前先清理一下临时接收buff
    m_TcpBuff.clear();
    reslen = m_currentClient->write(arr, len);
    if(reslen != len){
        qDebug()<<"write TCP fail:"<<reslen;
    }
    len = reslen;
}

void CTcpService::editWrite(QByteArray arr)
{
    emit sigDataRead(arr);
}

bool CTcpService::isWorking()
{
    return m_bIsWorking && m_tcpServer->isListening();
}

QByteArray CTcpService::getCurrentBuff()
{
    QByteArray backdata = m_TcpBuff;
    m_TcpBuff.clear();
    return backdata;
}

void CTcpService::clearBuff()
{
    m_TcpBuff.clear();
}

void CTcpService::commWriteData()
{
    // TCP版本的写数据实现
    if(!m_currentClient){
        qDebug()<<"No current client for TCP write";
        return;
    }
    // 具体实现根据需要添加
}

void CTcpService::commClose()
{
    stopTcpServer();
    m_bIsWorking = false;
}

void CTcpService::startTimeWork()
{
    if(m_TimerId != -1){
        killTimer(m_TimerId);
    }
    if(m_iTimeOut > 0){
        m_TimerId = startTimer(m_iTimeOut);
    }
}

void CTcpService::timerEvent(QTimerEvent *event)
{
    if(event->timerId() == m_TimerId){
        // 定时器处理逻辑
        if(m_timeStatus == 0 && m_bIsWorking){
            // 处理定时任务
        }
    }
}

bool CTcpService::startWork()
{
    return startTcpServer();
}

void CTcpService::stopWork()
{
    stopTcpServer();
}

void CTcpService::setUpdateMode(bool mode)
{
    m_bIsUpdating = mode;
}

QByteArray CTcpService::doParmsUpdate(QByteArray cmd, QByteArray parms, bool isolddev)
{
    // TCP版本的参数更新实现
    // 这里需要根据具体的协议实现
    QByteArray result;
    
    if(!m_currentClient || m_currentClient->state() != QAbstractSocket::ConnectedState){
        qDebug()<<"No connected client for parameter update";
        return result;
    }
    
    // 构建更新命令
    QByteArray updateData = cmd + parms;
    
    // 发送数据
    if(commWrite(updateData)){
        // 等待响应
        QTimer timer;
        timer.setSingleShot(true);
        timer.start(SERI_TIMEOUT);
        
        while(timer.isActive() && result.isEmpty()){
            QCoreApplication::processEvents();
            result = getCurrentBuff();
        }
    }
    
    return result;
}

void CTcpService::slotStartUpdate(QString filename)
{
    // TCP版本的升级实现
    qDebug()<<"TCP start update with file:"<<filename;
    // 具体实现根据需要添加
}

QStringList CTcpService::getConnectedClients()
{
    QStringList clients;
    for(auto client : m_clientList){
        if(client && client->state() == QAbstractSocket::ConnectedState){
            QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
            clients.append(clientInfo);
        }
    }
    return clients;
}
