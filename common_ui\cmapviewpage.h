﻿#ifndef CMAPVIEWPAGE_H
#define CMAPVIEWPAGE_H

#include <QWidget>
#include "geturl.h"
#include "mapgraphicsview.h"

namespace Ui {
class CMapViewPage;
}

/*
*地图视图主页面
*/
class CMapViewPage : public QWidget
{
    Q_OBJECT

public:
    explicit CMapViewPage(QWidget *parent = nullptr);
    ~CMapViewPage();
    void showmapview(int id, double lat, double lng, float process);
    void resizeEvent(QResizeEvent *event);
    void initMap();

private:
    Ui::CMapViewPage *ui;
    QString m_sRoadMap;
    QString m_sVectorMap;
    QString m_sSateMap;
    GetUrl* m_geturl = nullptr;
    GetUrl* m_getoverurl = nullptr;
    MapGraphicsView *m_mapview;
};

#endif // CMAPVIEWPAGE_H
