﻿#include "cdrivetestpage.h"
#include "ui_cdrivetestpage.h"
#include <QTextEdit>
#include <QDockWidget>
#include <QPlainTextEdit>
#include <QLabel>
#include <QDebug>

CDriveTestPage::CDriveTestPage(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CDriveTestPage)
{
    ui->setupUi(this);
    /*
    QDockWidget *dWid1 = new QDockWidget(QString::fromLocal8Bit("停靠窗口1"), this);
    m_LDockWid.append(dWid1);
    dWid1->setFeatures(QDockWidget::DockWidgetMovable);
    //dWid1->setAllowedAreas(Qt::LeftDockWidgetArea|Qt::RightDockWidgetArea);
    //dWid1->resize(width()/2, height());

    QListWidget *lwid1 = new QListWidget();
    m_lwdMap[0] = dWid1;
    //lwid1->setViewMode(QListWidget::IconMode); // 设置视图模式为IconMode
    lwid1->setFlow(QListView::LeftToRight); // 设置水平流布局
    lwid1->setWrapping(true); // 允许自动换行
    lwid1->setResizeMode(QListView::Adjust); // 调整项大小以适应窗口

    dWid1->setWidget(lwid1);

    QDockWidget *dWid2 = new QDockWidget(QString::fromLocal8Bit("停靠窗口2"), this);
    m_LDockWid.append(dWid2);
    dWid2->setFeatures(QDockWidget::DockWidgetMovable);
    //dWid2->resize(width()/2, height());
    QListWidget *lwid2 = new QListWidget();
    m_LListWid.append(lwid2);

    //lwid2->setViewMode(QListWidget::IconMode); // 设置视图模式为IconMode
    lwid2->setFlow(QListView::LeftToRight); // 设置水平流布局
    lwid2->setWrapping(true); // 允许自动换行
    lwid2->setResizeMode(QListView::Adjust); // 调整项大小以适应窗口
    dWid2->setWidget(lwid2);
     */
    /*QLabel *lab1 = new QLabel();
    //QString tedit1;
    tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    //tedit2->append("111111111111111111\n11111111111111111\n111111111111111111111\n1111111111");
    //tedit2->append("new line");
    //tedit1->adjustSize();
    QFont font2 = tedit2->font();
    font.setPointSize(fontsize);
    tedit2->setFont(font);
    QFontMetrics metrics2(font);
    QSize size2 = metrics2.size(Qt::TextDontClip, tedit2->toPlainText());
    qDebug()<<"tedit1->height():"<<size2.height()<<size3.height()<<size4.height();
    dWid2->setMinimumHeight(size2.height() + 36);


    QDockWidget *dWid3 = new QDockWidget(QString::fromLocal8Bit("停靠窗口3"), this);
    m_LDockWid.append(dWid3);
    dWid3->setFeatures(QDockWidget::DockWidgetMovable);
    //dWid3->setMinimumHeight(height()/2);

    QListWidget *lwid3 = new QListWidget();
    m_LListWid.append(lwid3);
    //lwid3->setViewMode(QListWidget::IconMode); // 设置视图模式为IconMode
    lwid3->setFlow(QListView::LeftToRight); // 设置水平流布局
    lwid3->setWrapping(true); // 允许自动换行
    lwid3->setResizeMode(QListView::Adjust); // 调整项大小以适应窗口
    dWid3->setWidget(lwid3);
    /*
    QDockWidget *dWid4 = new QDockWidget(QString::fromLocal8Bit("停靠窗口4"), this);
    dWid4->setFeatures(QDockWidget::DockWidgetMovable);

    QTextEdit *tedit4 = new QTextEdit();
    dWid4->setWidget(tedit4);

    QDockWidget *dWid5 = new QDockWidget(QString::fromLocal8Bit("停靠窗口5"), this);
    dWid5->setFeatures(QDockWidget::DockWidgetMovable);
    dWid5->setMinimumHeight(height()/2);

    QTextEdit *tedit5 = new QTextEdit();
    dWid5->setWidget(tedit5);

    QDockWidget *dWid6 = new QDockWidget(QString::fromLocal8Bit("停靠窗口6"), this);
    dWid6->setFeatures(QDockWidget::DockWidgetMovable);

    QTextEdit *tedit6 = new QTextEdit();
    dWid6->setWidget(tedit6);

    addDockWidget(Qt::TopDockWidgetArea, dWid1);
    addDockWidget(Qt::TopDockWidgetArea, dWid2);
    addDockWidget(Qt::TopDockWidgetArea, dWid3);
    */
    //addDockWidget(Qt::BottomDockWidgetArea, dWid4);
    //addDockWidget(Qt::BottomDockWidgetArea, dWid5);
    //addDockWidget(Qt::BottomDockWidgetArea, dWid6);
    //addDockWidget(Qt::LeftDockWidgetArea, dWid5);
    //addDockWidget(Qt::RightDockWidgetArea, dWid3);
    //addDockWidget(Qt::BottomDockWidgetArea, dWid4);
    //addDockWidget(Qt::BottomDockWidgetArea, dWid6);

}

CDriveTestPage::~CDriveTestPage()
{
    delete ui;
}

void CDriveTestPage::slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex){
    QListWidget *lwid = NULL;
    QDockWidget *dwid = NULL;
    QFont font;
    font.setPixelSize(16);
    if(m_dwdMap.contains(sportN)){
        dwid = m_dwdMap[sportN];
        lwid = m_lwdMap[sportN];
    }

    if(dataValues.size() == 0){
        qDebug()<<"dataValues.size() == 0";
        if(dwid != NULL){
            removeDockWidget(dwid);
            delete lwid;
            m_dwdMap.remove(sportN);
            delete dwid;
            m_lwdMap.remove(sportN);
        }
        return;
    }

    if(dwid == NULL){
        dwid = new QDockWidget(sportN, this);
        m_dwdMap[sportN] = dwid;
        dwid->setFeatures(QDockWidget::DockWidgetMovable);
        lwid = new QListWidget;
        m_lwdMap[sportN] = lwid;
        lwid->setFlow(QListView::LeftToRight); // 设置水平流布局
        lwid->setWrapping(true); // 允许自动换行
        lwid->setResizeMode(QListView::Adjust); // 调整项大小以适应窗口
        lwid->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        dwid->setWidget(lwid);
        addDockWidget(Qt::TopDockWidgetArea, dwid);
        for(int i = 0; i < datakeys.size(); i++){
            QListWidgetItem *item = new QListWidgetItem();
            item->setFont(font);
            lwid->insertItem(i, item);
            lwid->adjustSize();
        }
        QFontMetrics metrics2(font);
        QSize size2 = metrics2.size(Qt::TextDontClip, lwid->item(0)->text());
        qDebug()<<size2.height()<<size2.height() * datakeys.size()<<height();
        //dwid->setMinimumHeight((size2.height() + 9) * datakeys.size());
        dwid->setMinimumHeight(height());

        QList<QDockWidget *> dwlist = m_dwdMap.values();
        for(QDockWidget *w:dwlist){
            w->setMinimumWidth(width()/dwlist.size());
        }
    }


    for(int i = 0; i < datakeys.size(); i++){
       QListWidgetItem *item = lwid->item(i);
       QString newstr = QString(datakeys.at(i) + ": " + dataValues.at(i)).leftJustified(lwid->width()/2, ' ');
       //qDebug()<<newstr<<m_LListWid.at(0)->width();
       item->setText(newstr);
    }

    //qDebug()<<"slotDataShow:"<<datakeys<<QThread::currentThreadId();
    //qDebug()<<"slotDataShow:"<<datakeys.size();

    repaint();
}

void CDriveTestPage::showEvent(QShowEvent *event){
    qDebug()<<"showEvent";
}
