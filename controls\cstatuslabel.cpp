﻿#include "cstatuslabel.h"
#include <QPainter>
#include <QDebug>
#include <QtMath>
#include <QPen>
#include <algorithm>

CStatusLabel::CStatusLabel(QWidget *parent) : QLabel(parent)
{
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#FF0000")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));
    m_vBrush.append(QBrush(QColor("#238E23")));

    m_sStatText = QString::fromLocal8Bit("空闲");

}

void CStatusLabel::paintEvent(QPaintEvent *) {

    QPainter painter1(this);
    painter1.setRenderHint(QPainter::Antialiasing, true);

    // 设置画笔颜色
    QPen pen1;
    pen1.setColor("#FFFFFF");
    painter1.setPen(pen1);

    //vbrush.append(QBrush(QColor("#FFFF00")));
    //vbrush.append(QBrush(QColor("#C0C0C0")));
    //vbrush.append(QBrush(QColor("#D9D919")));
    //vbrush.append(QBrush(QColor("#A67D3D")));
    //vbrush.append(QBrush(QColor("#5C4033")));
    //vbrush.append(QBrush(QColor("#871F78")));
    //vbrush.append(QBrush(QColor("#8F8FBD")));
    //vbrush.append(QBrush(QColor("#8E2323")));
    //vbrush.append(QBrush(QColor("#9932CD")));

    QStringList textlist;
    textlist.append(QString::fromLocal8Bit("正常"));
    textlist.append(QString::fromLocal8Bit("锁紧"));
    textlist.append(QString::fromLocal8Bit("到位"));
    textlist.append(QString::fromLocal8Bit("超上限"));
    textlist.append(QString::fromLocal8Bit("超下限"));
    textlist.append(QString::fromLocal8Bit("驱动故障"));
    textlist.append(QString::fromLocal8Bit("超差"));
    textlist.append(QString::fromLocal8Bit("超速"));
    textlist.append(QString::fromLocal8Bit("找零完成"));
    textlist.append(QString::fromLocal8Bit("电压超限"));

    QVector<QPointF> vsitoffset;
    vsitoffset.append(QPointF(-10.0, 20.0));
    vsitoffset.append(QPointF(-10.0, 10.0));
    vsitoffset.append(QPointF(-15.0, 5.0));
    vsitoffset.append(QPointF(-30.0, -10.0));
    vsitoffset.append(QPointF(-30.0, -10.0));
    vsitoffset.append(QPointF(-40.0, -10.0));
    vsitoffset.append(QPointF(-20.0, 0.0));
    vsitoffset.append(QPointF(-15.0, 10.0));
    vsitoffset.append(QPointF(-20.0, 25.0));
    vsitoffset.append(QPointF(-20.0, 20.0));

    float num = 10.00;
    for (int i = 0;i<10;i++) {
        //double startAngle = 0 * 16; // 起始角度，这里以45度为起点
        double spanAngle = (360 / num) * 16; // 圆弧的角度
        paintRangle(painter1, pen1, spanAngle * i, spanAngle, m_vBrush.at(i), textlist.at(i), vsitoffset.at(i));
    }

    QPointF center(width() / 2, height() / 2);
    double radius = std::min(width(), height()) / 2 - 100;
    QRectF boundingRect(center.x() - radius, center.y() - radius, 2 * radius, 2 * radius);
    QPainterPath path;
    path.moveTo(center.x() + radius, center.y());
    path.arcTo(boundingRect,  0, 360);
    painter1.fillPath(path, QBrush(QColor("#8E2323")));
    painter1.drawPath(path);
    QFont font = this->font();  // 获取当前字体
    font.setPixelSize(26);  // 设置字体大小为26px
    painter1.setFont(font);
    painter1.drawText(center.x() - 25, center.y() + 10, m_sStatText);

}

void CStatusLabel::paintRangle(QPainter &painter, QPen &pen, double startAngle, double spanAngle, const QBrush &brh, const QString &text,const QPointF &sitoffset){
    /*QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 设置画笔颜色
    QPen pen;
    //pen.setWidth(6);
    //pen.setColor("#FFFFFF");
    pen.setColor(QColor(Qt::blue));
    painter.setPen(pen);
    */
    // 设置圆弧的圆心坐标、半径和起始角度、结束角度
    QPointF center(width() / 2, height() / 2);
    double radius = std::min(width(), height()) / 2 - 100;
    //double startAngle = 0 * 16; // 起始角度，这里以45度为起点
    //double spanAngle = 270 * 16; // 圆弧的角度

    double radiusl = std::min(width(), height()) / 2 - 20;

    // 绘制圆弧
    //painter.drawArc(center.x() - radius, center.y() - radius, 2 * radius, 2 * radius, startAngle, spanAngle);

    QRectF boundingRect(center.x() - radius, center.y() - radius, 2 * radius, 2 * radius);
    QPointF startPoint(center.x() + radius * cos(qDegreesToRadians(360 - (startAngle / 16))), center.y() + radius * sin(qDegreesToRadians(360 - (startAngle) / 16)));
    QPointF endPoint(center.x() + radius * cos(qDegreesToRadians(360 - ((startAngle + spanAngle)) / 16)), center.y() + radius * sin(qDegreesToRadians(360 - ((startAngle + spanAngle)) / 16)));

    QRectF boundingRect1(center.x() - radiusl, center.y() - radiusl, 2 * radiusl, 2 * radiusl);
    QPointF startPoint1(center.x() + radiusl * cos(qDegreesToRadians(360 - (startAngle / 16))), center.y() + radiusl * sin(qDegreesToRadians(360 - (startAngle) / 16)));
    QPointF endPoint1(center.x() + radiusl * cos(qDegreesToRadians(360 - ((startAngle + spanAngle)) / 16)), center.y() + radiusl * sin(qDegreesToRadians(360 - ((startAngle + spanAngle)) / 16)));

    //painter.drawLine(startPoint,startPoint1);
    //painter.drawLine(endPoint,endPoint1);

    /*qDebug()<<"radius:"<<radius;
    qDebug()<<boundingRect;
    qDebug()<<startPoint;
    qDebug()<<endPoint;
    qDebug()<<qCos(90.00 * M_PI / 180)<<qSin(0);
    */

    //painter.drawArc(center.x() - radiusl, center.y() - radiusl, 2 * radiusl, 2 * radiusl, startAngle, spanAngle);
    //painter.drawLine(100, 50, 130, 50);

    QPainterPath path;
    path.moveTo(startPoint);
    path.arcTo(boundingRect, startAngle / 16, spanAngle / 16);
    path.moveTo(startPoint);
    path.lineTo(startPoint1);
    path.arcTo(boundingRect1, startAngle / 16, spanAngle / 16);
    path.lineTo(endPoint);
    path.moveTo(startPoint);
    //path.lineTo(startPoint);
    path.closeSubpath();
    painter.fillPath(path, brh);
    painter.drawPath(path);
    double ty = (startPoint.y() + endPoint1.y()) / 2;
    double tx = (startPoint.x() + endPoint.x() + startPoint1.x() + endPoint1.x()) / 4;
    tx += sitoffset.x();
    ty += sitoffset.y();
    QFont font = this->font();  // 获取当前字体
    font.setPixelSize(15);  // 设置字体大小为15px
    painter.setFont(font);
    painter.drawText(tx, ty, text);
}


void CStatusLabel::resizeEvent(QResizeEvent *event) {
    //QLabel::resizeEvent(event);
    //update(); // 调用paintEvent
}

void CStatusLabel::statFlush(QStringList slColor,QString stat){
    m_vBrush.clear();
    for(QString scolor:slColor){
         m_vBrush.append(QColor(scolor));
    }
    m_sStatText = stat;
    update();
}

