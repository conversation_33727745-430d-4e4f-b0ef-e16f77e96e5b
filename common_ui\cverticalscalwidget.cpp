﻿#include "cverticalscalwidget.h"

CVerticalScalWidget::CVerticalScalWidget(QWidget *parent) : QWidget(parent), m_fcurrentval(0) {
        m_fcurrentval = 135.5;
}

void CVerticalScalWidget::setCurrentVal(float val)
{
    m_fcurrentval = val;
    update(); // 触发重绘
}

void CVerticalScalWidget::setDirect(int direct){
    idirect = direct;
    update();
}

void CVerticalScalWidget::setBackColor(QColor sc, QColor ec){
    startBkColor = sc;
    endBkColor = ec;
    update();
}

void CVerticalScalWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true); // 抗锯齿

    drawAltitudeScale(painter);
}

void CVerticalScalWidget::drawAltitudeScale(QPainter &painter) {
    int centerY = height() / 2;
    int cvalwidth = 60;
    int xoffset = width() * 0.3;
    int sscalewidth = 20;
    int lscalewidth = 40;
    int textwidth = 40;
    int sstartmarin = xoffset;     //刻度位置
    int sendmarinl = xoffset + lscalewidth;
    int sendmarins = xoffset + sscalewidth;
    int tstartmarin = xoffset + lscalewidth + 10;     //文本位置
    int tendmarin = tstartmarin + textwidth;
    int cvalmarin = tendmarin;
    int clipmarin = 20;      //剪裁矩形边距
    int polylen = 20;    //五边形半步宽度


    //idirect = 1;

    if(idirect == 1){
        sstartmarin = width() - sstartmarin;
        sendmarins = width() - sendmarins;
        sendmarinl = width() - sendmarinl;
        tendmarin = width() - tstartmarin;
        tstartmarin = width() - tstartmarin - textwidth;
        cvalmarin = width() - cvalmarin - cvalwidth;
    }

    int Scale = 15;
    int range =  16;
    int step = 100.0;

    //m_fcurrentval = 25.0;

    int currentval = static_cast<int>(m_fcurrentval);
    if(qAbs(currentval) < 20){
        step = 5.0;
    }else if(qAbs(currentval) < 100){
        step = 10.0;
    }else{
        step = 100.0;
    }

    // 保存状态
    painter.save();

    // 设置绘图区域

    QRect altitudeRect(10, 0, width() - clipmarin, height() - clipmarin);

    int radius = width() * 0.1;
    QPainterPath path;
    path.addRoundedRect(altitudeRect, radius, radius);

    painter.setClipPath(path);

    //QRect altitudeRect(10, 0, width() - clipmarin , height() - clipmarin);
    //painter.setClipRect(altitudeRect);

    QLinearGradient gradient(altitudeRect.topLeft(), altitudeRect.bottomLeft());

    // 设置渐变颜色（示例：从蓝色渐变到绿色）
    gradient.setColorAt(0.0, startBkColor);    // 顶部颜色
    gradient.setColorAt(0.5, endBkColor);
    gradient.setColorAt(1.0, startBkColor);   // 底部颜色

    // 绘制背景框
    painter.setPen(Qt::NoPen);
    painter.fillRect(altitudeRect, gradient);
    painter.drawRect(altitudeRect);
    painter.setFont(QFont("Arial", 10));

    // 绘制刻度
    painter.setPen(QPen(QBrush(Qt::white), 3));

    int yoffset = currentval % step;

    // 绘制上面刻度
    for (int i = 0;i < range; i++) {
        int ypos = centerY - i * Scale - (float(step - yoffset) / step * Scale);
        int value = currentval - yoffset + i * step + step;
        if(value % (step * 2) == 0){
            //qDebug()<<"up len:"<<ypos<<centerY<<((step - yoffset) / step * Scale)<<float(step - yoffset)/step;
            //painter.drawLine(width() - xoffset, ypos, width() - 20 - xoffset, ypos);
            //painter.drawText(width() - 30 - 30 - xoffset, ypos - 10, 30, 10, Qt::AlignLeft, QString::number(value));
            painter.drawLine(sstartmarin, ypos, sendmarinl, ypos);
            painter.drawText(tstartmarin, ypos - 10, textwidth, 20, Qt::AlignLeft, QString::number(value));
        }else{
            //qDebug()<<"up short:"<<ypos;
            painter.drawLine(sstartmarin, ypos,sendmarins, ypos);
        }
    }

    // 绘制下面刻度
    for (int i = 0;i < range; i++) {
        int ypos = centerY + i * Scale + (float(yoffset) / step * Scale);
        int value = currentval - yoffset - i * step;
        //qDebug()<<"down len:"<<ypos<<centerY<<((step - yoffset) / step * Scale)<<((float)yoffset / step * Scale)<<value<<idirect;
        if(value % (step * 2) == 0){
            //painter.drawLine(width() - xoffset, ypos, width() - 20 - xoffset, ypos);
            //painter.drawText(width() - 30 - 30 - xoffset, ypos - 10, 30, 10, Qt::AlignLeft, QString::number(value));
            painter.drawLine(sstartmarin, ypos, sendmarinl, ypos);
            painter.drawText(tstartmarin, ypos - 10, textwidth, 20, Qt::AlignLeft, QString::number(value));
        }else{
            //qDebug()<<"down short:"<<ypos;
            //painter.drawLine(width() - xoffset, ypos, width() - 10 - xoffset, ypos);
            painter.drawLine(sstartmarin, ypos,sendmarins, ypos);
        }
    }

    // 绘制固定指示框
    //painter.setPen(Qt::yellow);
    //painter.drawRect(cvalmarin , centerY-15, cvalwidth, 30);

    // 显示当前数值
    //painter.drawText(cvalmarin, centerY-15, cvalwidth, 30, Qt::AlignCenter, QString::number(static_cast<int>(currentAltitude)));

    // 恢复状态
    painter.restore();

    QPolygon polygon;
    if(idirect == 1){
        polygon << QPoint(width() - polylen - clipmarin, centerY - 20) << QPoint(width() - clipmarin, centerY - 20);
        polygon << QPoint(width() - clipmarin , centerY + 20) << QPoint(width() - polylen - clipmarin, centerY + 20);
        polygon << QPoint(width() - 2*polylen - clipmarin, centerY); // 添加一个额外的点，形成一个五边形
    }else{
        polygon << QPoint(polylen + clipmarin, centerY - 20) << QPoint(clipmarin, centerY - 20);
        polygon << QPoint(clipmarin, centerY + 20) << QPoint(polylen + clipmarin, centerY + 20);
        polygon << QPoint(2*polylen + clipmarin, centerY); // 添加一个额外的点，形成一个五边形
    }

    // 设置画笔和画刷
    painter.setPen(QPen(QColor(Qt::yellow), 2));

    // 绘制多边形
    painter.drawPolygon(polygon);
}
