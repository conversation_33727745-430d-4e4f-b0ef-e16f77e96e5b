﻿#ifndef CSUBPAGEMANAGER_H
#define CSUBPAGEMANAGER_H

#include <QObject>
#include <QMap>

/*
*显示页面管理类，用于维护tabpage子页面对象
*/
class CSubPageManager : public QObject
{
    Q_OBJECT
public:
    explicit CSubPageManager(QObject *parent = nullptr);
    void RegisterPage(QString pageName, QWidget *pageObj);
    QWidget* GetObjectbyName( QString pageName);
    static CSubPageManager *GetInstance();
private:
    QMap<QString, QWidget *> m_PageMager;
    static CSubPageManager *m_Instance;

signals:

};

#endif // CSUBPAGEMANAGER_H
