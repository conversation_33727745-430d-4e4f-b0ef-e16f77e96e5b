﻿#include <QDebug>
#include <QWidget>
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include <QThread>
#include "c4a0100protocol.h"

C4a0100Protocol::C4a0100Protocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect C4a0100Protocol";
       connect(this, &C4a0100Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &C4a0100Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
   }
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-X:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Z:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-x:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-y:"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-z:"));
   m_slProtoKeys.append("Temp-UNO:");
   m_slProtoKeys.append("Temp-DUE:");
}

C4a0100Protocol::~C4a0100Protocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool C4a0100Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    if(barr.size() < sizeof(Stru4a0100)){
        return false;
    }
    m_uMsgLen = barr.at(4) + (barr.at(5) << 8);

    if(m_uMsgLen != sizeof(Stru4a0100)){
        m_uMsgLen = sizeof(Stru4a0100);
        return false;
    }

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(4)<<barr.at(5);
}

void C4a0100Protocol::paseMsg(const QByteArray msg){
    Stru4a0100 st_4a0100;
    QStringList dataValues;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < 40){
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size();
        return;
    }

    m_iReqCount++;

    ::memcpy(&st_4a0100, msg.data(), 40);
    /*QString str = QString::number(st_3a0100.Quaternion_x, 'f', 4);
    //QString str = QString("%1-%2").arg(st_3a0100.Quaternion_x).arg(st_3a0100.Quaternion_y);
    qDebug()<<"paseMsg:"<<msg.toHex(' ');
    qDebug()<<"paseMsg:"<<msg.mid(7, 4).toHex(' ')<<msg.mid(11, 4).toHex(' ');
    qDebug()<<"paseMsg:"<<st_3a0100.timestamp;
    char buf4[4];
    ::memcpy(buf4, &(st_3a0100.timestamp), 4);
    qDebug()<<QByteArray(buf4, 4).toHex(' ');
    */
    //qDebug()<<"paseMsg:"<<st_3a0100.timestamp<<st_3a0100.Quaternion_w<<st_3a0100.Quaternion_x<<st_3a0100.Quaternion_y<<st_3a0100.Quaternion_z<<QThread::currentThreadId();;
    dataValues.append(QString::number(st_4a0100.accel_x));
    dataValues.append(QString::number(st_4a0100.accel_y));
    dataValues.append(QString::number(st_4a0100.accel_z));
    dataValues.append(QString::number(st_4a0100.gyro_x));
    dataValues.append(QString::number(st_4a0100.gyro_y));
    dataValues.append(QString::number(st_4a0100.gyro_z));
    dataValues.append(QString::number(st_4a0100.temp_DUE));
    dataValues.append(QString::number(st_4a0100.temp_UNO));

    writeCvsFile("4A01", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }

}


