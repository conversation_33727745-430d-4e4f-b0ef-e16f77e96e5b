﻿/********************************************************************
 * 文件名： geturl.cpp
 * 时间：   2024-05-19 14:29:30
 * 开发者：  mhf
 * 邮箱：   <EMAIL>
 * 说明：   瓦片地图网络请求类
 * ******************************************************************/
#include "geturl.h"
#include "bingformula.h"
#include <qnetworkaccessmanager.h>
#include <QNetworkReply>
#include <QDebug>
#include <QSet>
#include <QtConcurrent>
#include "cprotoparamdata.h"
#include "cconfigmanager.h"

QString GetUrl::m_scachedir = "./MapCache";

GetUrl::GetUrl(QObject* parent)
    : QObject{parent}
{
    m_thread = new QThread;
    this->moveToThread(m_thread);
    m_thread->start();

    //m_rect.setTopLeft(Bing::latLongToPixelXY(113.31092834,23.27036548, m_level));
    //m_rect.setBottomRight(Bing::latLongToPixelXY(114.64370728,22.61654627, m_level));
    qDebug()<<"m_rect:"<<m_rect;
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::updateTitle, this, &GetUrl::updateTitle);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::showRect, this, &GetUrl::showRect);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::setLevel, this, &GetUrl::setLevel);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::urlChange, this, &GetUrl::urlChange);

    m_llcentersit = QPointF(113.80979,22.751116);
    //m_llcentersit = QPointF(116.397497, 39.906888);
    MapParmSt &sMapParmst = CProtoParamData::getMapParmSt();
    m_level = sMapParmst.m_iShowLevel;
    qDebug()<<"get usrl init:"<<sMapParmst.m_pDefSite<<sMapParmst.m_iShowLevel;
    m_pixcentersit = Bing::latLongToPixelXY(sMapParmst.m_pDefSite.x(), sMapParmst.m_pDefSite.y(), m_level);
    //m_pixcentersit = Bing::latLongToPixelXY(113.8189301,  22.7485709, m_level);
    m_tilelcentersit = Bing::pixelXYToTileXY(m_pixcentersit);
    qDebug()<<"m_pixcentersit"<<m_pixcentersit<<m_llcentersit<<m_llcentersit.x()<<m_llcentersit.y();
    m_rect.setTopLeft(QPoint(Bing::tileXYToPixelXY(QPoint(m_tilelcentersit.x() - 5, m_tilelcentersit.y() - 5))));
    m_rect.setBottomRight(QPoint(Bing::tileXYToPixelXY(QPoint(m_tilelcentersit.x() + 5, m_tilelcentersit.y() + 5))));

    m_urltype = 0;

    QString slang = G_CONFIG.getValue("System.LANG").toString();
    if(slang == "CN"){
        m_sVectorMap = g_sGDVectorMap;
        m_sRoadMap = g_sGDRoadMap;
        m_sSateMap = g_sGDSateMap;
    }else{
        m_sVectorMap = g_sGGVectorMap;
        m_sRoadMap = g_sGGRoadMap;
        m_sSateMap = g_sGGSateMap;
    }

    FileParmSt *pfilest = CProtoParamData::getFileParmSt();
    m_scachedir = pfilest->mapcachedir;

    emit GetUrlInterface::getInterface() -> showPos(m_pixcentersit, m_llcentersit);
}

GetUrl::~GetUrl()
{
    quit();
    clear();

    m_thread->quit();
    m_thread->wait();
    delete m_thread;
}

void GetUrl::urlinit(int urltype, int layer){
    m_urllayer = layer;
    if(layer == 0){
        if(urltype == 0){
            setUrl(m_sVectorMap, 0);
        }else{
            setUrl(m_sSateMap, 0);
        }
    }else{
        if(urltype == 0){
            setUrl(nullptr, 0);
        }else{
            setUrl(m_sRoadMap, 1);
        }
    }

}

void GetUrl::urlChange(int urltype){
    //qDebug()<<"layer:"<<m_urllayer<<urltype;
    if(m_urllayer == 0){
        if(urltype == 0){
            setUrl(m_sVectorMap, 0);
        }else if(urltype == 1){
            setUrl(m_sSateMap, 0);
        }
    }else{
        if(urltype == 2){
            setUrl(m_sRoadMap, 1);
        }else{
            setUrl(nullptr, 0);
        }
    }
}

/**
 * @brief     设置瓦片地图源地址
 * @param url
 */
void GetUrl::setUrl(QString url, int urltype)
{
    quit();   // 退出下载后再清空数组，防止数据竞争
    clear();
    m_exist.clear();   // 清空已下载列表
    if (url == nullptr || url.isEmpty()){
        return;
    }

    m_url = url;
    getImg(m_rect, m_level);   // 使用默认范围、层级更新地图
    m_urltype = urltype;
}

QString GetUrl::getCacheDir(){
    return m_scachedir;
}

/**
 * @brief       下载瓦片
 * @param info
 * @return
 */
void httpGet(ImageInfo info)
{
    if(info.url == nullptr || info.url.isEmpty()){
        return;
    }

    //大于18，使用空白图片填充
    if(info.z > 18){
        info.img.load(":/img/blankmap.png");
        return;
    }

    QNetworkAccessManager manager;
    QSharedPointer<QNetworkReply> reply(manager.get(QNetworkRequest(QUrl(info.url))));
    // 等待返回
    QEventLoop loop;
    QObject::connect(reply.data(), &QNetworkReply::finished, &loop, &QEventLoop::quit);   // 等待获取完成
    QTimer::singleShot(5000, &loop, &QEventLoop::quit);                                   // 等待超时
    loop.exec();

    QString sdir = GetUrl::getCacheDir() + QString("/%1").arg(info.z);
    QString imgfilename = QString(sdir + "/f%2_%3.png").arg(info.x).arg(info.y);
    QDir dir(sdir);
    if(!dir.exists() && !dir.mkdir(".")){
        qDebug()<<"create dir fail:";
    }

    if (reply->error() == QNetworkReply::NoError)
    {
        QByteArray buf = reply->readAll();
        if (!buf.isEmpty())
        {
            info.img.loadFromData(buf);

            if(!info.img.save(imgfilename, "PNG")){
                qDebug()<<"save imgfile fail"<<imgfilename<<errno;
            }else{
                //qDebug()<<"save imgfile succ";
            }
            if (!info.img.isNull())
            {
                emit GetUrlInterface::getInterface() -> update(info);
                emit GetUrlInterface::getInterface() -> updateTitle(info.x, info.y, info.z);
                return;
            }
        }
    }

    info.count++;
    if (info.count < 3)
    {
        httpGet(info);   // 下载失败重新下载
        return;
    }
    else
    {
        qWarning() << "下载失败：" << reply->errorString();
        info.img.load(imgfilename);
        if (!info.img.isNull())
        {
            emit GetUrlInterface::getInterface() -> update(info);
            emit GetUrlInterface::getInterface() -> updateTitle(info.x, info.y, info.z);
            return;
        }
    }
}

/**
 * @brief       获取瓦片地图
 * @param rect  瓦片地图的像素范围
 * @param level 瓦片地图的级别
 */
void GetUrl::getImg(QRect rect, int level)
{
    if (rect.isEmpty())
        return;
    if (level > 22 || level < 0)
        return;
    m_rect = rect;
    m_level = level;

    if (m_future.isRunning())   // 判断是否在运行
    {
        m_future.cancel();   // 取消下载
    }
    clear();   // 清空待下载列表

    getTitle(rect, level);   // 获取所有需要加载的瓦片编号
    //qDebug() << QString::fromLocal8Bit("获取瓦片数：") << m_infos.count();
    getUrl();                                         // 将瓦片编号转为url
    //检查url是否可用，避免阻塞线程
    m_future = QtConcurrent::map(m_infos, httpGet);   // 在线程池中下载瓦片图
}

/**
 * @brief      设置获取瓦片地图的像素范围
 * @param rect
 */
void GetUrl::showRect(QRect rect)
{
    if (rect.isEmpty())
        return;
    getImg(rect, m_level);
}

/**
 * @brief       通过设置显示瓦片层级别完成缩放显示
 * @param level
 */
void GetUrl::setLevel(int level)
{
    if ((level < 0) || (level > 23))
    {
        return;
    }
    if (m_level != level)
    {
        m_exist.clear();   // 清空已下载列表
    }
    m_level = level;
}

/**
 * @brief       获取瓦片编号
 * @param rect
 * @param level
 */
void GetUrl::getTitle(QRect rect, int level)
{
    QPoint tl = Bing::pixelXYToTileXY(rect.topLeft());
    QPoint br = Bing::pixelXYToTileXY(rect.bottomRight());

    quint64 value = 0;
    ImageInfo info;
    info.z = level;

    int max = qPow(2, level);   // 最大瓦片编号
    //qDebug()<<"tl-br"<<tl<<br<<max<<level;
    for (int x = tl.x(); x <= br.x(); x++)
    {
        info.type = m_urltype;

        if (x < 0)
            continue;
        if (x >= max)
            break;
        info.x = x;
        for (int y = tl.y(); y <= br.y(); y++)
        {
            if (y < 0)
                continue;
            if (y >= max)
                break;
            value = ((quint64) level << 48) + (x << 24) + y;

            if (!m_exist.contains(value))
            {
                info.y = y;
                m_infos.append(info);
                //qDebug()<<info.x<<info.y<<info.z;
            }
        }
    }
}

/**
 * @brief 获取用于请求瓦片地图的信息
 */
void GetUrl::getUrl()
{
    if(m_url == nullptr || m_url.isEmpty()){
        return;
    }

    if (m_url.contains("{x}"))   // XYZ格式
    {
        QString url = m_url;
        url.replace("{x}", "%1");
        url.replace("{y}", "%2");
        url.replace("{z}", "%3");
        for (int i = 0; i < m_infos.count(); i++)
        {
            m_infos[i].url = url.arg(m_infos[i].x).arg(m_infos[i].y).arg(m_infos[i].z);
            //qDebug()<<"url info:"<<m_infos[i].url;
        }
    }
    else if (m_url.contains("{q}"))   // Bing的quadKey格式
    {
        QString url = m_url;
        url.replace("{q}", "%1");
        QPoint point;
        for (int i = 0; i < m_infos.count(); i++)
        {
            point.setX(m_infos[i].x);
            point.setY(m_infos[i].y);
            QString quadKey = Bing::tileXYToQuadKey(point, m_infos[i].z);   // 将xy转为quadkey
            m_infos[i].url = url.arg(quadKey);
        }
    }
    else
    {
        qDebug() << QString::fromLocal8Bit("url格式未定义")<<m_url;
    }
}

/**
 * @brief 清空内容
 */
void GetUrl::clear()
{
    QVector<ImageInfo> info;
    m_infos.swap(info);
}

/**
 * @brief 退出下载
 */
void GetUrl::quit()
{
    if (m_future.isRunning())   // 判断是否在运行
    {
        m_future.cancel();            // 取消下载
        m_future.waitForFinished();   // 等待退出
    }
}

/**
 * @brief     将下载成功的瓦片编号添加进已下载列表，已经下载的瓦片在后续不进行下载
 * @param x
 * @param y
 * @param z
 */
void GetUrl::updateTitle(int x, int y, int z)
{
    quint64 value = (quint64(z) << 48) + (x << 24) + y;
    m_exist.insert(value);
}
