﻿#include <QDebug>
#include "cbb11dbbdProtocol.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "csubpagemanager.h"


cbb11dbbdProtocol::cbb11dbbdProtocol(QObject *parent) : CBaseProtocol(parent)
{

    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
   if(dripage != NULL){
       qDebug()<<"connect cbb11dbbdProtocol";
       connect(this, &cbb11dbbdProtocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &cbb11dbbdProtocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
   }

   m_slProtoKeys.append(QString::fromLocal8Bit("帧序号"));
   m_slProtoKeys.append(QString::fromLocal8Bit("FPGA版本"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU温度U"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU温度D"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU陀螺X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU陀螺Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU陀螺Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU加速度计X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU加速度计Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("IMU加速度计Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("X轴磁力计"));
   m_slProtoKeys.append(QString::fromLocal8Bit("Y轴磁力计"));
   m_slProtoKeys.append(QString::fromLocal8Bit("Z轴磁力计"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺-Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度-X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度-Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("陀螺温度-Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加速度-Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("加计温度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("GNSS周-AGR"));
   m_slProtoKeys.append(QString::fromLocal8Bit("GNSS秒-AGR"));
   m_slProtoKeys.append(QString::fromLocal8Bit("GNSS秒"));
   m_slProtoKeys.append(QString::fromLocal8Bit("GNSS延迟"));
   m_slProtoKeys.append(QString::fromLocal8Bit("GNSS卫星数"));
   m_slProtoKeys.append(QString::fromLocal8Bit("RTK状态"));
   m_slProtoKeys.append(QString::fromLocal8Bit("速度状态"));
   m_slProtoKeys.append(QString::fromLocal8Bit("真北航迹方向"));
   m_slProtoKeys.append(QString::fromLocal8Bit("北向速度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("东向速度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("天向速度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("位置状态"));
   m_slProtoKeys.append(QString::fromLocal8Bit("纬度方向"));
   m_slProtoKeys.append(QString::fromLocal8Bit("纬度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("经度方向"));
   m_slProtoKeys.append(QString::fromLocal8Bit("经度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("高程"));
   m_slProtoKeys.append(QString::fromLocal8Bit("航向状态"));
   m_slProtoKeys.append(QString::fromLocal8Bit("基线长"));
   m_slProtoKeys.append(QString::fromLocal8Bit("横滚角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("俯仰角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("航向角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("ECEF_X"));
   m_slProtoKeys.append(QString::fromLocal8Bit("ECEF_Y"));
   m_slProtoKeys.append(QString::fromLocal8Bit("ECEF_Z"));
   m_slProtoKeys.append(QString::fromLocal8Bit("几何精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("位置精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("时间精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("垂直精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("水平精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("北向精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("东向精度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("截至高度角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("帧累计值"));
   m_slProtoKeys.append(QString::fromLocal8Bit("经度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("纬度"));
   m_slProtoKeys.append(QString::fromLocal8Bit("高程"));
   m_slProtoKeys.append("Ave");
   m_slProtoKeys.append("Avn");
   m_slProtoKeys.append("Avu");
   m_slProtoKeys.append(QString::fromLocal8Bit("A-俯仰角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("A-横滚角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("A-航向角"));
   m_slProtoKeys.append(QString::fromLocal8Bit("FPGA中断数"));
   m_slProtoKeys.append(QString::fromLocal8Bit("处理包计数"));
   m_slProtoKeys.append(QString::fromLocal8Bit("系统状态"));

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);


}

bool cbb11dbbdProtocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 4);
    m_uMsgLen = (unsigned char)barr.at(4) + ((unsigned char)barr.at(5) << 8);

    if(m_uMsgLen != sizeof(FpgaDataInfo)){
        qDebug()<<"length parse error";
        m_uMsgLen = sizeof(FpgaDataInfo);
        return false;
    }

    return true;
}

void cbb11dbbdProtocol::paseMsg(const QByteArray msg){
    FpgaDataInfo st_bb1db;

    QStringList dataValues;
    //qDebug()<<sizeof (FpgaDataInfo)<<msg.size()<<m_uMsgLen;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < m_uMsgLen){ //254
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size()<<msg.toHex(' ');
        return;
    }

    m_iReqCount++;

    ::memcpy(&st_bb1db, msg.data(), sizeof(FpgaDataInfo));

    uint16_t currentindx = st_bb1db.num_clk >> 8;
    //qDebug()<<"code:"<<currentindx<<st_bb1db.version;
    if(m_iFramIndx != -1 && ((m_iFramIndx + 1) % m_iFpgaFc != currentindx)){
        qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN<<msg.mid(8,2).toHex(' ')<<msg.mid(10,2).toHex(' ');
        qDebug()<< m_sMsgCache.toHex();
        qDebug()<< msg.toHex();
        m_iLossFrameSum++;
        setFrameErr(E_FRAME_LOSS);
        if(m_bIsNeedShowL){
            emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") + QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
            m_bIsNeedShowL = false;
        }
    }

    if(!sum8to16CheckSum(msg, 0, 2)){
        qDebug()<<QTime::currentTime().toString()<<"sum16CheckSum error";
        qDebug()<<msg.toHex();
        m_iCheckFailSum++;
        setFrameErr(E_FRAME_CHECK);
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        //return;
    }

    m_sMsgCache.clear();
    m_sMsgCache.append(msg);
    m_iFramIndx = currentindx;

    dataValues.append(QString::number(currentindx));
    dataValues.append(QString::number(st_bb1db.version));
    dataValues.append(QString::number(st_bb1db.UNOtemperature));
    dataValues.append(QString::number(st_bb1db.DUEtemperature));
    dataValues.append(QString::number(st_bb1db.imu_gyrox));
    dataValues.append(QString::number(st_bb1db.imu_gyroy));
    dataValues.append(QString::number(st_bb1db.imu_gyroz));
    dataValues.append(QString::number(st_bb1db.imu_accx));
    dataValues.append(QString::number(st_bb1db.imu_accy));
    dataValues.append(QString::number(st_bb1db.imu_accz));
    dataValues.append(QString::number(st_bb1db.magGrpx));
    dataValues.append(QString::number(st_bb1db.magGrpy));
    dataValues.append(QString::number(st_bb1db.magGrpz));
    dataValues.append(QString::number(st_bb1db.fog_x));
    dataValues.append(QString::number(st_bb1db.fog_y));
    dataValues.append(QString::number(st_bb1db.fog_z));
    dataValues.append(QString::number(st_bb1db.fog_tempx));
    dataValues.append(QString::number(st_bb1db.fog_tempy));
    dataValues.append(QString::number(st_bb1db.fog_tempz));
    dataValues.append(QString::number(st_bb1db.axis3_accx));
    dataValues.append(QString::number(st_bb1db.axis3_accy));
    dataValues.append(QString::number(st_bb1db.axis3_accz));
    dataValues.append(QString::number(st_bb1db.axis3_temp));
    dataValues.append(QString::number(st_bb1db.hGPSData_gpsweek));
    dataValues.append(QString::number(st_bb1db.hGPSData_gpssecond));
    dataValues.append(QString::number(st_bb1db.gpssecond982));
    dataValues.append(QString::number(st_bb1db.ppstimesdelay));
    dataValues.append(QString::number(st_bb1db.star_num));
    dataValues.append(QString::number((st_bb1db.rtkstatus & 0xFF00) >> 8));
    dataValues.append(QString::number(st_bb1db.gnssspeedstatus));
    st_bb1db.tracktrue[4] = st_bb1db.tracktrue[5];
    st_bb1db.tracktrue[5] = '\0';
    QByteArray newdata;
    newdata.append(st_bb1db.tracktrue[4]);
    newdata.append(st_bb1db.tracktrue[3]);
    newdata.append(st_bb1db.tracktrue[2]);
    newdata.append(st_bb1db.tracktrue[1]);
    newdata.append(st_bb1db.tracktrue[0]);
    dataValues.append(newdata);
    dataValues.append(QString::number(st_bb1db.speed_vn));
    dataValues.append(QString::number(st_bb1db.speed_ve));
    dataValues.append(QString::number(st_bb1db.speed_vu));
    dataValues.append(CTlhTools::char2String(st_bb1db.gnsspositionstaus[1]));
    dataValues.append(CTlhTools::char2String(st_bb1db.directionofLat[1]));
    dataValues.append(QString::number(st_bb1db.latitude));
    dataValues.append(CTlhTools::char2String(st_bb1db.directionofLon[1]));
    dataValues.append(QString::number(st_bb1db.longitude));
    dataValues.append(QString::number(st_bb1db.altitude));
    dataValues.append(QString::number((st_bb1db.headingstate & 0xFF00) >> 8));
    dataValues.append(QString::number(st_bb1db.baselinelength));
    dataValues.append(QString::number(st_bb1db.roll));
    dataValues.append(QString::number(st_bb1db.pitch));
    dataValues.append(QString::number(st_bb1db.yaw));
    dataValues.append(QString::number(st_bb1db.ecef_x));
    dataValues.append(QString::number(st_bb1db.ecef_y));
    dataValues.append(QString::number(st_bb1db.ecef_z));
    dataValues.append(QString::number(st_bb1db.geometry_z));
    dataValues.append(QString::number(st_bb1db.location_z));
    dataValues.append(QString::number(st_bb1db.time_z));
    dataValues.append(QString::number(st_bb1db.vertical_z));
    dataValues.append(QString::number(st_bb1db.horizontal_z));
    dataValues.append(QString::number(st_bb1db.north_z));
    dataValues.append(QString::number(st_bb1db.east_z));
    dataValues.append(QString::number(st_bb1db.endheight_z));
    dataValues.append(QString::number(st_bb1db.gd_packetindex));
    dataValues.append(QString::number(st_bb1db.Alongitude));
    dataValues.append(QString::number(st_bb1db.Alatitude));
    dataValues.append(QString::number(st_bb1db.Aaltitude));
    dataValues.append(QString::number(st_bb1db.Ave));
    dataValues.append(QString::number(st_bb1db.Avn));
    dataValues.append(QString::number(st_bb1db.Avu));
    dataValues.append(QString::number(st_bb1db.Apitch));
    dataValues.append(QString::number(st_bb1db.Aroll));
    dataValues.append(QString::number(st_bb1db.Aheading));
    dataValues.append(QString::number(st_bb1db.fpga_internum));
    dataValues.append(QString::number(st_bb1db.packnum));
    //dataValues.append(QString(st_bb1db.sys_stat));

    dataValues.append(getSysStatDesByC(st_bb1db.sys_stat[0]));

    writeCvsFile("BB11", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        //qDebug()<<"time:"<<QTime::currentTime().toString()<<stime;
        if(m_bIsNeedKeys){
            emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        }else{
            emit sigDataUpdate(QStringList(), dataValues, m_sPortN, m_iProtoIndex);
        }
        //qDebug()<<dataValues;

        m_bIsNeedShow = false;
    }

}

