﻿#ifndef CBB11DBBD_H
#define CBB11DBBD_H

#include <QObject>
#include "cbaseprotocol.h"

class cbb11dbbdProtocol : public CBaseProtocol
{
    Q_OBJECT

public:
    explicit cbb11dbbdProtocol(QObject *parent = nullptr);
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);

private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _FpgaDataInfo_{
        unsigned short head;
        unsigned short cmd;
        unsigned short length;
        unsigned short	data_len;
        unsigned short	num_clk;
        unsigned short	version;
        short	UNOtemperature;
        short	DUEtemperature;
        short	imu_gyrox;
        short	imu_gyroy;
        short	imu_gyroz;
        short	imu_accx;
        short	imu_accy;
        short	imu_accz;
        short	magGrpx;
        short	magGrpy;
        short	magGrpz;
        int	fog_x;
        int	fog_y;
        int	fog_z;
        unsigned short	fog_tempx;
        unsigned short	fog_tempy;
        unsigned short	fog_tempz;
        float	axis3_accx;
        float	axis3_accy;
        float	axis3_accz;
        unsigned short	axis3_temp;
        unsigned short	reserved0;
        unsigned short	reserved1;
        unsigned short	reserved2;
        unsigned short	hGPSData_gpsweek;
        unsigned int	hGPSData_gpssecond;
        unsigned int	gpssecond982;
        unsigned int	ppstimesdelay;
        unsigned short	star_num;
        unsigned short	rtkstatus;
        unsigned short	gnssspeedstatus;
        char	tracktrue[6];
        float	speed_vn;
        float	speed_ve;
        float	speed_vu;
        unsigned char	gnsspositionstaus[2];
        unsigned char	directionofLat[2];
        double	latitude;
        unsigned char	directionofLon[2];
        double	longitude;
        double	altitude;
        unsigned short	headingstate;
        float	baselinelength;
        float	roll;
        float	pitch;
        float	yaw;
        float	ecef_x;
        float	ecef_y;
        float	ecef_z;
        float	geometry_z;
        float	location_z;
        float	time_z;
        float	vertical_z;
        float	horizontal_z;
        float	north_z;
        float	east_z;
        float	endheight_z;
        unsigned short	fpga_checksum;
        unsigned short	gd_checksum;
        unsigned short	gd_packetindex;
        double  Alongitude;        //算法结果，经纬高
        double  Alatitude;        //算法结果，经纬高
        float   Aaltitude;       //算法结果，经纬高
        float   Ave;             //算法结果，东向速度
        float   Avn;             //算法结果，北向速度
        float   Avu;             //算法结果，天向速度
        float   Apitch;          //算法结果，俯仰角
        float   Aroll;           //算法结果，横滚角
        float   Aheading;        //算法结果，偏航角
        unsigned short check_numA;
        unsigned int  fpga_internum;
        unsigned int  packnum;
        char  sys_stat[2];
        unsigned short check_num;
    }FpgaDataInfo;
#pragma pack(pop)

signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString sportN);
private:

};


#endif // CBB11DBBD_H
