<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CCommPage</class>
 <widget class="QWidget" name="CCommPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Communication Settings</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBox_CommConfig">
     <property name="title">
      <string>Communication Configuration</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label_CommType">
        <property name="text">
         <string>Type:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="cb_CommType"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_PortN">
        <property name="text">
         <string>Port:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="cb_PortN"/>
      </item>
      <item row="1" column="2">
       <widget class="QLabel" name="label_BaudR">
        <property name="text">
         <string>Baud Rate:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QComboBox" name="cb_BaudR"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_DataB">
        <property name="text">
         <string>Data Bits:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="cb_DataB"/>
      </item>
      <item row="2" column="2">
       <widget class="QLabel" name="label_CheckB">
        <property name="text">
         <string>Parity:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="QComboBox" name="cb_CheckB"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_StopB">
        <property name="text">
         <string>Stop Bits:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="cb_StopB"/>
      </item>
      <item row="3" column="2">
       <widget class="QLabel" name="label_DataM">
        <property name="text">
         <string>Data Mode:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="QComboBox" name="cb_DataM"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_TcpIP">
        <property name="text">
         <string>TCP IP:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QComboBox" name="cb_TcpIP">
        <property name="editable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="QLabel" name="label_TcpPort">
        <property name="text">
         <string>TCP Port:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="3">
       <widget class="QComboBox" name="cb_TcpPort">
        <property name="editable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="0" colspan="2">
       <widget class="QPushButton" name="bt_CommOpen">
        <property name="text">
         <string>Open</string>
        </property>
       </widget>
      </item>
      <item row="5" column="2" colspan="2">
       <widget class="QPushButton" name="bt_CommClose">
        <property name="text">
         <string>Close</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_CommData">
     <property name="title">
      <string>Communication Data</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QTextEdit" name="ed_CommRecvB">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QTextEdit" name="ed_CommSendB">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>80</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QComboBox" name="cb_writeType"/>
          </item>
          <item>
           <widget class="QPushButton" name="bt_CommSend">
            <property name="text">
             <string>Send</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="bt_CommClear">
            <property name="text">
             <string>Clear</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
