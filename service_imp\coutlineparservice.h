﻿#ifndef COUTLINEPASESERVICE_H
#define COUTLINEPASESERVICE_H

#include <QObject>
#include "cresolvingtask.h"
#include <QFile>
#include "cprotoparamdata.h"

#define READF_BUFF_LEN 1024

/*离线二进制数据文件解析*/
class COutLineParService : public QObject
{
    Q_OBJECT
public:
    explicit COutLineParService(QObject *parent = nullptr);
    ~COutLineParService();
    void timerEvent(QTimerEvent *event) override;
    void stopWorking();

signals:
    void sigDataRead(const QByteArray &arr);
    void sigFileReadEnd(const QString resinfo);
private:
    CResolvingTask *m_resolvTask;
    bool m_bIsWorking;
    QStringList m_sComList;
    int m_timeStatus;
    int m_icatRound;
    int m_iTimeOut;
    int m_iIntvTimes;
    QFile m_fhandle;
    int m_tId;
    CProtoParamData *m_pparm;
    bool m_bIsError;
public:
    void setParm(CProtoParamData *parm){
        m_pparm = parm;
    }
public slots:
    bool fileReadData(QString filename);

};

#endif // COUTLINEPASESERVICE_H
