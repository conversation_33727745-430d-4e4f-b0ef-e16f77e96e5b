﻿#ifndef C3B020BPROTOCOL_H
#define C3B020BPROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class C3B020BProtocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C3B020BProtocol(QObject *parent = nullptr);
    bool preInit();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
    bool sum8CheckSum(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru3b020b{
        unsigned char  head[3];
        uint32_t timestamp;
        short Angularvelocity_x;
        short Angularvelocity_y;
        short Angularvelocity_z;
        short CelebrationAcceleration_x;
        short CelebrationAcceleration_y;
        short CelebrationAcceleration_z;
        float Quaternion_w;
        float Quaternion_x;
        float Quaternion_y;
        float Quaternion_z;
        unsigned char checksum;
    }Stru3b020b;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigChartsUpdate(const QStringList &statuskeys, const QVector<double> Values, const QString &sportN);

};

#endif // C3B020BPROTOCOL_H
