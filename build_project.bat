@echo off
echo Building TLHV1_DEVP Project with TCP Communication Support
echo ========================================================

REM Check if Qt environment is set
if "%QTDIR%"=="" (
    echo Error: Qt environment not found. Please set QTDIR environment variable.
    echo Example: set QTDIR=C:\Qt\5.14.2\msvc2017_64
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

REM Run qmake
echo Running qmake...
%QTDIR%\bin\qmake.exe ..\TLHV1_DEVP.pro
if errorlevel 1 (
    echo Error: qmake failed
    pause
    exit /b 1
)

REM Build the project
echo Building project...
nmake
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable should be in the build directory.

REM Build test client
echo Building TCP test client...
cd ..
%QTDIR%\bin\qmake.exe test_tcp_client.cpp -o test_client_makefile
if errorlevel 1 (
    echo Warning: Test client qmake failed
) else (
    nmake -f test_client_makefile
    if errorlevel 1 (
        echo Warning: Test client build failed
    ) else (
        echo Test client built successfully!
    )
)

pause
