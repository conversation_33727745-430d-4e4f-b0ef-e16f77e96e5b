﻿#include "cscalebarlabel.h"
#include <QPainter>
#include <QDebug>

/*刻度尺*/

CScalebarLabel::CScalebarLabel(QWidget *parent) : QLabel(parent)
{

    pradiu = 9;
    pcenter = QPointF(pradiu,pradiu);
    ispress = false;
    currentp = pradiu;
    m_process = 0.0;

}

void CScalebarLabel::paintEvent(QPaintEvent *e){
    QPainter painter(this);
    painter.fillRect(e->rect().x() + pradiu, e->rect().y() + pradiu, currentp, e->rect().height(), QColor("#C1DD29"));
    painter.fillRect(e->rect().x() + currentp, e->rect().y() + pradiu, e->rect().width(), e->rect().height(), QColor("#FDC1C1"));
    QPen pen;
    int slwidth = 3;
    pen.setWidth(slwidth);
    pen.setColor("#009966");
    painter.setPen(pen);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.drawLine(pradiu, pradiu, currentp, pradiu);
    pen.setColor("#FF69B4");
    painter.setPen(pen);
    painter.drawLine(currentp, pradiu, width(), pradiu);
    float shortline = height() * 0.5;
    float longline = height() * 0.7;
    float interlen = (width() - 30 - pradiu) / 100.0;
    pen.setColor("#0");
    pen.setWidth(1);
    painter.setPen(pen);
    for (int i = 0;i <= 100;i++) {
        if(i % 10 == 0){
            painter.drawLine(i * interlen + pradiu, slwidth + pradiu, i * interlen + pradiu, longline);
            if( i != 0){
                painter.drawText(i * interlen - 7, longline + 10, QString::number(i) + "%");
            }else{
                painter.drawText(0, longline + 10, "0");
            }
        }else{
            painter.drawLine(i * interlen + pradiu, slwidth + pradiu, i * interlen + pradiu, shortline);
        }
    }

    //pen.setWidth(1);
    //pen.setBrush(QBrush(Qt::red));
    //painter.setPen(pen);
    //painter.setBrush(QPixmap(":/img/goldpolyback01.png"));


    //QPolygonF polygon;
    //polygon << pcenter << QPointF(pcenter.x()-pradiu, pcenter.y() - pradiu) << QPoint(pcenter.x() + pradiu, pcenter.y() - pradiu);
    //painter.drawPolygon(polygon);
    //QPainterPath polypath;
    //polypath.moveTo(pcenter);
    //polypath.lineTo(pcenter.x()-pradiu, pcenter.y() - pradiu);
    //polypath.lineTo(pcenter.x() + pradiu, pcenter.y() - pradiu);
    //polypath.lineTo(pcenter); // 闭合路径
    //painter.drawPath(polypath);

    //painter.drawEllipse(pcenter, pradiu, pradiu);
    painter.drawImage(pcenter.x() - pradiu * 0.6, 0 , QImage(":/img/pbarrow.png"));
}

void CScalebarLabel::mousePressEvent(QMouseEvent *ev){
    //qDebug()<<pcenter<<ev->x()<<ev->y();
    if(ev->x() >= pcenter.x() - pradiu && ev->x() <= pcenter.x()+ pradiu && ev->y() >= pcenter.y() - pradiu && ev->y() <=pcenter.y() + pradiu){
        qDebug()<<"pointin";
        ispress = true;
    }
}
void CScalebarLabel::mouseMoveEvent(QMouseEvent *ev){
    if(ispress){
        if(ev->x() > width() - 30){
            pcenter = QPointF(width() - 30, pcenter.y());
        }else if(ev->x() < pradiu){
            pcenter = QPointF(pradiu, pcenter.y());
        }else{
            pcenter = QPointF(ev->x(), pcenter.y());
        }
        currentp = pcenter.x();
        m_process = (currentp - pradiu) * 100.0/ (width() - pradiu - 30);
        emit sigpmoved(m_process, 0);
        update();
    }
}
void CScalebarLabel::mouseReleaseEvent(QMouseEvent *ev){
    ispress = false;
    m_process = (currentp - pradiu) * 100.0/ (width() - pradiu - 30);
    currentp = pradiu + m_process / 100.0 * (width() - pradiu - 30);
    emit sigpmoved(m_process, 1);
}

void CScalebarLabel::setProcess(float process){
    //qDebug()<<"setProcess:"<<m_process<<process;
    if(ispress || process < m_process){
        return;
    }
    m_process = process;
    currentp =  pradiu + process / 100.0 * (width() - pradiu - 30);
    pcenter.setX(currentp);
    update();
}

float CScalebarLabel::getProcess(){
    return currentp;
}

void CScalebarLabel::reset(){
    m_process = 0.0;
    currentp = pradiu;
    pcenter.setX(currentp);
    update();
}
