﻿#ifndef CAA6600PROTOCOL_H
#define CAA6600PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

#define TSCAL_FAC 0.0625   //温度比例因子
#define FOGX_FAC 748986.469396   //陀螺仪X轴标定因素
#define FOGY_FAC 757839.137248   //陀螺仪Y轴标定因素
#define FOGZ_FAC 755024.395768   //陀螺仪Z轴标定因素

class Caa6600Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit Caa6600Protocol(QObject *parent = nullptr);
    ~Caa6600Protocol();
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);

private:
#pragma pack(push)
#pragma pack(1)
    typedef struct gdwrxdata912info {
        uint16_t    datalength;
        uint16_t    selftestingcode;
        uint16_t    fpgaversion;
        uint16_t    watchversion;
        uint16_t    Tgears;
        float   Tflwheelspeed;
        float   Tfrwheelspeed;
        float   Tblwheelspeed;
        float   Tbrwheelspeed;
        uint16_t Tcaninfocounter;
        int32_t fogx;
        int32_t fogy;
        int32_t fogz;
        int16_t fogtemperaturex;
        int16_t fogtemperaturey;
        int16_t fogtemperaturez;
        uint32_t   accelerometerx;
        uint32_t   accelerometery;
        uint32_t   accelerometerz;
        int16_t    accelerometertemp;
        uint16_t   reserve1e;
        uint16_t   reserve1f;
        uint16_t   Reserve20;
        uint16_t   gnssweek;
        uint32_t   millisecondofweek;
        uint32_t   secondofweek;
        uint32_t   ppsdelay10ns;
        uint16_t   gpsstarnumber;
        uint16_t   rtkstatus;
        uint16_t   speedstatus;
        uint16_t   truenorthtrack[3];
        float	northvelocity;
        float   eastvelocity;
        float   upvelocity;
        uint16_t  positionstatus;
        uint16_t  directionoflat;
        double  latitude;
        uint16_t  directionoflon;
        double  longitude;
        double  altitude;
        uint16_t  Headingstate;
        uint32_t  baselength;
        float   roll;
        float   pitch;
        float   yaw;
        uint16_t gears;
        uint16_t caninfocounter;
        float   flwheelspeed;
        float   frwheelspeed;
        float   blwheelspeed;
        float   brwheelspeed;
        float	timeprecisionZ;
        float	verticalprecZ;
        float	horizontalprecZ;
        float	northprecisionZ;
        float	eastprecisionZ;
        float	endheightangleZ;
        uint16_t checksum;
        uint16_t frameindex;
        //The following are the results of the algorithm
        double	Alongitude;
        double	Alatitude;
        float	Aaltitude;
        float	Ave;
        float	Avn;
        float	Avu;
        float	Apitch;
        float	Aroll;
        float	Aheading;
        uint16_t checksumA;
    } gdwrxdata912_t;
    typedef struct gdwrxdata912info_new {
        uint16_t selftestingcode;
        int32_t fogx;
        int32_t fogy;
        int32_t fogz;
        int16_t fogtemperaturex;
        int16_t fogtemperaturey;
        int16_t fogtemperaturez;
        DataS32   accelerometerx;
        DataS32   accelerometery;
        DataS32   accelerometerz;
        float  alg1_fogx;
        float  alg1_fogy;
        float  alg1_fogz;
        float  alg1_accx;
        float  alg1_accy;
        float  alg1_accz;
    }gdwrxdata912_t_new;
    typedef struct _Struaa6600{
        unsigned short head;
        unsigned short cmd;
        unsigned short len;
        gdwrxdata912_t_new st921info;
        unsigned short checksum;
    }Struaa6600;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString sportN);

private:

};

#endif // CAA6600PROTOCOL_H
