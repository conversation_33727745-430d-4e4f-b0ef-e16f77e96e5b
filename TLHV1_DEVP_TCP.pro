QT += core widgets serialport network

CONFIG += c++11

TARGET = TLHV1_DEVP_TCP
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp

# Include subdirectories
include(common/common_new.pri)
include(service_ui/service_ui_new.pri)
include(service_imp/service_imp_new.pri)
include(service_dialog/service_dialog_new.pri)
include(service_protocol/service_protocol_new.pri)

# Resources
RESOURCES += \
    resource.qrc

# Windows specific settings
win32 {
    RC_FILE = app.rc
    QMAKE_TARGET_PRODUCT = "TLHV1 Development Tool with TCP Support"
    QMAKE_TARGET_DESCRIPTION = "TLHV1 Development Tool with Serial and TCP Communication"
    QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"
}

# Output directories
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/debug
    OBJECTS_DIR = $$PWD/debug/obj
    MOC_DIR = $$PWD/debug/moc
    RCC_DIR = $$PWD/debug/rcc
    UI_DIR = $$PWD/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/release
    OBJECTS_DIR = $$PWD/release/obj
    MOC_DIR = $$PWD/release/moc
    RCC_DIR = $$PWD/release/rcc
    UI_DIR = $$PWD/release/ui
}
