﻿#ifndef CBDDB1BPROTOCOL_H
#define CBDDB1BPROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class Cbddb1bProtocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit Cbddb1bProtocol(QObject *parent = nullptr);
    ~Cbddb1bProtocol();
    bool setProtoLength(const QByteArray &barr);
    bool preInit();
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Strubddb1b{
        uint8_t header[3];	//0xbd,0xdb,0x1b
        short roll;		    //横滚角
        short pitch;		//俯仰角
        short azimuth;	    //航向角
        long gyrox;		//陀螺x轴
        long gyroy;		//陀螺y轴
        long  gyroz;		    //陀螺z轴
        long accelx;		//加速度x轴
        long accely;		//加速度y轴
        long accelz;		//加速度z轴
        long latitude;	    //纬度
        long longitude;	    //经度
        long altitude;	    //高度
        short ve;			//东向速度
        short vn;			//北向速度
        short vu;			//天向速度
        uint8_t status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向
        uint8_t gss_second;
        uint8_t reserve[5];
        unsigned short pdata1;
        unsigned short pdata2;
        short pdata3;
        uint32_t gps_time;
        uint8_t type;
        uint8_t xor_verify1;
        uint32_t gps_week;
        uint8_t xor_verify2;
    }Strubddb1b;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);
    void sigNavUpdate(const QStringList statusValues, const QString portn, const int protoindex);
    void sigChartsUpdate(const QStringList &datakeys, const QVector<double> dataValues, const QString &sportN, const int protoindex);
    void sigAttituUpdate(const QVector<double> attivalues, const QString portn);

private:
    QString LatStd;
    QString LonStd;
    QString HStd;
    QString Vn_std;
    QString Ve_std;
    QString Vd_std;
    QString RollStd;
    QString PitchStd;
    QString YawStd;
    QString InsideTemp;
    QString GpsPosState;
    QString GpsSatelNum;
    QString GpsDirectionState;
    QString WheelType;
    QString Baseline;
    QString rtkState;
    QString roadTestState;
    QString calibrateVal;
    QString heading;
    QTimer m_plattimer;
    bool m_bshowview;
};

#endif // CBDDB0BPROTOCOL_H
