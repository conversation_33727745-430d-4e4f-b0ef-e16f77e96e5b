﻿#ifndef CCSERIALPAGE_H
#define CCSERIALPAGE_H

#include <QWidget>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QMap>
#include "csseriservice.h"
#include "cprotoparamdata.h"
#include "coutlineparservice.h"

namespace Ui {
class CcserialPage;
}


/*
* 串口工具页面
*/
class CcserialPage : public QWidget
{
    Q_OBJECT

public:
    explicit CcserialPage(QWidget *parent = nullptr);
    ~CcserialPage();
    void needDisableUartParm(bool benable);
    void openOrCloseSeriPort();
    void findMsgHeadAuto(const QByteArray &barr, QByteArrayList &headlist);
    void ReadTextEditData(const QByteArray &data);
    void serialWriteData();
    bool getStatusByPortN(QString portn);
    QStringList getAvilaCom();
    void showEvent(QShowEvent *event) override;
    void setSeripageStatus();
    void setProtoParam(CProtoParamData *p){
        m_pparam = p;
    }
    QPointer<CSseriService> getServByPortN(QString portn);
    QPointer<CSseriService> getServByPortN();
    void setUpgStatus(bool);
    void restartPort();
    void catPackData(bool isNeedClear);

private:
    Ui::CcserialPage *ui;
    bool m_bIsHex;
    QMap<QString, QPointer<CSseriService>> m_seriSerMap;
    void timerEvent(QTimerEvent *e) override;
    void closeEvent(QCloseEvent *event) override;
    bool m_IsOpen;
    QByteArray m_SeriBuff;
    int m_checkSize;
    CProtoParamData *m_pparam;
    bool m_bIsAutoStart;   //离线解析状态
    QString m_sWorkFile;
    QByteArray m_lRecvshowArr;
    COutLineParService *m_olser;
    QStringList m_sComLists;
    bool m_isInValid;
public slots:
    //设置当前串口
    void slotSetPortN(const QString &text);
    //设置当前波特率
    void slotSetBaudR(const QString &text);
signals:
    //串口状态同步信号
    void sigUpgMUartP(const QString &tport, const QString &tbaud, const bool opstat);
    //串口打开信号
    void sigSeriOpen(const QString portn,const int optype, const QString filepre);
    //串口插拔信号
    void sigPortDataChange(const QStringList &comlist);
    //写串口信号
    void SigSeriWrite(const char * arr, int &len);
private slots:
    void on_bt_SeriOpen_clicked();
    void on_bt_SeriClose_clicked();
    void on_bt_SeriClear_clicked();
    void on_bt_SeriSend_clicked();
    void on_bt_catpack_clicked();
    void on_bt_import_clicked();
    void on_bt_startparse_clicked();
    //void on_bt_showData_clicked();
    void on_ch_isSaveFile_clicked();
    void on_cb_AlgorParm_currentIndexChanged(int index);
};

#endif // CCSERIALPAGE_H
