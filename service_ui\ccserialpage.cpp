﻿#include "ccserialpage.h"
#include "ui_ccserialpage.h"
#include <QDebug>
#include <QMessageBox>
#include <QMap>
#include <QList>
#include <algorithm>
#include <QShowEvent>
#include <QDir>
#include <QFileDialog>
#include <QDesktopServices>
#include "cprotoparamdata.h"

CcserialPage::CcserialPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CcserialPage)
{
    ui->setupUi(this);

    m_seriSerMap["defaut"] = new CSseriService;
    connect(ui->cb_PortN, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &CcserialPage::slotSetPortN);
    startTimer(3000);
    m_bIsAutoStart = false;

    m_olser = NULL;
    m_IsOpen = false;
    m_isInValid = false;

    FileParmSt* filest = CProtoParamData::getFileParmSt();
    if(filest->isNeedCsv){
        ui->ch_isSaveFile->setCheckState(Qt::CheckState::Checked);
    }else{
        ui->ch_isSaveFile->setCheckState(Qt::CheckState::Unchecked);
    }

    //禁用最小化按钮
    setWindowFlags(windowFlags() & ~Qt::WindowMinimizeButtonHint);
}

CcserialPage::~CcserialPage()
{
    delete ui;
}



void CcserialPage::ReadTextEditData(const QByteArray &data){
    QString portn = ui->cb_PortN->currentText();
    if(!m_seriSerMap.contains(portn)){
        if(this->isVisible()){
            QMessageBox::critical(this, tr("Error"), tr("Failed to write serial port, please check if the serial port configuration is correct! Serial port number") + ":" + portn);
        }
        qDebug()<<"Port "<<portn<<"is not invalid";
        return;
    }
    QPointer<CSseriService> seriser = m_seriSerMap[portn];
    if(seriser == NULL){
        if(this->isVisible()){
            QMessageBox::critical(this, tr("Error"), tr("Failed to write serial port, please check if the serial port configuration is correct! Serial port number") + ":" + portn);
        }
        qDebug()<<"Port "<<portn<<"is not invalid";
        return;
    }

    QByteArray bShowArr;
    if(m_SeriBuff.size() < 1024){
        m_SeriBuff.append(data);
        if(m_bIsHex){
            ui->ed_SeriRecvB->append(m_SeriBuff.toHex(' '));
        }else{
            ui->ed_SeriRecvB->append(m_SeriBuff);
        }

    }

}

void CcserialPage::serialWriteData(void){
    QString portN = ui->cb_PortN->currentText();
    if(!m_seriSerMap.contains(portN)){
        qDebug()<<"SeriPort:"<<portN<<"is not working";
        return;
    }
    QPointer<CSseriService> seriser = m_seriSerMap[portN];
    if(seriser == NULL){
        qDebug()<<"SeriPort:"<<portN<<"is not working";
        return;
    }
    QString warr = ui->ed_SeriSendB->toPlainText();
    if(m_bIsHex){
        QByteArray harr;
        for(int i = 0; i < warr.size(); i++){
            if(warr[i] == ' '){
                continue;
            }
            int num = warr.mid(i, 2).toUInt(nullptr, 16);
            ++i;
            harr.append(num);
        }
        seriser->seriWrite(harr);
    }else{
        seriser->seriWrite(warr.toLocal8Bit());
    }

}

void CcserialPage::needDisableUartParm(bool benable){
    ui->cb_BaudR->setDisabled(benable);
    ui->cb_DataB->setDisabled(benable);
    ui->cb_DataM->setDisabled(benable);
    ui->cb_PortN->setDisabled(benable);
    ui->cb_StopB->setDisabled(benable);
    ui->cb_CheckB->setDisabled(benable);
}

void CcserialPage::on_bt_SeriOpen_clicked()
{
    qDebug()<<ui->bt_SeriOpen->text()<<ui->cb_PortN->currentText()<<ui->cb_BaudR->currentText();
    QString sPortN = ui->cb_PortN->currentText();
    QPointer<CSseriService> seriser = m_seriSerMap[sPortN];
    QString btstatus = ui->bt_SeriOpen->text();
    if(tr("Open") == btstatus){
        if(seriser == NULL){
            seriser = new CSseriService;
            m_seriSerMap[sPortN] = seriser;
            connect(this, &CcserialPage::sigSeriOpen, seriser, &CSseriService::slotSerialOpenOrClose, Qt::BlockingQueuedConnection);
            connect(seriser, &CSseriService::destroyed, this, [&](QObject *obj){
                qDebug()<<"destroyed";
                //QMessageBox::information(this, u8"提示", u8"串口关闭");
                for (int i = 0;i < m_seriSerMap.size();i++) {
                    if(m_seriSerMap.values().at(i) == obj){
                        m_seriSerMap[m_seriSerMap.keys().at(i)] = NULL;
                    }
                }
                ui->bt_SeriOpen->setText(tr("Open"));
                m_IsOpen = false;
                emit sigUpgMUartP(ui->cb_PortN->currentText(), ui->cb_BaudR->currentText(), false);

            });
            connect(seriser, &CSseriService::sigReadEnd, this, [=](int code){
                QMessageBox::information(this, u8"提示", u8"SD卡文件数据读取完成!");
            });
        }
        QString sBaudR = ui->cb_BaudR->currentText();
        QString sDataB = ui->cb_DataB->currentText();
        QString sDataM = ui->cb_DataM->currentText();
        QString sStopB = ui->cb_StopB->currentText();
        QString sParity = ui->cb_CheckB->currentText();
        seriser->SerialInit(sPortN, sBaudR, sDataB, sParity, sStopB, sDataM, m_pparam);
        emit sigSeriOpen(sPortN, 0, "");
        if(!seriser->isWorking()){
            if(isVisible()){
                QMessageBox::critical(this, tr("Error"), tr("Serial port opening failed, please check if the serial port configuration is correct!"));
            }
            ui->bt_SeriOpen->setText(tr("Open"));
            m_IsOpen = false;
            return;
        }
        needDisableUartParm(true);
        //创建时就要同步一次参数
        m_pparam->informParamSig();
        ui->bt_SeriOpen->setText(tr("Close"));
        m_IsOpen = true;
    }else{
        needDisableUartParm(false);
        qDebug()<<"sigSeriOpen begin";
        emit sigSeriOpen(sPortN, 1, "");
        qDebug()<<"sigSeriOpen end";
        delete seriser;
        m_seriSerMap[sPortN] = NULL;
        seriser = NULL;
        ui->bt_SeriOpen->setText(tr("Open"));
        m_IsOpen = false;
    }
}

void CcserialPage::timerEvent(QTimerEvent *e){
    getAvilaCom();
}

void CcserialPage::on_bt_SeriClose_clicked()
{
    close();
}

void CcserialPage::on_bt_SeriClear_clicked()
{
    ui->ed_SeriRecvB->clear();
    m_SeriBuff.clear();
}

void CcserialPage::closeEvent(QCloseEvent *event){
    Q_UNUSED(event);
    needDisableUartParm(false);
    ui->ed_SeriRecvB->clear();
    m_SeriBuff.clear();

    emit sigUpgMUartP(ui->cb_PortN->currentText(), ui->cb_BaudR->currentText(), m_isInValid);
}

void CcserialPage::slotSetPortN(const QString &text){
    QPointer<CSseriService> seriser = m_seriSerMap[text];
    if(seriser == NULL || !seriser->isWorking()){
        needDisableUartParm(false);
        m_IsOpen = false;
    }else{
        needDisableUartParm(true);
        m_IsOpen = true;
    }

    ui->cb_PortN->setCurrentText(text);
}

void CcserialPage::slotSetBaudR(const QString &text){
    qDebug()<<"slotSetBaudR:"<<text;
    ui->cb_BaudR->setCurrentText(text);
}

void CcserialPage::openOrCloseSeriPort(){
    on_bt_SeriOpen_clicked();
}

/*重复太多，无法找到 报文头*/
void CcserialPage::findMsgHeadAuto(const QByteArray &barr, QByteArrayList &headlist){
    QByteArray headarr;
    //QList<QPair<QByteArray, int>> mybehmap;
    QMap<QByteArray, int> mybehmap;
    int headlen = 5;
    QByteArray zeroarr(headlen, 0x00);
    int fcount = 0;
    for(int i = 0; i < barr.size() - headlen; i++){
        headarr = barr.mid(i, headlen);
        if(!headarr.compare(zeroarr)){
            continue;
        }
        if((fcount = barr.count(headarr)) > 1){
            mybehmap[headarr] = fcount;
        }
    }

    QList<QPair<QByteArray, int>> mybehlist;
    QMap<QByteArray, int>::iterator it = mybehmap.begin();
    for (;it != mybehmap.end(); it++) {
        mybehlist.append(QPair<QByteArray, int>(it.key(), it.value()));
    }

    std::sort(mybehlist.begin(), mybehlist.end(), [](const QPair<QByteArray, int> &a, const QPair<QByteArray, int> &b){
        return a.second > b.second;
    });

    //qDebug()<<mybehmap.size()<<mybehlist.size();
    //qDebug()<<barr.toHex(' ');
    for (int i = 0;i < mybehlist.size(); i++) {
        //qDebug()<<mybehlist.at(i).first.toHex(' ')<<mybehlist.at(i).second;
        headlist.append(mybehlist.at(i).first);
    }

}

void CcserialPage::on_bt_SeriSend_clicked()
{
    QString portn = ui->cb_PortN->currentText();
    if(!m_seriSerMap.contains(portn)){
        QMessageBox::critical(this, tr("Error"), tr("Please open the serial port first!"));
        return;
    }

    QPointer<CSseriService> seriser = m_seriSerMap[portn];
    if(seriser == NULL){
        QMessageBox::critical(this, tr("Error"), tr("Please open the serial port first!"));
        qDebug()<<"Port "<<portn<<"is not invalid";
        return;
    }

    QString ssenddata = ui->ed_SeriSendB->toPlainText();

    QByteArray harr;
    if(ui->cb_DataM->currentIndex() == 0){
        for(int i = 0; i < ssenddata.size(); i++){
            if(ssenddata[i] == ' ' || ssenddata[i] == '\n'){
                continue;
            }

                int num = ssenddata.mid(i, 2).toUInt(nullptr, 16);
                ++i;
                harr.append(num);
         }
    }else{
        harr.append(ssenddata.toUtf8());
    }

    if(ui->ch_addctl->isChecked()){
        harr.append(0x0D);
        harr.append(0x0A);
    }
    //qDebug()<<"send data:"<<harr.toHex(' ');

    int index = ui->cb_writeType->currentIndex();
    if(index == 0){
        seriser->editWrite(harr);
    }else{
        connect(this, &CcserialPage::SigSeriWrite, seriser, &CSseriService::slotSeriWrite, Qt::BlockingQueuedConnection);
        int len = harr.size();
        //将buff清空
        on_bt_SeriClear_clicked();

        emit SigSeriWrite(harr, len);
        disconnect(this, &CcserialPage::SigSeriWrite, seriser, &CSseriService::slotSeriWrite);
        //抓取反馈结果
        catPackData(false);
    }
    ui->ed_SeriRecvB->append(QTime::currentTime().toString("HH:mm:ss ") + tr("send") +":" + ssenddata);
}

bool CcserialPage::getStatusByPortN(QString portn){
    if(!m_seriSerMap.contains(portn)){
        return false;
    }
    QPointer<CSseriService> seriser = m_seriSerMap[portn];
    if(seriser != NULL && seriser->isWorking()){
        return true;
    }else{
        return false;
    }
}

QStringList CcserialPage::getAvilaCom(){
    //qDebug()<<"portn1"<<&m_seriSerMap<<m_seriSerMap.first()<<m_seriSerMap["defaut"]<<m_seriSerMap.firstKey();
    if(m_seriSerMap.size() == 0){
        return QStringList("");
    }
    QStringList sportNs = m_seriSerMap["defaut"]->getAvilaCom();
    if(m_sComLists != sportNs){
        m_sComLists = sportNs;
        ui->cb_PortN->hidePopup();
        ui->cb_PortN->clear();
        ui->cb_PortN->addItems(m_sComLists);
        emit sigPortDataChange(m_sComLists);
    }
    return m_sComLists;
}

void CcserialPage::on_bt_catpack_clicked()
{
    catPackData(true);
}

void CcserialPage::catPackData(bool isNeedClear){
    QString portn = ui->cb_PortN->currentText();
    if(!m_seriSerMap.contains(portn)){
        return;
    }
    QPointer<CSseriService> seriser = m_seriSerMap[portn];
    if(seriser != NULL){
        if(isNeedClear){
            seriser->clearBuff();
        }
        QTimer::singleShot(1000, [=]() { // 延迟 1 秒后执行
            QString headstr = QTime::currentTime().toString("HH:mm:ss ") + tr("Recv") + ":";
            if(ui->cb_DataM->currentIndex() == 0){
                ui->ed_SeriRecvB->append(headstr + seriser->getCurrentBuff().toHex(' '));
            }else{
                ui->ed_SeriRecvB->append(headstr + QString::fromLocal8Bit(seriser->getCurrentBuff().replace(0x00, 0x20)));
            }
        });
    }
}

void CcserialPage::showEvent(QShowEvent *event){
    needDisableUartParm(m_IsOpen);
}

void CcserialPage::setSeripageStatus(){
    QString sportN = ui->cb_PortN->currentText();
    if(getStatusByPortN(sportN)){
        ui->bt_SeriOpen->setText(tr("Close"));
    }else{
        ui->bt_SeriOpen->setText(tr("Open"));
    }
}

QPointer<CSseriService> CcserialPage::getServByPortN(QString portn){
    //qDebug()<<"portn"<<portn<<&m_seriSerMap;
    if(m_seriSerMap.contains(portn)){
        return m_seriSerMap[portn];
    }else{
        return NULL;
    }
}

QPointer<CSseriService> CcserialPage::getServByPortN(){
    if(m_seriSerMap.size() > 0){
        for (int i = 0;i < m_seriSerMap.size();i++) {
            if(m_seriSerMap.values().at(i) != NULL && m_seriSerMap.values().at(i)->isWorking()){
                return m_seriSerMap.values().at(i);
            }
        }
        return NULL;
    }else{
        return NULL;
    }
}


void CcserialPage::on_bt_import_clicked()
{
    QString currentDir = QDir::currentPath();
    m_sWorkFile = QFileDialog::getOpenFileName(this, tr("Open binary data file"), currentDir, tr("Dat files (*.dat)"));
    if(!m_sWorkFile.isEmpty()){
        ui->le_filename->setText(m_sWorkFile);
    }
}

void CcserialPage::on_bt_startparse_clicked()
{
    static qint64 read_num = 0L;
    m_sWorkFile = ui->le_filename->text();

    if(m_bIsAutoStart && m_olser != NULL){
        m_bIsAutoStart = false;
        m_olser->stopWorking();
        m_olser = NULL;
        ui->bt_startparse->setText(tr("Start parsing"));
        ui->bt_import->setDisabled(false);
        read_num = 0L;
        return;
    }

    m_bIsAutoStart = true;

    m_olser = new COutLineParService;
    m_olser->setParm(m_pparam);
    connect(m_olser, &COutLineParService::sigFileReadEnd, this, [=](QString resinfo){
        QMessageBox::information(this, tr("File read completed"), resinfo);
        m_olser->deleteLater();
        m_olser = NULL;
        read_num = 0L;
        m_bIsAutoStart = false;
        ui->bt_SeriOpen->setDisabled(false);
        ui->bt_startparse->setText(tr("Start parsing"));
        ui->bt_import->setDisabled(false);
    });
    connect(m_olser, &COutLineParService::sigDataRead, this, [&](QByteArray dataarr){
        int data_num = dataarr.size();
        if(read_num == 0){
            read_num += data_num;
            ui->ed_SeriRecvB->append(tr("Read byte count") + QString("%1:").arg(data_num));
            ui->ed_SeriRecvB->append(dataarr.toHex(' '));
        }else{
            read_num += data_num;
            ui->ed_SeriRecvB->append(tr("Read byte count") + QString("%1").arg(data_num) + tr("Total number of bytes read") + QString("%2").arg(read_num));
            //qDebug()<<ui->ed_SeriRecvB->document()->lineCount();
        }
        //m_lRecvshowArr.append(dataarr);
        //if(m_lRecvshowArr.size() > 20480){
            //ui->ed_SeriRecvB->clear();
        //    m_lRecvshowArr.clear();
        //}
    });
    if(!m_olser->fileReadData(m_sWorkFile)){
        return;
    }

    m_pparam->informParamSig();
    ui->bt_startparse->setText(tr("Stop parsing"));
    ui->bt_import->setDisabled(true);
}

void CcserialPage::setUpgStatus(bool stat){
    ui->bt_SeriOpen->setDisabled(stat);
    //当串口操作状态更新为可用时，重新打开串口
    emit sigUpgMUartP(ui->cb_PortN->currentText(), ui->cb_BaudR->currentText(), stat);
    m_isInValid = stat;
}


//void CcserialPage::on_bt_showData_clicked()
//{
//    QUrl folderUrl = QUrl::fromLocalFile(QDir::currentPath());
//
//    // 打开文件夹
//    if (!QDesktopServices::openUrl(folderUrl)) {
//        // 处理打开文件夹失败的情况
//    }
//    close();
//}

void CcserialPage::on_ch_isSaveFile_clicked()
{
    FileParmSt* filest = CProtoParamData::getFileParmSt();
    if(ui->ch_isSaveFile->isChecked()){
        filest->isNeedCsv = true;
    }else{
        filest->isNeedCsv = false;
    }
}

//重启串口
void CcserialPage::restartPort(){
    QString sPortN = ui->cb_PortN->currentText();
    //  释放当前串口对象
    if(m_seriSerMap.contains(sPortN) && m_seriSerMap[sPortN] != NULL){
         m_seriSerMap[sPortN]->deleteLater();
        //delete m_seriSerMap[sPortN];
    }
    //重新打开串口
    m_seriSerMap[sPortN] = NULL;
    ui->bt_SeriOpen->setText(tr("Open"));
    openOrCloseSeriPort();
    //同步串口打开状态
    emit sigUpgMUartP(ui->cb_PortN->currentText(), ui->cb_BaudR->currentText(), false);
}

void CcserialPage::on_cb_AlgorParm_currentIndexChanged(int index)
{
    switch (index) {
    case 0:
        ui->ed_SeriSendB->clear();
        break;
    case 1://读取配置
        ui->ed_SeriSendB->setText("$ALGO:readConfig");
        break;
    case 2://重启算法
        ui->ed_SeriSendB->setText("$ALGO:restart");
        break;
    case 3://开启标定状态
        ui->ed_SeriSendB->setText("$ALGO:writeConfig,Adj_Nav_Standard_flag,2");
        break;
    case 4://取消标定状态
        ui->ed_SeriSendB->setText("$ALGO:writeConfig,Adj_Nav_Standard_flag,0");
    default:
        break;
    }
    ui->ch_addctl->setChecked(true);
    ui->cb_writeType->setCurrentIndex(1);
    ui->cb_DataM->setCurrentIndex(1);
}
