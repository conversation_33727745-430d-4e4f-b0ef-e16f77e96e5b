﻿#include "cloadfiletaskdlg.h"
#include <QFile>
#include <QDebug>
#include <QPushButton>
#include "Cali.h"
#include <QElapsedTimer>

CLoadFileTaskDlg::CLoadFileTaskDlg(QWidget *parent) : QDialog(parent)
{
      qDebug()<<"CLoadFileTaskDlg xx";
      resize(300, 100);
      //move(parent->width()/2, parent->height()/2);
      setWindowFlags(Qt::Dialog|Qt::FramelessWindowHint);
      setAttribute(Qt::WA_TranslucentBackground);
      qRegisterMetaType<QVector<int>>("QVector<int>");
      qRegisterMetaType<QVector<QList<double> *>>("QVector<QList<double> *>");
      qRegisterMetaType<QList<QString> *>("QList<QString> *");
      qRegisterMetaType<QList<double> *>("QList<double> *");
      setModal(true);
      m_thloadthread = new QThread();
      m_thloadthread->start();
      m_pgLoadProc = new QProgressBar(this);
      QFont font;
      font.setPixelSize(20);
      m_pgLoadProc->setFont(QFont("微软雅黑", 20));
      m_pgLoadProc->resize(300, 50);
      QPushButton *btcancel = new QPushButton(this);
      btcancel->resize(80, 30);
      btcancel->move(110, 60);
      btcancel->setText(tr("Cancel"));
      m_pgLoadProc->setValue(0);
      m_LoadTask = new CLoadFileTask;
      m_LoadTask->moveToThread(m_thloadthread);
      connect(btcancel, &QPushButton::clicked, this, [=](){
          m_LoadTask->stopLoad();
          this->close();
      });
      connect(m_LoadTask, &CLoadFileTask::sigUpgValue, m_pgLoadProc, &QProgressBar::setValue);
      connect(this, &CLoadFileTaskDlg::sigLoadCvsFile, m_LoadTask, &CLoadFileTask::slotLoadCvsFile);
      connect(this, &CLoadFileTaskDlg::sigLoadFileAvg, m_LoadTask, &CLoadFileTask::slotLoadFileAvg);
      connect(this, &CLoadFileTaskDlg::sigLoadMulFileAvg, m_LoadTask, &CLoadFileTask::slotLoadMulFileAvg);
      connect(m_pgLoadProc, &QProgressBar::valueChanged, this, [=](int value){
          if(value == 100){
              emit sigReadFileEnd();
              close();
          }
      });

}

/*读取一个文件的一个或者多个字段*/
void CLoadFileTask::slotLoadCvsFile(const QString &filename,const QVector<int> vindex, QVector<QList<double> *> dlist){
    QFile file(filename);
    if(!file.open(QIODevice::ReadOnly)){
        return;
    }

    QElapsedTimer t;
    t.start();

    qint64 filesize =  file.size();
    qDebug()<<"slotLoadCvsFile"<<filesize<<QThread::currentThreadId()<<vindex<<t.elapsed();
    qint64 readsize = 0;

    //去除第一行
    QString readarr = file.readLine();
    int readlnum = 0;
    QStringList keysread;
    do {
        readarr = file.readLine();
        if(readarr.size() == 0){
            break;
        }
        readsize += readarr.size();
        //去除开头和最后一个双引号和换行符
        if(readarr[0] == '\"'){
            readarr[0] = ' ';
        }
        int endpos = readarr.lastIndexOf('\"');
        if(endpos > 1){
            readarr.resize(endpos);
        }
        readarr.remove(':');
        keysread = readarr.split("\",\"");
        if(keysread.size() < 2){
            keysread = readarr.split(',');
        }
        if(keysread.size() < 2){
            continue;
        }
        //keysread.removeAll("\"");
        //keysread.removeAll(":");
        //keysread.removeAll("\n");
        for(int i = 0; i < vindex.size(); i++){
            if(vindex[i] < keysread.size()){
                //qDebug()<<"vindex[i]"<<vindex.size()<<keysread.at(vindex[i]);
                dlist[i]->append(keysread.at(vindex[i]).toDouble());
            }
        }
        emit sigUpgValue(readsize * 100/filesize);
        readlnum++;
        //if(readlnum > 2010000){
        //    break;
        //}
    } while (readarr.size() != 0 && !m_bStop);
    qDebug()<<"readsize:"<<readsize<<readlnum<<t.elapsed();
    qDebug()<<"first val:"<<dlist[0]->at(0);
    file.close();
    emit sigUpgValue(100);
}

/*读取单个文件的陀螺数据并计算平均值*/
void CLoadFileTask::slotLoadFileAvg(const QString &filename,const QVector<int> vindex, int ifrq, QVector<QList<double> *> dlist){
    QFile file(filename);
    if(!file.open(QIODevice::ReadOnly)){
        return;
    }

    qint64 filesize =  file.size();
    qDebug()<<"slotLoadCvsFile"<<filesize<<QThread::currentThreadId();
    qint64 readsize = 0;

    //去除第一行
    QString readarr = file.readLine();
    int readlnum = 0;
    qint64 readnum = 0;
    qint64 resnum = 0;
    int index = 0;
    QStringList keysread;
    QVector<double> totalval(vindex.size(), 0.000);
    do {
        readarr = file.readLine();
        if(readarr.size() == 0){
            break;
        }
        readsize += readarr.size();
        //去除开头和最后一个双引号和换行符
        if(readarr[0] == '\"'){
            readarr[0] = ' ';
        }
        int endpos = readarr.lastIndexOf('\"');
        if(endpos > 1){
            readarr.resize(endpos);
        }
        readarr.remove(':');
        keysread = readarr.split("\",\"");
        if(keysread.size() < 2){
            keysread = readarr.split(',');
        }
        if(keysread.size() < 2){
            continue;
        }

        index = 0;
        //将需要计算平滑的字段累加
        for(int i = 0; i < keysread.size(); i++){
            if(vindex[i] < keysread.size()){
                double dval = keysread.at(vindex[i]).toDouble();
                if(typeid (dval) != typeid (double)){
                    //qDebug()<<keysread.at(vindex[i]);
                }else{
                    totalval[index++] += dval;
                }
            }
        }
        //将累加后的数据取平均
        if(++readnum % ifrq == 0){
            for(int k = 0; k < totalval.size(); k++){
                dlist[k]->append(totalval.at(k) / ifrq);
                totalval[k] = 0.0000;
            }
        }

        emit sigUpgValue(readsize * 100/filesize);
    } while (readarr.size() != 0 && !m_bStop);
    qDebug()<<"readsize:"<<readsize<<readlnum;
    file.close();
    emit sigUpgValue(100);
}

/*读取多个文件的同一个字段并计算平均值*/
void CLoadFileTask::slotLoadMulFileAvg(const QStringList &filenamelist,const int dataindex, QList<QString> *vdspeeds, \
                                    QList<double> *vddata, QList<QString> *vsFilename){
    qDebug()<<"slotLoadMulFile:"<<vdspeeds<<vddata;
    int filenum = filenamelist.size();
    double allnum = 0;
    for(int i = 0; i < filenum; i++){
        allnum = 0;
        QString filename = filenamelist.at(i);
        QFile file(filename);
        if(!file.open(QIODevice::ReadOnly)){
            qDebug()<<"file open fail:"<<filename<<file.errorString();
            return;
        }

        QString readarr = file.readLine();
        int readlnum = 0;
        do {
            //去除开头和最后一个双引号和换行符
            if(readarr[0] == '\"'){
                readarr[0] = ' ';
            }
            int endpos = readarr.lastIndexOf('\"');
            if(endpos > 1){
                readarr.resize(endpos);
            }
            readarr.remove(':');
            auto keysread = readarr.split("\",\"");
            if(keysread.size() < 2){
                keysread = readarr.split(',');
            }
            readlnum++;
            allnum += keysread.at(dataindex).toDouble();
            readarr = file.readLine();
        } while (readarr.size() != 0 && !m_bStop);
        file.close();
        vsFilename->push_back(filename);
        QStringList liststr = filename.replace(".csv", "").split('_');
        if(liststr.size() > 2){
            vdspeeds->push_back(liststr.at(1));
        }else{
            vdspeeds->push_back("0");
        }
        //qDebug()<<"readlnum:"<< QString("%1").arg(allnum, 0, 'f', 4)<<readlnum<<QString("%1").arg(allnum/readlnum, 0, 'f', 4);
        vddata->push_back(allnum/readlnum);
        emit sigUpgValue(i * 100/ filenum);

        if(m_bStop){
            break;
        }
    }
    qDebug()<<"vdspeeds"<<vdspeeds<<vddata;
    emit sigUpgValue(100);
}


//陀螺仪标度因数相关计算
void CCalculateTask::slotBiasRelation(QVector<double> &w, QVector<double> &F_plus, QVector<double> &F_minus, double &dreslut){
    double *dw = w.data();
    int len = w.size();
    double *dplus = F_plus.data();
    double *dminus = F_minus.data();
    //::Gyro_Scale(dw, len, dplus, dminus)
}

//陀螺仪零偏相关计算
void CCalculateTask::slotBiasRelation(QVector<double> &w, double k,double &dreslut){
    double *dw = w.data();
    int len = w.size();
    dreslut = ::Bias_Stability(dw, len, 1, 1, k);
}

//陀螺仪标定函数
void CCalculateTask::slotGyroCali(){

}
//加速度计标定函数
void CCalculateTask::slotAccCali(){

}

/*void CCalculateTask::GetAxisLst(SdkGyroData sdkData,string strAxis,List<double> speedLst,List<double> plusLst,List<double> minuxLst, double &fr)
{
    List<ScaleItemInfo> axisLst = sdkData.scaleItemInfoLst.Where(x => x.Axis == strAxis).ToList();
    foreach(var itemInfo in axisLst)
    {
        if (itemInfo.Speed == 0)
        {
            if (strAxis == "x")
            {
                fr = (itemInfo.xPlus + itemInfo.xMinus) / 2;
            }
            if (strAxis == "y")
            {
                fr = (itemInfo.yPlus + itemInfo.yMinus) / 2;
            }
            if (strAxis == "z")
            {
                fr = (itemInfo.zPlus + itemInfo.zMinus) / 2;
            }
        }
        else
        {
            speedLst.Add(itemInfo.Speed);
            if (strAxis == "x")
            {
                plusLst.Add(itemInfo.xPlus);
                minuxLst.Add(itemInfo.xMinus);
            }
            if (strAxis == "y")
            {
                plusLst.Add(itemInfo.yPlus);
                minuxLst.Add(itemInfo.yMinus);
            }
            if (strAxis == "z")
            {
                plusLst.Add(itemInfo.zPlus);
                minuxLst.Add(itemInfo.zMinus);
            }
        }
    }
}
*/
