﻿#ifndef CDRIVETESTPAGE_H
#define CDRIVETESTPAGE_H

#include <QMainWindow>
#include <QLabel>
#include <QListWidget>
#include <QVector>

namespace Ui {
class CDriveTestPage;
}

/*
* 多口调试页面
*/
class CDriveTestPage : public QMainWindow
{
    Q_OBJECT

public:
    explicit CDriveTestPage(QWidget *parent = nullptr);
    ~CDriveTestPage();
    void showEvent(QShowEvent *event) override;
public slots:
    void slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);

private:
    Ui::CDriveTestPage *ui;
    QMap<QString, QDockWidget *> m_dwdMap;
    QMap<QString, QListWidget *> m_lwdMap;
};

#endif // CDRIVETESTPAGE_H
