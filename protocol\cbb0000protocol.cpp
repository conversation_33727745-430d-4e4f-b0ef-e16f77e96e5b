﻿#include "cbb0000protocol.h"
#include <QDebug>
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "csubpagemanager.h"
#include "cprotocolfactory.h"
#include <QtMath>

Cbb0000Protocol::Cbb0000Protocol(QObject *parent) : CBaseProtocol(parent)
{

    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
    CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
   if(dripage != NULL){
       qDebug()<<"connect Cbb0000Protocol";
       connect(this, &Cbb0000Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &Cbb0000Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
       connect(this, &Cbb0000Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
   }

   m_iLossFrameSum = 0;
   m_iCheckFailSum = 0;

   m_iFramIndx = -1;
   setFrameErr(E_FRAME_OK);
   m_lcatCurrCounts = 0l;

}

bool Cbb0000Protocol::setProtoLength(const QByteArray &barr){
    m_slProtoKeys =  CProtocolFactory::getProKeys(m_iProtoIndex);
    m_slFreqKeys.append(m_slProtoKeys.at(9));
    m_slFreqKeys.append(m_slProtoKeys.at(10));
    m_slFreqKeys.append(m_slProtoKeys.at(11));
    m_slFreqKeys.append(m_slProtoKeys.at(12));
    m_slFreqKeys.append(m_slProtoKeys.at(13));
    m_slFreqKeys.append(m_slProtoKeys.at(14));
    m_slFreqKeys.append(m_slProtoKeys.at(15));
    m_slFreqKeys.append(m_slProtoKeys.at(16));
    m_slFreqKeys.append(m_slProtoKeys.at(17));
    m_bMsgHead = barr.mid(0, 4);
    m_uMsgLen = (unsigned char)barr.at(4) + ((unsigned char)barr.at(5) << 8);

    if(m_uMsgLen != sizeof(FmcDataInfo)){
        qDebug()<<"length parse error setProtoLength:"<<m_uMsgLen<<sizeof(FmcDataInfo);
        m_uMsgLen = sizeof(FmcDataInfo);
        return false;
    }

    m_vScale.insert(0, m_slProtoKeys.size() - 6, 1.0);
    m_vScale.insert(67, 6, qPow(10, 16));

    //0-char 1-short 2-unsigned short 3-int 4-unsigned int 5-float 6-double

    //m_vScaleType = {0, 2, 3, }

    return true;
}

bool Cbb0000Protocol::preInit(){
    CBaseProtocol::preInit();
    qDebug()<<"m_catFreqCounts"<<m_icatFreqCounts<<m_dCatFreq<<m_dGyrFreq;
    emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") +QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABEXCEPT"), QStringList(tr("Except") + ":0"), m_sPortN);
    return true;
}

void Cbb0000Protocol::paseMsg(const QByteArray msg){
    FmcDataInfo st_bb0000;

    QStringList dataValues;
    QByteArray writeBytes;
    //qDebug()<<sizeof (st_bb0000)<<msg.size()<<m_uMsgLen;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(msg.size() < m_uMsgLen){ //254
        qDebug()<<"paseMsg fail,msg len is invalid:"<<msg.size()<<msg.toHex(' ');
        return;
    }

    ::memcpy(&st_bb0000, msg.data(), sizeof(FmcDataInfo));

    m_iReqCount++;

    //uint16_t currentindx = st_bb0000.selftestingcode >> 8;
    uint16_t currentindx = st_bb0000.selftestingcode;
    //qDebug()<<"code:"<<st_aa6600.st921info.selftestingcode;
    //if(m_iFramIndx != -1 && ((m_iFramIndx + 1) % m_iFpgaFc != currentindx)){
    if(!lossFrameCheck(st_bb0000.selftestingcode)){
        qDebug()<<QTime::currentTime().toString()<<"lost frame:"<< m_iFramIndx<<m_sPortN;
        qDebug()<< m_sMsgCache.toHex();
        qDebug()<< msg.toHex();
        m_iLossFrameSum++;
        setFrameErr(E_FRAME_LOSS);
        if(m_bIsNeedShowL){
            emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") +QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
            m_bIsNeedShowL = false;
        }
    }

    //if(!sum16CheckSum(msg)){
    if(!sum8to16CheckSum(msg, 0, 2)){
        qDebug()<<QTime::currentTime().toString()<<"sum16CheckSum error";
        qDebug()<<msg.toHex();
        m_iCheckFailSum++;
        setFrameErr(E_FRAME_CHECK);
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        //return;
    }

    if(m_iGnsstime > 0 && m_iGnsstime > st_bb0000.secondofweek){
        emit sigStatuUpdate(QStringList("LABEXCEPT"), QStringList(tr("Except") + ":1"), m_sPortN);
        m_bIsNeedShowC = false;
    }
    m_iGnsstime = st_bb0000.secondofweek;

    m_sMsgCache.clear();
    m_sMsgCache.append(msg);
    //m_iFramIndx = currentindx;

    dataValues.append(QString::number(currentindx));
    dataValues.append(QString::number(st_bb0000.fpgaversion));
    dataValues.append(QString::number(st_bb0000.watchversion));
    dataValues.append(QString::number(st_bb0000.Xgears));
    dataValues.append(QString::number(st_bb0000.Xflwheelspeed));
    dataValues.append(QString::number(st_bb0000.Xfrwheelspeed));
    dataValues.append(QString::number(st_bb0000.Xblwheelspeed));
    dataValues.append(QString::number(st_bb0000.Xbrwheelspeed));
    dataValues.append(QString::number(st_bb0000.Xcaninfocounter));
    dataValues.append(QString::number(st_bb0000.fogx, 'f', 6));
    dataValues.append(QString::number(st_bb0000.fogy, 'f', 6));
    dataValues.append(QString::number(st_bb0000.fogz, 'f', 6));
    dataValues.append(QString::number(st_bb0000.fogtemperaturex));
    dataValues.append(QString::number(st_bb0000.fogtemperaturey));
    dataValues.append(QString::number(st_bb0000.fogtemperaturez));
    dataValues.append(QString::number(st_bb0000.accelerometerx, 'f', 6));
    dataValues.append(QString::number(st_bb0000.accelerometery, 'f', 6));
    dataValues.append(QString::number(st_bb0000.accelerometerz, 'f', 6));
    dataValues.append(QString::number(st_bb0000.accelerometertemp));
    dataValues.append(QString::number(st_bb0000.reserve1e));
    dataValues.append(QString::number(st_bb0000.reserve1f));
    dataValues.append(QString::number(st_bb0000.Reserve20));
    dataValues.append(QString::number(st_bb0000.gnssweek));
    dataValues.append(QString::number(st_bb0000.millisecondofweek));
    dataValues.append(QString::number(st_bb0000.secondofweek));
    dataValues.append(QString::number(st_bb0000.ppsdelay10ns));
    dataValues.append(QString::number(st_bb0000.gpsstarnumber));
    dataValues.append(QString::number((st_bb0000.rtkstatus & 0xFF00) >> 8));
    dataValues.append(QString::number(st_bb0000.speedstatus));
    st_bb0000.truenorthtrack[4] = st_bb0000.truenorthtrack[5];
    st_bb0000.truenorthtrack[5] = '\0';
    QByteArray newdata;
    newdata.append(st_bb0000.truenorthtrack[4]);
    newdata.append(st_bb0000.truenorthtrack[3]);
    newdata.append(st_bb0000.truenorthtrack[2]);
    newdata.append(st_bb0000.truenorthtrack[1]);
    newdata.append(st_bb0000.truenorthtrack[0]);
    //dataValues.append(newdata.toHex(' '));
    dataValues.append(newdata);
    //qDebug()<<st_bb0000.northvelocity<<QString::number(st_bb0000.northvelocity);
    dataValues.append(QString::number(st_bb0000.northvelocity));
    dataValues.append(QString::number(st_bb0000.eastvelocity));
    dataValues.append(QString::number(st_bb0000.upvelocity));
    dataValues.append(CTlhTools::char2String(st_bb0000.GnssStaDirLat >> 8 & 0xFF));
    dataValues.append(CTlhTools::char2String(st_bb0000.GnssStaDirLat & 0xFF));
    dataValues.append(QString::number(st_bb0000.latitude));
    dataValues.append(CTlhTools::char2String(st_bb0000.DirLonHeadingSta >> 8 & 0xFF));
    dataValues.append(QString::number(st_bb0000.longitude));
    dataValues.append(QString::number(st_bb0000.altitude));
    dataValues.append(QString::number(st_bb0000.DirLonHeadingSta & 0xFF));
    dataValues.append(QString::number(st_bb0000.baselength));
    dataValues.append(QString::number(st_bb0000.roll));
    dataValues.append(QString::number(st_bb0000.pitch));
    dataValues.append(QString::number(st_bb0000.yaw));
    dataValues.append(QString::number(st_bb0000.gears));
    dataValues.append(QString::number(st_bb0000.caninfocounter));
    dataValues.append(QString::number(st_bb0000.flwheelspeed));
    dataValues.append(QString::number(st_bb0000.frwheelspeed));
    dataValues.append(QString::number(st_bb0000.blwheelspeed));
    dataValues.append(QString::number(st_bb0000.brwheelspeed));
    dataValues.append(QString::number(st_bb0000.timeprecisionZ));
    dataValues.append(QString::number(st_bb0000.verticalprecZ));
    dataValues.append(QString::number(st_bb0000.horizontalprecZ));
    dataValues.append(QString::number(st_bb0000.northprecisionZ));
    dataValues.append(QString::number(st_bb0000.eastprecisionZ));
    dataValues.append(QString::number(st_bb0000.endheightangleZ));
    dataValues.append(QString::number(st_bb0000.StanDeviat_Lat, 'f', 5));
    dataValues.append(QString::number(st_bb0000.StanDeviat_Lon, 'f', 5));
    dataValues.append(QString::number(st_bb0000.StanDeviat_Alt, 'f', 5));
    dataValues.append(QString::number(st_bb0000.StanDeviat_Heading, 'f', 5));
    dataValues.append(QString::number(st_bb0000.StanDeviat_Pitch, 'f', 5));
    dataValues.append(QString::number(st_bb0000.Sol_Status));
    dataValues.append(QString::number(st_bb0000.Pos_Type));
    dataValues.append(QString::number(st_bb0000.checksum));
    dataValues.append(QString::number(st_bb0000.frameindex));
    dataValues.append(QString::number(st_bb0000.Alongitude));
    dataValues.append(QString::number(st_bb0000.Alatitude));
    dataValues.append(QString::number(st_bb0000.Aaltitude));
    dataValues.append(QString::number(st_bb0000.Ave));
    dataValues.append(QString::number(st_bb0000.Avn));
    dataValues.append(QString::number(st_bb0000.Avu));
    dataValues.append(QString::number(st_bb0000.Apitch));
    dataValues.append(QString::number(st_bb0000.Aroll));
    dataValues.append(QString::number(st_bb0000.Aheading));
    dataValues.append(QString::number(st_bb0000.cali_gyrox, 'f', 6));
    dataValues.append(QString::number(st_bb0000.cali_gyroy, 'f', 6));
    dataValues.append(QString::number(st_bb0000.cali_gyroz, 'f', 6));
    dataValues.append(QString::number(st_bb0000.cali_accelx, 'f', 6));
    dataValues.append(QString::number(st_bb0000.cali_accely, 'f', 6));
    dataValues.append(QString::number(st_bb0000.cali_accelz, 'f', 6));
    dataValues.append(QString::number(st_bb0000.fpga_internum));
    dataValues.append(QString::number(st_bb0000.packnum));
    //dataValues.append(QString(st_bb1db.sys_stat));

    dataValues.append(getSysStatDesByC(st_bb0000.sys_stat[0]));

    if(m_icatFreqCounts != 0){
        m_vGyrDatasX.append(st_bb0000.fogx);
        m_vGyrDatasY.append(st_bb0000.fogy);
        m_vGyrDatasZ.append(st_bb0000.fogz);
        m_vTempDatasX.append(st_bb0000.fogtemperaturex);
        m_vTempDatasY.append(st_bb0000.fogtemperaturey);
        m_vTempDatasZ.append(st_bb0000.fogtemperaturez);
        m_vCaliDatasX.append(st_bb0000.accelerometerx);
        m_vCaliDatasY.append(st_bb0000.accelerometery);
        m_vCaliDatasZ.append(st_bb0000.accelerometerz);
        if(++m_lcatCurrCounts % m_icatFreqCounts == 0){
            double davggyrX = CTlhTools::getAverage(m_vGyrDatasX);
            double davggyrY = CTlhTools::getAverage(m_vGyrDatasY);
            double davggyrZ = CTlhTools::getAverage(m_vGyrDatasZ);
            double davgtempX = CTlhTools::getAverage(m_vTempDatasX);
            double davgtempY = CTlhTools::getAverage(m_vTempDatasY);
            double davgtempZ = CTlhTools::getAverage(m_vTempDatasZ);
            double davgcaliX = CTlhTools::getAverage(m_vCaliDatasX);
            double davgcaliY = CTlhTools::getAverage(m_vCaliDatasY);
            double davgcaliZ = CTlhTools::getAverage(m_vCaliDatasZ);
            QStringList slwriteval;
            slwriteval.append(QString::number(davggyrX ));
            slwriteval.append(QString::number(davggyrY ));
            slwriteval.append(QString::number(davggyrZ ));
            slwriteval.append(QString::number(davgtempX));
            slwriteval.append(QString::number(davgtempY));
            slwriteval.append(QString::number(davgtempZ));
            slwriteval.append(QString::number(davgcaliX));
            slwriteval.append(QString::number(davgcaliY));
            slwriteval.append(QString::number(davgcaliZ));
            //qDebug()<<slwriteval;
            writeCvsFile("BB00_C" + QString::number(m_icatFreqCounts), m_slFreqKeys, slwriteval, 1);
            m_vGyrDatasX.clear();
            m_vGyrDatasY.clear();
            m_vGyrDatasZ.clear();
            m_vTempDatasX.clear();
            m_vTempDatasY.clear();
            m_vTempDatasZ.clear();
            m_vCaliDatasX.clear();
            m_vCaliDatasY.clear();
            m_vCaliDatasZ.clear();
        }
    }

    writeCvsFile("BB00", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        //qDebug()<<"time:"<<QTime::currentTime().toString()<<stime;
        if(m_bIsNeedKeys){
            emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        }else{
            emit sigDataUpdate(QStringList(), dataValues, m_sPortN, m_iProtoIndex);
        }

        m_bIsNeedShow = false;
    }

    //采集IMU数据， 并生成二进制文件
    QDataStream stream(&writeBytes, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);
    int ival = 0;

    if(m_bIsNeedDat){
        if(m_iDatType == 1){
            stream << st_bb0000.millisecondofweek / 1000.0;
            ival = st_bb0000.cali_gyrox * qPow(10, 7);
            stream << ival;
            ival = st_bb0000.cali_gyroy * qPow(10, 7);
            stream << ival;
            ival = st_bb0000.cali_gyroz * qPow(10, 7);
            stream << ival;
            ival = st_bb0000.cali_accelx * qPow(10, 7);
            stream << ival;
            ival = st_bb0000.cali_accelx * qPow(10, 7);
            stream << ival;
            ival = st_bb0000.cali_accelx * qPow(10, 7);
            stream << ival;
            qDebug()<<"writeBytes:"<<writeBytes.toHex(' ') << st_bb0000.cali_gyrox << st_bb0000.cali_gyrox * qPow(10, 16) << ival;
            writeDatFile("BB00", writeBytes);
        }else{
            writeDatFile("BB00",  msg);
        }
    }
}
