﻿#ifndef CVERTICALSCALWIDGET_H
#define CVERTICALSCALWIDGET_H

#include <QWidget>
#include <QPainter>
#include <QTimer>
#include <QLinearGradient>
#include <cmath>
#include <QDebug>

/*
* 垂直刻度尺绘制页面
*/
class CVerticalScalWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CVerticalScalWidget(QWidget *parent = nullptr);
    void drawAltitudeScale(QPainter &painter);
    void paintEvent(QPaintEvent *event) override;
    void setBackColor(QColor sc, QColor ec);
    void setDirect(int direct);
    void setCurrentVal(float val);
signals:
private:
private:
    float m_fcurrentval; // 当前数值
    int idirect;  //对齐方式 0-靠右 1- 靠左
    QColor startBkColor;
    QColor endBkColor;
    QColor centerBkColor;

};

#endif // CVERTICALSCALWIDGET_H
