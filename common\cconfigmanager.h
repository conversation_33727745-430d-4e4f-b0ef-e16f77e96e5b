﻿#ifndef CCONFIGMANAGER_H
#define CCONFIGMANAGER_H

#include <QObject>
#include <QSettings>

#define G_CONFIG CConfigManager::getInstance()
#define VERSION "V1.0.8m"


/*
*配置文件管理类
*/
class CConfigManager:public QObject
{
    Q_OBJECT
public:
    static CConfigManager& getInstance() {
           static CConfigManager instance;
           return instance;
       }
    QVariant getValue(QString);
    void setValue(QString key, QString val);
    /*加载配置*/
    bool loadConfig(QString &erroinfo);
    /*保存配置*/
    void saveConfig();
    /*加载地图配置*/
    void loadMapConfig();
    /*保存地图配置*/
    void saveMapConfig();
    /*加载设备配置*/
    void loadDevConfig();
    /*保存设备配置*/
    void saveDevConfig();
    /*保存文件配置*/
    void saveFileConfig();
    /*加载文件配置*/
    bool loadFileConfig(QString &erroinfo);
    CConfigManager();
    virtual ~CConfigManager() {}
    CConfigManager(const CConfigManager&) = delete;
    CConfigManager& operator=(const CConfigManager&) = delete;

private:
    QString m_sCurrentDir;
    QString m_cFcommon;
    QSettings *m_spComonSet;
};

#endif // CCONFIGMANAGER_H
