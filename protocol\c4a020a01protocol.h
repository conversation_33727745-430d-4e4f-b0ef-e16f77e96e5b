﻿#ifndef C4A020A01PROTOCOL_H
#define C4A020A01PROTOCOL_H

#include <QObject>
#include "cbaseprotocol.h"

class C4A020A01Protocol : public CBaseProtocol
{
    Q_OBJECT
public:
    explicit C4A020A01Protocol(QObject *parent = nullptr);
    bool setProtoLength(const QByteArray &barr);
    void paseMsg(const QByteArray msg);
private:
#pragma pack(push)
#pragma pack(1)
    typedef struct _Stru4a020A{
        unsigned short head;
        unsigned short cmd;
        unsigned int ttemp;
        float accel_x;
        float accel_y;
        float accel_z;
        float gyro_x;
        float gyro_y;
        float gyro_z;
        float temp_UNO;
        unsigned char checksum;
    }Stru4a020a;
#pragma pack(pop)
signals:
    void sigDataUpdate(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex);
    //void sigStatuUpdate(const QStringList &statuskeys, const QStringList statusValues, const QString &sportN);

signals:

};

#endif // C4A020A01PROTOCOL_H
