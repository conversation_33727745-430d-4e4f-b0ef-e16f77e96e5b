﻿#include "cplaybackwidget.h"
#include "ui_cplaybackwidget.h"
#include "geturl.h"

CPlaybackWidget::CPlaybackWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CPlaybackWidget)
{
    ui->setupUi(this);
    CScalebarLabel *lab = ui->lb_scalelb;


    ui->bt_startbt->setStyleSheet("border:Opx");
    ui->bt_startbt->setIconSize(QSize(ui->bt_startbt->width(),ui->bt_startbt->height()));
    QPixmap pixmap(":/img/startplaynew.png");
    ui->bt_startbt->setIcon(QIcon(pixmap));

    m_isPlay = false;

    QFont font;
    font.setFamily("Times New Roman");  // 设置字体名称
    font.setPointSize(10);             // 设置字体大小
    font.setBold(true);                // 设置字体加粗
    ui->lb_currentnum->setFont(font);
    ui->lb_totalnum->setFont(font);
    ui->lb_currentnum->setAlignment(Qt::AlignCenter);
    ui->lb_totalnum->setAlignment(Qt::AlignCenter);
    ui->lb_totalnum->setText("100.00");

    pressstat = 1;

    ui->ch_isfollow->setCheckState(Qt::Checked);

    connect(lab, &CScalebarLabel::sigpmoved, this, &CPlaybackWidget::slotpmoved);
}

CPlaybackWidget::~CPlaybackWidget()
{
    delete ui;
}



void CPlaybackWidget::on_bt_startbt_clicked()
{
    if(m_isPlay){
        QPixmap pixmap(":/img/startplaynew.png");
        ui->bt_startbt->setIcon(QIcon(pixmap));
        m_isPlay = false;
        emit GetUrlInterface::getInterface() -> setplaystat(false);
    }else{
        QPixmap pixmap(":/img/stopplaynew.png");
        ui->bt_startbt->setIcon(QIcon(pixmap));
        m_isPlay = true;
        emit GetUrlInterface::getInterface() -> setplaystat(true);
    }
}

void CPlaybackWidget::slotpmoved(float x, int type){
    ui->lb_currentnum ->setText(QString("%1").arg(x, 6, 'f', 2));
    if(type == 1){
        emit GetUrlInterface::getInterface() -> setprocess(x);
    }
    pressstat = type;
    //textlab->show();
}

void CPlaybackWidget::setCurrentProcess(float currentnum){
    if(pressstat == 0){
        return;
    }

    if(!m_isPlay){
        QPixmap pixmap(":/img/stopplaynew.png");
        ui->bt_startbt->setIcon(QIcon(pixmap));
        m_isPlay = true;
    }

    ui->lb_currentnum->setText(QString("%1").arg(currentnum, 6, 'f', 2));
    //qDebug()<<"setCurrentProcess:"<<QString("%1").arg(currentnum, 6, 'f', 2);
    ui->lb_scalelb->setProcess(currentnum);
}

void CPlaybackWidget::on_ch_isfollow_stateChanged(int arg1)
{
    if(arg1 == 0){
        emit sigFollowChange(false);
    }else{
        emit sigFollowChange(true);
    }
}

void CPlaybackWidget::reset(){
    ui->lb_currentnum->setText(QString("%1").arg(0.0, 6, 'f', 2));
    ui->lb_scalelb->reset();
}
