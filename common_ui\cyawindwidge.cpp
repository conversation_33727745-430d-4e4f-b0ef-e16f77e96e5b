﻿#include "cyawindwidge.h"

CYawIndWidge::CYawIndWidge(QWidget *parent)
    : QWidget(parent), m_yaw(0) {
    m_yaw = 80.344535;
    //timer->start(100);
}

void CYawIndWidge::setYaw(float angle) {
    m_yaw = angle;
    update();
}

void CYawIndWidge::paintEvent(QPaintEvent*) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    const int side = qMin(width(), height());
    const QPoint center(width()/2, height() + side/2);
    const int radius = side * 1.3;

    drawBackground(painter, center, radius);
    drawScale(painter, center, radius);
    drawArrow(painter, center, radius);
    drawFly(painter, center, radius);
}

void CYawIndWidge::drawBackground(QPainter& painter, const QPoint& center, int radius) {
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor("#eddfe5"));
    // 绘制上半圆（180°到0°的顺时针半圆）
    painter.drawPie(
        center.x() - radius,
        center.y() - radius,
        radius*2, radius*2,
        180*16,   // 起始角度（9点钟方向）
        -180*16   // 顺时针绘制半圆（到3点钟方向）
    );
}


void CYawIndWidge::drawScale(QPainter& painter, const QPoint& center, int radius) {
    painter.setPen(QPen(QColor("#4C2F7A"), 3));
    QFont font = painter.font();
    font.setPointSize(10);
    painter.setFont(font);

    const int tickLengths = 8;
    const int tickLengthl = 16;
    const int textMargin = 15;

    for(int physicalAngle = 0; physicalAngle <= 180; physicalAngle += 5) {
        int offset = qRound(m_yaw) % 10;
        const double radians = qDegreesToRadians(double(physicalAngle) + offset);
        const int displayAngle = qRound(m_yaw) - offset + 90 - physicalAngle; // 转换为显示角度

        // 刻度端点坐标（圆周上）
        const int x = center.x() + radius * cos(radians);
        const int y = center.y() - radius * sin(radians);

        // 绘制向内刻度线


        // 每20度显示标签
        if(physicalAngle % 10 == 0) {
            QFontMetrics fm(painter.font());
            QString text;
            if(displayAngle > 180){
                text = QString::number(180 - displayAngle);
            }if(displayAngle < -180){
                text = QString::number(-180 - displayAngle);
            } else{
                text = QString::number(displayAngle);
            }
            QRect textRect = fm.boundingRect(text);

            // 计算内缩文本位置
            const int textX = x - (radius*0.05 + textMargin) * cos(radians);
            const int textY = y + (radius*0.05 + textMargin) * sin(radians);

            painter.drawText(
                textX - textRect.width()/2,
                textY + textRect.height()/2,
                text
            );
            painter.drawLine(
                x, y,
                x - tickLengthl * cos(radians),
                y + tickLengthl * sin(radians)
            );
        }else{
            painter.drawLine(
                x, y,
                x - tickLengths * cos(radians),
                y + tickLengths * sin(radians)
            );
        }
    }

    painter.setPen(QPen(QColor("#4C2F7A"), 1)); // 文本框边框跟背景色一致
    //painter.setBrush(QColor("#FFE4B5")); // 文本框背景用米色
    QRect pithval(width() / 2 - 60, height() - 40, 120, 40);
    //painter.drawRect(pithval);

    QPainterPath path;
    path.addRoundedRect(pithval, 20, radius, Qt::AbsoluteSize);
    painter.drawPath(path.simplified());
    path.clear();

    painter.setPen(QColor("#4C2F7A")); // 文本框用 深绿色
    painter.setFont(QFont("Arial", 12, QFont::Bold));
    painter.drawText(pithval, Qt::AlignCenter, tr("Yaw") +":" + QString::number(m_yaw, 'f', 4));
}

void CYawIndWidge::drawArrow(QPainter& painter, const QPoint& center, int radius) {
    painter.save();
    painter.translate(center);

    // 箭头多边形（顶点在外部）
    QPolygon arrow;
    arrow << QPoint(0, -radius)          // 箭头尖端
          << QPoint(radius * 0.04, -radius * 1.08)
          << QPoint(-radius * 0.04, -radius * 1.08);

    // 绘制红色箭头
    painter.setPen(Qt::NoPen);
    if(m_yaw < 0.0001 && m_yaw > -0.0001){
        painter.setBrush(Qt::green);
    }else{
        painter.setBrush(Qt::red);
    }
    painter.drawPolygon(arrow);

    painter.restore();
}

void CYawIndWidge::drawFly(QPainter& painter, const QPoint& center, int radius){
    // 绘制飞机
    painter.setPen(QPen(QColor("#fe914d"), 2));
    painter.setBrush(QBrush());
    painter.save();
    painter.translate(center);
    //qDebug()<<"radius:"<<radius; //148
    QPolygonF polygon;
    painter.drawConvexPolygon(QPolygonF()
                              << QPointF(-radius * 0 / 241, -radius * 213.33 / 241)
                              << QPointF(-radius * 6.67 / 241, -radius * 190 / 241)
                              << QPointF(-radius * 26.67 / 241, -radius * 173.33 / 241)
                              << QPointF(-radius * 26.67 / 241, -radius * 166.67 / 241)
                              << QPointF(-radius * 6.67 / 241, -radius * 168.33 / 241)
                              << QPointF(-radius * 6.67 / 241, -radius * 166.67 / 241)
                              << QPointF(-radius * 3.33 / 241, -radius * 153.33 / 241)
                              << QPointF(-radius * 10 / 241, -radius * 143.33 / 241)
                              << QPointF(-radius * 10 / 241, -radius * 140 / 241)
                              << QPointF(-radius * 0 / 241, -radius * 143.33 / 241)
                              << QPointF(radius * 10 / 241, -radius * 140 / 241)
                              << QPointF(radius * 10 / 241, -radius * 143.33 / 241)
                              << QPointF(radius * 3.33 / 241, -radius * 153.33 / 241)
                              << QPointF(radius * 6.67 / 241, -radius * 166.67 / 241)
                              << QPointF(radius * 6.67 / 241, -radius * 168.33 / 241)
                              << QPointF(radius * 26.67 / 241, -radius * 166.67 / 241)
                              << QPointF(radius * 26.67 / 241, -radius * 173.33 / 241)
                              << QPointF(radius * 6.67 / 241, -radius * 190 / 241)
                              << QPointF(-radius * 0 / 241, -radius * 213.33 / 241));

    painter.drawConvexPolygon(polygon);

    if(m_yaw >= 0.0001){
        painter.drawText(-radius * 56.67 / 241, -radius * 168.33 / 241, "N");
    }
    if(m_yaw <= -0.0001){
        painter.drawText(radius * 56.67 / 241, -radius * 168.33 / 241, "N");
    }

    painter.restore();
}

