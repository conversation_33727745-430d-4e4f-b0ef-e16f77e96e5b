﻿#include "c3b020bprotocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include <QDebug>

C3B020BProtocol::C3B020BProtocol(QObject *parent) : CBaseProtocol(parent)
{
    CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));

   if(dripage != NULL){
       qDebug()<<"connect Cbddb0bProtocol";
       connect(this, &C3B020BProtocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
       connect(this, &C3B020BProtocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
   }

    m_slProtoKeys.append(tr("Time_stamp") + ":");
    m_slProtoKeys.append(tr("Cali_accel") + "-x:");
    m_slProtoKeys.append(tr("Cali_accel") + "-y:");
    m_slProtoKeys.append(tr("Cali_accel") + "-z:");
    m_slProtoKeys.append(tr("Angular_velocity") + "-x:");
    m_slProtoKeys.append(tr("Angular_velocity") + "-y:");
    m_slProtoKeys.append(tr("Angular_velocity") + "-z:");
    m_slProtoKeys.append(tr("Quaternion") + "-w:");
    m_slProtoKeys.append(tr("Quaternion") + "-x:");
    m_slProtoKeys.append(tr("Quaternion") + "-y:");
    m_slProtoKeys.append(tr("Quaternion") + "-z:");

}

bool C3B020BProtocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = sizeof(Stru3b020b);

    return true;
}

bool C3B020BProtocol::preInit(){
    CBaseProtocol::preInit();
    emit sigStatuUpdate(QStringList("LABFLOSS"), QStringList(tr("FLoss") + QString(":%1").arg(m_iLossFrameSum)), m_sPortN);
    emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
    return true;
}

void C3B020BProtocol::paseMsg(const QByteArray msg){
    Stru3b020b st_3b020b;
    QStringList dataValues;
    //异或校验
    if(!sumEorCheck(msg, 0, 1)){
        qDebug()<<QTime::currentTime().toString()<<"sumEorCheck error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") + QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    m_iReqCount++;

    ::memcpy(&st_3b020b, msg.data(), sizeof(Stru3b020b));


//    float faccelx = st_3b020b.CelebrationAcceleration_x * 12.0 / 32768.0;
//    float faccely = st_3b020b.CelebrationAcceleration_y * 12.0 / 32768.0;
//    float faccelz = st_3b020b.CelebrationAcceleration_z * 12.0 / 32768.0;
//    float fgyrox = st_3b020b.Angularvelocity_x * 300.0 / 32768.0;
//    float fgyroy = st_3b020b.Angularvelocity_y * 300.0 / 32768.0;
//    float fgyroz = st_3b020b.Angularvelocity_z * 300.0 / 32768.0;


    float faccelx = st_3b020b.CelebrationAcceleration_x / 2047.9375;
    float faccely = st_3b020b.CelebrationAcceleration_y / 2047.9375;
    float faccelz = st_3b020b.CelebrationAcceleration_z / 2047.9375;
    float fgyrox = st_3b020b.Angularvelocity_x /109.2233;
    float fgyroy = st_3b020b.Angularvelocity_y /109.2233;
    float fgyroz = st_3b020b.Angularvelocity_z /109.2233;

    dataValues.append(QString::number(st_3b020b.timestamp));
    dataValues.append(QString::number(faccelx));
    dataValues.append(QString::number(faccely));
    dataValues.append(QString::number(faccelz));
    dataValues.append(QString::number(fgyrox));
    dataValues.append(QString::number(fgyroy));
    dataValues.append(QString::number(fgyroz));
    dataValues.append(QString::number(st_3b020b.Quaternion_w));
    dataValues.append(QString::number(st_3b020b.Quaternion_x));
    dataValues.append(QString::number(st_3b020b.Quaternion_y));
    dataValues.append(QString::number(st_3b020b.Quaternion_z));

    writeCvsFile("3B020B", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        m_bIsNeedShow = false;
    }
}
