﻿#include "csinglestatuslable.h"
#include <QPainter>
#include <QDebug>

CSingleStatusLable::CSingleStatusLable(QWidget *parent) : QLabel(parent)
{
    m_pixOff.load(":/img/grayboal.jpeg");
    m_pixOn.load(":/img/greenboal.jpeg");
    m_text = tr("Wheelspeed");
}


void CSingleStatusLable::paintEvent(QPaintEvent *e){
    QPainter p(this);
    //p.translate(height()/2,height()/2);
    //p.scale(width()/200.0,width()/200.0);
    //p.setPen(Qt::NoPen);
    drawPixmap(&p);
    //drawText(&p);
}

void CSingleStatusLable::drawPixmap(QPainter *p)
{
    int r=14;
    p->save();

    if(m_state)
    {
        p->drawPixmap(r/2,r/2,r,r,m_pixOn);
    }else{
        p->drawPixmap(r/2,r/2,r,r,m_pixOff);
    }

    p->restore();
    qDebug()<<"drawPixmap";
    //update();
}

void CSingleStatusLable::drawText(QPainter *p)
{
    if(m_text.isEmpty())
        return;

    int r=20;
    QRect rect(r,r/2,r,r);
    p->save();
    p->setPen(m_textColor);
    p->setFont(m_textFont);
    p->drawText(rect,Qt::AlignCenter,m_text);
    p->restore();
    update();
}
