﻿#ifndef CMAPCONFIGWIDGET_H
#define CMAPCONFIGWIDGET_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include "cprotoparamdata.h"

namespace Ui {
class CMapConfigWidget;
}

/*
*地图参数设置页面
*/
class CMapConfigWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CMapConfigWidget(QWidget *parent = nullptr);
    ~CMapConfigWidget();
    void setColor(QPushButton *bt, QString &color);
    QIcon createLineIcon(int width, int height, int lineWidth, Qt::PenStyle lineStyle);
    void showEvent(QShowEvent *event);
    void setAllStyle(QLabel *lab, QString &color, int widthindex, QString &lcolor, int lwidthindex,  int styleindex);
    void getMapParms(MapParmSt &);

private slots:
    void on_cb_city_currentIndexChanged(int index);

    void on_cb_lineshow_currentIndexChanged(int index);

    void on_cb_showpreci_currentIndexChanged(int index);

    void on_bt_fdotcolor_clicked();

    void on_cb_fdotboldnum_currentIndexChanged(int index);

    void on_bt_flinecolor_clicked();

    void on_cb_flineboldnum_currentIndexChanged(int index);

    void on_cb_flinestyle_currentIndexChanged(int index);

    void on_bt_sdotcolor_clicked();

    void on_cb_sdotboldnum_currentIndexChanged(int index);

    void on_bt_slinecolor_clicked();

    void on_cb_slineboldnum_currentIndexChanged(int index);

    void on_cb_slinestyle_currentIndexChanged(int index);

    void on_bt_tdotcolor_clicked();

    void on_cb_tdotboldnum_currentIndexChanged(int index);

    void on_bt_tlinecolor_clicked();

    void on_cb_tlineboldnum_currentIndexChanged(int index);

    void on_cb_tlinestyle_currentIndexChanged(int index);

signals:
    void sigParmsChange(int index);

private:
    Ui::CMapConfigWidget *ui;
    QString m_fdotColor;
    QString m_flineColor;
    QString m_sdotColor;
    QString m_slineColor;
    QString m_tdotColor;
    QString m_tlineColor;
    int m_fdotwidth = 0;
    int m_sdotwidth = 0;
    int m_tdotwidth = 0;
    int m_flinewidth = 0;
    int m_slinewidth = 0;
    int m_tlinewidth = 0;
    int m_pflinestyle = 0;
    int m_pslinestyle = 0;
    int m_ptlinestyle = 0;
    QVector<QPointF> m_vLngLat;
    QPointF m_pDefSite;
    QVector<int> m_vdbnum = { 2, 1, 3, 4, 6, 8, 10};
    QVector<Qt::PenStyle> m_vdstyle = {Qt::SolidLine, Qt::DashLine, Qt::DotLine, Qt::DashDotLine};
};

#endif // CMAPCONFIGWIDGET_H
