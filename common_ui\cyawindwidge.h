﻿#ifndef CHEADINGINDWIDGE_H
#define CHEADINGINDWIDGE_H

#include <QWidget>
#include <QPainter>
#include <QPolygon>
#include <QFontMetrics>
#include <cmath>
#include <QtMath>
#include <QDebug>

/*
* 飞行视图航向子页面
*/
class CYawIndWidge : public QWidget
{
    Q_OBJECT
public:
    explicit CYawIndWidge(QWidget *parent = nullptr);
    void paintEvent(QPaintEvent*);
    void setYaw(float angle);
    void drawFly(QPainter& painter, const QPoint& center, int radius);
    void drawArrow(QPainter& painter, const QPoint& center, int radius);
    void drawScale(QPainter& painter, const QPoint& center, int radius);
    void drawBackground(QPainter& painter, const QPoint& center, int radius);


signals:
private:
    float m_yaw;

};

#endif // CHEADINGINDWIDGE_H
