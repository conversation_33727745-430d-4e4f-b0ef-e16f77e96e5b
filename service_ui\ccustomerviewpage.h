﻿#ifndef CCUSTOMERVIEWPAGE_H
#define CCUSTOMERVIEWPAGE_H

#include <QWidget>
#include <QTimer>
#include "cmapviewpage.h"
#include "cplateviewpage.h"
#include "cflyviewpage.h"

class PortInfoSt{
public:
    int id;
    qint64 lasttimes;
};

namespace Ui {
class CCustomerViewPage;
}

/*
* 导航视图页面
*/
class CCustomerViewPage : public QWidget
{
    Q_OBJECT

public:
    explicit CCustomerViewPage(QWidget *parent = nullptr);
    ~CCustomerViewPage();
    void resizeEvent(QResizeEvent *event) override;
    QColor getColorByVal(int val);
public slots:
    void on_bt_showplate_clicked();

    void on_bt_showmap_clicked();
    void slotDataShow(const QStringList dataValues, const QString portn, const int protoindex);
    void slotAttituShow(const QVector<double> attituValues, const QString portn);
    void slotDataPlayback(int rownum, QString &filename);
    void slotLngLatinfo(int, double, double, float);
    void slotViewChange(int index);
signals:
    void sigShowSelf();
    void sigLngLatinfo(int, double, double, float);

private slots:
    void on_cb_showportn_currentTextChanged(const QString &arg1);

private:
    Ui::CCustomerViewPage *ui;
    float m_fstframscale;
    CPlateViewPage *cplatevpg;
    CMapViewPage *cmapvpg;
    CFlyViewPage *cflypg;
    double offsettest;
    int m_rownum;
    float m_fFastProcess;
    bool m_bPlaystat;
    QMap<QString, PortInfoSt> m_mLineIdMap;
    QString m_sPortN;
    int m_iViewMode;
};

#endif // CCUSTOMERVIEWPAGE_H
