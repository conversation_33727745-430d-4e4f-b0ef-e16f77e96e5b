﻿#include "ccollectrecorddialog.h"
#include "ui_ccollectrecorddialog.h"
#include <QMenu>
#include <QDebug>
#include <QDir>
#include <QDateTime>
#include <QFileDialog>
#include <QDesktopServices>
#include <QMessageBox>
#include <QtConcurrent>
#include <QClipboard>
#include "cconfigmanager.h"
#include "ccustomerviewpage.h"
#include "csubpagemanager.h"

CCollectRecordDialog::CCollectRecordDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CCollectRecordDialog)
{
    ui->setupUi(this);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setAlternatingRowColors(true);
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->horizontalHeader()->setStretchLastSection(true);

    ui->tableWidget->setContextMenuPolicy(Qt::CustomContextMenu);

    connect(ui->tableWidget, &QTableWidget::customContextMenuRequested, this, &CCollectRecordDialog::showContextMenu);

    m_pfilest = CProtoParamData::getFileParmSt();
    QString recorddir = m_pfilest->savedir;
    QDir dir(recorddir);
    QFileInfoList list = dir.entryInfoList(QDir::Files | QDir::NoDotAndDotDot);
    ui->tableWidget->setRowCount(list.size());
    ui->tableWidget->setColumnWidth(0, 50);
    ui->tableWidget->setColumnWidth(1, 240);
    ui->tableWidget->setColumnWidth(2, 130);
    ui->tableWidget->setColumnWidth(3, 100);
    int rownum = 0;

    on_bt_refresh_clicked();

    CCustomerViewPage *custompage = static_cast<CCustomerViewPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_CustomerView"));
    connect(this, &CCollectRecordDialog::sigLngLatFile, custompage, &CCustomerViewPage::slotDataPlayback);

}

CCollectRecordDialog::~CCollectRecordDialog()
{
    delete ui;
}

void CCollectRecordDialog::showContextMenu(const QPoint &pos){
    // 创建一个QMenu
    QMenu menu(this);

    // 添加菜单项
    QAction *action1 = menu.addAction(tr("View driving trajectory"));
    QAction *action2 = menu.addAction(tr("Delete"));
    QAction *action3 = menu.addAction(tr("Open the file location"));
    QAction *action4 = menu.addAction(tr("Copy the file pathname"));
    QAction *action5 = menu.addAction(tr("Open the file"));

    // 显示菜单
    QAction *selectedAction = menu.exec(ui->tableWidget->viewport()->mapToGlobal(pos));

    QList<QTableWidgetItem *> selectitem = ui->tableWidget->selectedItems();

    //qDebug()<<selectitem.at(0)->data(0)<<selectitem.at(1)->data(0)<<selectitem.at(2)->data(0);
    QString recorddir = m_pfilest->savedir;

    // 处理选中的菜单项
    if (selectedAction == action1) {  //查看轨迹回放
        // 处理Action 1
        qDebug()<<"acton1";
        QString filename = selectitem.at(1)->data(0).value<QString>();
        if(!filename.startsWith("BDDB0B") && !filename.startsWith("NavData")){
            QMessageBox::critical(this, tr("Info"), tr("Non sports car trajectory file, unable to replay trajectory!"));
            return;
        }
        int rownum = selectitem.at(0)->data(0).value<int>();
        QString abfilename = recorddir + "/" + filename;
        close();
        qDebug()<<"slotDataPlayback:"<<filename;
        emit sigLngLatFile(rownum, abfilename);
    } else if (selectedAction == action2) { //删除
        QString filename = m_pfilest->savedir + "/" + selectitem.at(1)->data(0).value<QString>();
        qDebug()<<QFile::remove(filename)<<filename;
        on_bt_refresh_clicked();
        // 处理Action 2
    }else if(selectedAction == action3){ //打开文件所在目录
        //QDesktopServices::openUrl(QUrl::fromLocalFile(recorddir));
        QProcess process;
        QString filePath = m_pfilest->savedir + "/" + selectitem.at(1)->data(0).value<QString>();
        filePath.replace("/", "\\"); // 只能识别 "\"
        QString cmd = QString("explorer.exe /select,\"%1\"").arg(filePath);
        //qDebug() << cmd;
        process.startDetached(cmd);
    }else if(selectedAction == action4){//复制文件路径
        QString filePath = m_pfilest->savedir + "/" + selectitem.at(1)->data(0).value<QString>();
        filePath.replace("/", "\\"); // 只能识别 "\"
        QClipboard* clipboard = QApplication::clipboard();
        clipboard->setText(filePath);
    }else if(selectedAction == action5){//打开文件
        QString filePath = m_pfilest->savedir + "/" + selectitem.at(1)->data(0).value<QString>();
        filePath.replace("/", "\\"); // 只能识别 "\"
        QUrl fileUrl = QUrl::fromLocalFile(filePath);
        if (!QDesktopServices::openUrl(fileUrl)) {
            QMessageBox::critical(this, tr("Error"), tr("Fail to open file"));
        }
    }
}

void CCollectRecordDialog::on_bt_refresh_clicked()
{
    QString recorddir = m_pfilest->savedir;
    QDir dir(recorddir);
    QFileInfoList list = dir.entryInfoList(QDir::Files | QDir::NoDotAndDotDot);
    QVector<FILEINFO_ST *> vfileinfos;
    QVector<QDateTime> vdatetimes;
    QDate fdate = QDate(1990,0, 0);
    int ifiledate = ui->cb_filedate->currentIndex();
    if(ifiledate == 1){
        fdate = QDate::currentDate();
    }
    foreach (QFileInfo fileInfo, list) {
        if(fileInfo.isFile() && (fileInfo.fileName().endsWith(".csv") || fileInfo.fileName().endsWith(".dat"))){
            FILEINFO_ST *finfo = new FILEINFO_ST;
            finfo->filename = fileInfo.fileName();
            finfo->filetime = fileInfo.lastModified();
            finfo->filesize = fileInfo.size();
            if(finfo->filetime.date() >= fdate){
                vfileinfos.append(finfo);
                vdatetimes.append(finfo->filetime);
            }
        }
    }

    std::sort(vdatetimes.begin(), vdatetimes.end());

    int rownum = 0;
    ui->tableWidget->clearContents();
    ui->tableWidget->setRowCount(list.size());

    for(int i = vdatetimes.size() - 1; i > -1 ; i--){
        for (int j = 0;j < vfileinfos.size();j++) {
            if(vdatetimes.at(i) == vfileinfos.at(j)->filetime){
                QTableWidgetItem *item1 = new QTableWidgetItem(QString::number(rownum));
                item1->setTextAlignment(Qt::AlignCenter);
                QTableWidgetItem *item2 = new QTableWidgetItem(vfileinfos.at(j)->filename);
                QTableWidgetItem *item3 = new QTableWidgetItem(vfileinfos.at(j)->filetime.toString("yyyy-MM-dd hh:mm:ss"));
                QTableWidgetItem *item4 = new QTableWidgetItem(QString::number(vfileinfos.at(j)->filesize));

                ui->tableWidget->setItem(rownum, 0, item1);
                ui->tableWidget->setItem(rownum, 1, item2);
                ui->tableWidget->setItem(rownum, 2, item3);
                ui->tableWidget->setItem(rownum, 3, item4);
                rownum++;
            }

            //qDebug()<<"add items"<<rownum;
        }
    }
}
