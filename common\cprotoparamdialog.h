﻿#ifndef MESSAGEDIALOG_H
#define MESSAGEDIALOG_H

#include <QDialog>
#include <QMap>
#include "cprotoparamdata.h"
#include <QDebug>
#include <QScrollArea>
#include "cmapconfigwidget.h"

/************************************
* 类名 CProtoParamDialog
* 父类 QDialog
* 说明 参数界面类，用于设置参数
*************************************/

namespace Ui {
class CProtoParamDialog;
}

class CProtoParamDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CProtoParamDialog(QWidget *parent = nullptr);
    ~CProtoParamDialog();
    CProtoParamData *getProtoParam(){
        qDebug()<<"parm 1 addr:"<<m_pParm;
        return m_pParm;
    }
    void showEvent(QShowEvent *) override;


private slots:
    void on_bt_saveparm_clicked();

    void on_bt_cancelparm_clicked();

    void on_bt_deviceconf_clicked();

    void on_bt_timerconf_clicked();

    void on_bt_ffieldconf_clicked();

    void on_bt_mapconfig_clicked();

private:

    Ui::CProtoParamDialog *ui;
    QString m_sProtoType;
    CProtoParamData *m_pParm;
    QScrollArea *m_scrollArea;
    CMapConfigWidget *m_mconfWid;
};

#endif // MESSAGEDIALOG_H
