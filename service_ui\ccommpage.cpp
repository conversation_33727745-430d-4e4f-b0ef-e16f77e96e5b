#include "ccommpage.h"
#include "ui_ccommpage.h"
#include <QDebug>
#include <QMessageBox>
#include <QTime>
#include <QCloseEvent>
#include <QShowEvent>
#include <QSerialPortInfo>

CCommPage::CCommPage(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::CCommPage)
    , m_bIsHex(true)
    , m_IsOpen(false)
    , m_checkSize(0)
    , m_pparam(nullptr)
    , m_bIsAutoStart(false)
    , m_olser(nullptr)
    , m_isInValid(false)
    , m_currentCommType(COMM_SERIAL)
{
    ui->setupUi(this);
    initUI();

    m_CommBuff.clear();

    // 初始化定时器
    startTimer(1000);
}

CCommPage::~CCommPage()
{
    // 清理所有通讯服务
    for(auto service : m_commSerMap.values()){
        if(service){
            service->deleteLater();
        }
    }
    m_commSerMap.clear();

    delete ui;
}

void CCommPage::initUI()
{
    // 初始化通讯类型下拉框
    ui->cb_CommType->addItem(tr("Serial"), COMM_SERIAL);
    ui->cb_CommType->addItem(tr("TCP"), COMM_TCP);

    // 初始化串口配置
    ui->cb_BaudR->addItems({"9600", "19200", "38400", "57600", "115200", "230400", "460800", "921600"});
    ui->cb_BaudR->setCurrentText("115200");

    ui->cb_DataB->addItems({"5", "6", "7", "8"});
    ui->cb_DataB->setCurrentText("8");

    ui->cb_CheckB->addItems({tr("No"), tr("Odd"), tr("Even")});
    ui->cb_CheckB->setCurrentText(tr("No"));

    ui->cb_StopB->addItems({"1", "1.5", "2"});
    ui->cb_StopB->setCurrentText("1");

    ui->cb_DataM->addItems({tr("Hex"), tr("Text")});
    ui->cb_DataM->setCurrentText(tr("Hex"));

    // 初始化TCP配置
    ui->cb_TcpIP->addItem("***************");
    ui->cb_TcpIP->setCurrentText("***************");

    ui->cb_TcpPort->addItem("8080");
    ui->cb_TcpPort->setCurrentText("8080");

    // 初始化写入类型
    ui->cb_writeType->addItems({tr("Simulate"), tr("RealWrite")});

    // 更新UI显示
    updateCommTypeUI();

    // 获取可用串口
    getAvilaCom();
}

void CCommPage::updateCommTypeUI()
{
    bool isSerial = (m_currentCommType == COMM_SERIAL);

    // 串口相关控件
    ui->label_PortN->setVisible(isSerial);
    ui->cb_PortN->setVisible(isSerial);
    ui->label_BaudR->setVisible(isSerial);
    ui->cb_BaudR->setVisible(isSerial);
    ui->label_DataB->setVisible(isSerial);
    ui->cb_DataB->setVisible(isSerial);
    ui->label_CheckB->setVisible(isSerial);
    ui->cb_CheckB->setVisible(isSerial);
    ui->label_StopB->setVisible(isSerial);
    ui->cb_StopB->setVisible(isSerial);
    ui->label_DataM->setVisible(isSerial);
    ui->cb_DataM->setVisible(isSerial);

    // TCP相关控件
    ui->label_TcpIP->setVisible(!isSerial);
    ui->cb_TcpIP->setVisible(!isSerial);
    ui->label_TcpPort->setVisible(!isSerial);
    ui->cb_TcpPort->setVisible(!isSerial);
}

QString CCommPage::getCurrentConfig()
{
    if(m_currentCommType == COMM_SERIAL){
        return ui->cb_PortN->currentText();
    }else{
        return QString("TCP_%1:%2").arg(ui->cb_TcpIP->currentText()).arg(ui->cb_TcpPort->currentText());
    }
}

ICommService* CCommPage::createCommService(CommType type)
{
    if(type == COMM_SERIAL){
        return new CSseriService;
    }else{
        return new CTcpService;
    }
}

QStringList CCommPage::getAvilaCom()
{
    m_sComLists.clear();
    QList<QSerialPortInfo> infos = QSerialPortInfo::availablePorts();
    for(const QSerialPortInfo &info : infos){
        m_sComLists.append(info.portName());
    }

    // 更新UI
    QString currentPort = ui->cb_PortN->currentText();
    ui->cb_PortN->clear();
    ui->cb_PortN->addItems(m_sComLists);
    if(m_sComLists.contains(currentPort)){
        ui->cb_PortN->setCurrentText(currentPort);
    }

    emit sigPortDataChange(m_sComLists);
    return m_sComLists;
}

void CCommPage::on_cb_CommType_currentIndexChanged(int index)
{
    m_currentCommType = static_cast<CommType>(ui->cb_CommType->itemData(index).toInt());
    updateCommTypeUI();

    // 如果当前有打开的连接，需要关闭
    if(m_IsOpen){
        on_bt_CommClose_clicked();
    }
}

void CCommPage::on_bt_CommOpen_clicked()
{
    QString config = getCurrentConfig();
    QPointer<ICommService> commSer = m_commSerMap[config];
    QString btstatus = ui->bt_CommOpen->text();

    if(tr("Open") == btstatus){
        if(commSer == NULL){
            commSer = createCommService(m_currentCommType);
            m_commSerMap[config] = commSer;

            connect(this, &CCommPage::sigCommOpen, commSer, &ICommService::slotCommOpenOrClose, Qt::BlockingQueuedConnection);
            connect(commSer, &ICommService::destroyed, this, [&](QObject *obj){
                qDebug()<<"Communication service destroyed";
                for (int i = 0; i < m_commSerMap.size(); i++) {
                    if(m_commSerMap.values().at(i) == obj){
                        QString key = m_commSerMap.keys().at(i);
                        m_commSerMap[key] = NULL;
                        break;
                    }
                }
                ui->bt_CommOpen->setText(tr("Open"));
                m_IsOpen = false;
                needDisableCommParm(false);
            });

            connect(commSer, &ICommService::sigDataRead, this, &CCommPage::ReadTextEditData);

            // TCP特有的信号连接
            if(m_currentCommType == COMM_TCP){
                CTcpService *tcpService = qobject_cast<CTcpService*>(commSer);
                if(tcpService){
                    connect(tcpService, &CTcpService::sigClientConnected, this, &CCommPage::sigTcpClientConnected);
                    connect(tcpService, &CTcpService::sigClientDisconnected, this, &CCommPage::sigTcpClientDisconnected);
                    connect(tcpService, &CTcpService::sigTcpError, this, &CCommPage::sigTcpError);
                }
            }
        }

        // 初始化通讯服务
        bool initResult = false;
        if(m_currentCommType == COMM_SERIAL){
            QString sPortN = ui->cb_PortN->currentText();
            QString sBaudR = ui->cb_BaudR->currentText();
            QString sDataB = ui->cb_DataB->currentText();
            QString sDataM = ui->cb_DataM->currentText();
            QString sStopB = ui->cb_StopB->currentText();
            QString sParity = ui->cb_CheckB->currentText();
            initResult = commSer->CommInit(sPortN, sBaudR, sDataB, sParity, sStopB, sDataM, m_pparam);
        }else{
            QString sTcpIP = ui->cb_TcpIP->currentText();
            QString sTcpPort = ui->cb_TcpPort->currentText();
            initResult = commSer->CommInit(sTcpIP, sTcpPort, "", "", "", "", m_pparam);
        }

        if(!initResult){
            QMessageBox::critical(this, tr("Error"), tr("Communication initialization failed!"));
            return;
        }

        emit sigCommOpen(config, 0, "");
        if(!commSer->isWorking()){
            if(isVisible()){
                QString errorMsg = (m_currentCommType == COMM_SERIAL) ?
                    tr("Serial port opening failed, please check if the serial port configuration is correct!") :
                    tr("TCP server start failed, please check if the TCP configuration is correct!");
                QMessageBox::critical(this, tr("Error"), errorMsg);
            }
            ui->bt_CommOpen->setText(tr("Open"));
            m_IsOpen = false;
            return;
        }

        needDisableCommParm(true);
        //创建时就要同步一次参数
        if(m_pparam){
            m_pparam->informParamSig();
        }
        ui->bt_CommOpen->setText(tr("Close"));
        m_IsOpen = true;
    }else{
        needDisableCommParm(false);
        qDebug()<<"sigCommOpen begin";
        emit sigCommOpen(config, 1, "");
        qDebug()<<"sigCommOpen end";
        delete commSer;
        m_commSerMap[config] = NULL;
        commSer = NULL;
        ui->bt_CommOpen->setText(tr("Open"));
        m_IsOpen = false;
    }
}

void CCommPage::on_bt_CommClose_clicked()
{
    close();
}

void CCommPage::on_bt_CommClear_clicked()
{
    ui->ed_CommRecvB->clear();
    m_CommBuff.clear();
}

void CCommPage::needDisableCommParm(bool benable)
{
    ui->cb_CommType->setDisabled(benable);

    if(m_currentCommType == COMM_SERIAL){
        ui->cb_PortN->setDisabled(benable);
        ui->cb_BaudR->setDisabled(benable);
        ui->cb_DataB->setDisabled(benable);
        ui->cb_CheckB->setDisabled(benable);
        ui->cb_StopB->setDisabled(benable);
        ui->cb_DataM->setDisabled(benable);
    }else{
        ui->cb_TcpIP->setDisabled(benable);
        ui->cb_TcpPort->setDisabled(benable);
    }
}

void CCommPage::ReadTextEditData(const QByteArray &data)
{
    m_CommBuff.append(data);

    QString showData;
    if(m_bIsHex){
        showData = data.toHex(' ').toUpper();
    }else{
        showData = QString::fromLocal8Bit(data);
    }

    ui->ed_CommRecvB->append(QTime::currentTime().toString("HH:mm:ss ") + tr("recv") + ":" + showData);
    emit sigDataReceived(data);
}

bool CCommPage::getStatusByConfig(QString config)
{
    if(m_commSerMap.contains(config) && m_commSerMap[config] != NULL){
        return m_commSerMap[config]->isWorking();
    }
    return false;
}

QPointer<ICommService> CCommPage::getServByConfig(QString config)
{
    if(m_commSerMap.contains(config)){
        return m_commSerMap[config];
    }
    return nullptr;
}

QPointer<ICommService> CCommPage::getServByConfig()
{
    QString config = getCurrentConfig();
    return getServByConfig(config);
}

void CCommPage::timerEvent(QTimerEvent *e)
{
    if(m_currentCommType == COMM_SERIAL){
        getAvilaCom();
    }
}

void CCommPage::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);
    setCommPageStatus();
}

void CCommPage::setCommPageStatus()
{
    QString config = getCurrentConfig();
    if(getStatusByConfig(config)){
        ui->bt_CommOpen->setText(tr("Close"));
        m_IsOpen = true;
        needDisableCommParm(true);
    }else{
        ui->bt_CommOpen->setText(tr("Open"));
        m_IsOpen = false;
        needDisableCommParm(false);
    }
}

void CCommPage::on_bt_CommSend_clicked()
{
    QString config = getCurrentConfig();
    QPointer<ICommService> commSer = m_commSerMap[config];
    if(commSer == NULL){
        qDebug()<<"Communication service:"<<config<<"is not working";
        return;
    }

    QString warr = ui->ed_CommSendB->toPlainText();
    QByteArray harr;

    if(m_bIsHex){
        for(int i = 0; i < warr.size(); i++){
            if(warr[i] == ' '){
                continue;
            }
            if(i + 1 < warr.size()){
                int num = warr.mid(i, 2).toUInt(nullptr, 16);
                ++i;
                harr.append(num);
            }
        }
    }else{
        harr = warr.toLocal8Bit();
    }

    QString ssenddata = m_bIsHex ? harr.toHex(' ').toUpper() : QString::fromLocal8Bit(harr);

    int index = ui->cb_writeType->currentIndex();
    if(index == 0){
        commSer->editWrite(harr);
    }else{
        connect(this, &CCommPage::SigCommWrite, commSer, &ICommService::slotCommWrite, Qt::BlockingQueuedConnection);
        int len = harr.size();
        //将buff清空
        on_bt_CommClear_clicked();

        emit SigCommWrite(harr, len);
        disconnect(this, &CCommPage::SigCommWrite, commSer, &ICommService::slotCommWrite);
        //抓取反馈结果
        catPackData(false);
    }
    ui->ed_CommRecvB->append(QTime::currentTime().toString("HH:mm:ss ") + tr("send") +":" + ssenddata);
}

void CCommPage::commWriteData()
{
    QString config = getCurrentConfig();
    if(!m_commSerMap.contains(config)){
        qDebug()<<"Communication service:"<<config<<"is not working";
        return;
    }
    QPointer<ICommService> commSer = m_commSerMap[config];
    if(commSer == NULL){
        qDebug()<<"Communication service:"<<config<<"is not working";
        return;
    }
    commSer->commWriteData();
}

void CCommPage::openOrCloseCommPort()
{
    on_bt_CommOpen_clicked();
}

void CCommPage::restartPort()
{
    QString config = getCurrentConfig();
    //  释放当前通讯对象
    if(m_commSerMap.contains(config) && m_commSerMap[config] != NULL){
         m_commSerMap[config]->deleteLater();
    }
    //重新打开通讯
    m_commSerMap[config] = NULL;
    ui->bt_CommOpen->setText(tr("Open"));
    openOrCloseCommPort();
    //同步通讯打开状态
    QString param = (m_currentCommType == COMM_SERIAL) ? ui->cb_BaudR->currentText() : ui->cb_TcpPort->currentText();
    emit sigUpgMCommP(config, param, false);
}

void CCommPage::catPackData(bool isNeedClear)
{
    // 数据包捕获实现
    if(isNeedClear){
        m_CommBuff.clear();
    }

    // 等待数据接收
    QTimer timer;
    timer.setSingleShot(true);
    timer.start(1000);

    while(timer.isActive()){
        QCoreApplication::processEvents();
        if(!m_CommBuff.isEmpty()){
            break;
        }
    }
}

void CCommPage::closeEvent(QCloseEvent *event)
{
    Q_UNUSED(event);
    needDisableCommParm(false);
    ui->ed_CommRecvB->clear();
    m_CommBuff.clear();

    QString config = getCurrentConfig();
    QString param = (m_currentCommType == COMM_SERIAL) ? ui->cb_BaudR->currentText() : ui->cb_TcpPort->currentText();
    emit sigUpgMCommP(config, param, m_isInValid);
}

void CCommPage::slotSetConfig(const QString &config)
{
    if(m_currentCommType == COMM_SERIAL){
        ui->cb_PortN->setCurrentText(config);
    }else{
        // 解析TCP配置
        if(config.startsWith("TCP_")){
            QString tcpConfig = config.mid(4); // 去掉"TCP_"前缀
            QStringList parts = tcpConfig.split(":");
            if(parts.size() == 2){
                ui->cb_TcpIP->setCurrentText(parts[0]);
                ui->cb_TcpPort->setCurrentText(parts[1]);
            }
        }
    }
    setCommPageStatus();
}

void CCommPage::slotSetBaudR(const QString &text)
{
    qDebug()<<"slotSetBaudR:"<<text;
    ui->cb_BaudR->setCurrentText(text);
}

void CCommPage::slotSetTcpIP(const QString &text)
{
    ui->cb_TcpIP->setCurrentText(text);
}

void CCommPage::slotSetTcpPort(const QString &text)
{
    ui->cb_TcpPort->setCurrentText(text);
}

void CCommPage::on_cb_PortN_currentTextChanged(const QString &text)
{
    Q_UNUSED(text);
    setCommPageStatus();
}

void CCommPage::on_cb_BaudR_currentTextChanged(const QString &text)
{
    Q_UNUSED(text);
}

void CCommPage::on_cb_TcpIP_currentTextChanged(const QString &text)
{
    Q_UNUSED(text);
    setCommPageStatus();
}

void CCommPage::on_cb_TcpPort_currentTextChanged(const QString &text)
{
    Q_UNUSED(text);
    setCommPageStatus();
}

void CCommPage::findMsgHeadAuto(const QByteArray &barr, QByteArrayList &headlist)
{
    // 消息头自动查找实现
    // 这里可以根据具体协议实现
}

void CCommPage::setUpgStatus(bool status)
{
    m_isInValid = status;
}
