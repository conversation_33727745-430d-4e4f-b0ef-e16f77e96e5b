﻿#include "c3a0100protocol.h"
#include <QDebug>
#include <QWidget>
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cdataanapage.h"
#include "ccustomerviewpage.h"
#include <QThread>
#include "cprotocolfactory.h"

C3a0100Protocol::C3a0100Protocol(QObject *parent) : CBaseProtocol(parent)
{
    qRegisterMetaType<QVector<double>>("QVector<double>");
     CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
     CDriveTestPage *drivepage = static_cast<CDriveTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriveTest"));
     CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));
     CCustomerViewPage *custompage = static_cast<CCustomerViewPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_CustomerView"));

    if(dripage != NULL){
        qDebug()<<"connect C3a0100Protocol";
        connect(this, &C3a0100Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &C3a0100Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
        connect(this, &C3a0100Protocol::sigDataUpdate, drivepage, &CDriveTestPage::slotDataShow);
        connect(this, &C3a0100Protocol::sigChartsUpdate, danapage, &CDataAnaPage::slotDataShow);
        connect(this, &C3a0100Protocol::sigAttituUpdate, custompage, &CCustomerViewPage::slotAttituShow);
    }
}

C3a0100Protocol::~C3a0100Protocol(){
    //QStringList tempvalues;
    //emit sigDataUpdate(m_slProtoKeys, tempvalues, m_sPortN);
}

bool C3a0100Protocol::setProtoLength(const QByteArray &barr){
    m_slProtoKeys =  CProtocolFactory::getProKeys(m_iProtoIndex);
    m_bMsgHead = barr.mid(0, 3);
    m_uMsgLen = barr.at(5) + (barr.at(6) << 8);

    if(m_uMsgLen != sizeof(Stru3a0100)){
        m_uMsgLen = sizeof(Stru3a0100);
        return false;
    }

    return true;
    //qDebug()<<barr.toHex(' ');
    //qDebug()<<"setProtoLength:"<<m_uMsgLen<<barr.at(5)<<barr.at(6);
}

void C3a0100Protocol::paseMsg(const QByteArray msg){
    Stru3a0100 st_3a0100;
    QStringList dataValues;
    int indxpos = 0;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
    if(m_bIsNeedDat){
        writeDatFile("3A01",msg);
    }

    m_iReqCount++;

    ::memcpy(&st_3a0100, msg.data(), m_uMsgLen);

    if(!sum8CheckSum(msg)){
        qDebug()<<QTime::currentTime().toString()<<"sum8CheckSum error:"<<msg.toHex();
        setFrameErr(E_FRAME_CHECK);
        m_iCheckFailSum++;
        if(m_bIsNeedShowC){
            emit sigStatuUpdate(QStringList("LABCHECK"), QStringList(tr("ChFail") +QString(":%1").arg(m_iCheckFailSum)), m_sPortN);
            m_bIsNeedShowC = false;
        }
        return;
    }

    if(m_iGnsstime > 0 && m_iGnsstime > st_3a0100.timestamp){
        emit sigStatuUpdate(QStringList("LABEXCEPT"), QStringList(tr("Except") + ":1"), m_sPortN);
        m_bIsNeedShowC = false;
    }
    m_iGnsstime = st_3a0100.timestamp;

    dataValues.append(QString::number(st_3a0100.timestamp));
    dataValues.append(QString::number(st_3a0100.CelebrationAcceleration_x, 'f', 5));
    dataValues.append(QString::number(st_3a0100.CelebrationAcceleration_y, 'f', 5));
    dataValues.append(QString::number(st_3a0100.CelebrationAcceleration_z, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Angularvelocity_x, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Angularvelocity_y, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Angularvelocity_z, 'f', 5));
    dataValues.append(QString::number(st_3a0100.LinearAcceleration_x, 'f', 5));
    dataValues.append(QString::number(st_3a0100.LinearAcceleration_y, 'f', 5));
    dataValues.append(QString::number(st_3a0100.LinearAcceleration_z, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Quaternion_w, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Quaternion_x, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Quaternion_y, 'f', 5));
    dataValues.append(QString::number(st_3a0100.Quaternion_z, 'f', 5));

    //增加姿态显示
    double froll,fpitch,fyaw;
    calculateEulerAngles(st_3a0100.Quaternion_w, st_3a0100.Quaternion_x, st_3a0100.Quaternion_y, st_3a0100.Quaternion_z, \
                         froll, fpitch, fyaw);

    QVector<double> vplateValue;
    vplateValue.append(froll);
    vplateValue.append(fpitch);
    vplateValue.append(fyaw);
    vplateValue.append(0.0f);
    vplateValue.append(0.0f);
    vplateValue.append(0.0f);

    emit sigAttituUpdate(vplateValue, m_sPortN);


    /*unsigned char buf[] = {0x41,0xE9};
    short sd;
    memcpy(&sd, buf, 2);
    qDebug()<<-1 * complement2original(sd &0x0000FFFF);
    */

    writeCvsFile("3A01", m_slProtoKeys, dataValues);
    if(m_bIsNeedShow){
        QString stime = getRunTimes();
        //qDebug()<<"time:"<<QTime::currentTime().toString()<<stime;
        dataValues.append(stime);
        dataValues.append(QString::number(m_iCurrentFeq));
        if(m_bIsNeedKeys){
            emit sigDataUpdate(m_slProtoKeys, dataValues, m_sPortN, m_iProtoIndex);
        }else{
            emit sigDataUpdate(QStringList(), dataValues, m_sPortN, m_iProtoIndex);
        }
        m_bIsNeedShow = false;
    }
    QVector<double> vddata;
    vddata.append(st_3a0100.timestamp);
    vddata.append(st_3a0100.CelebrationAcceleration_x);
    vddata.append(st_3a0100.CelebrationAcceleration_y);
    vddata.append(st_3a0100.CelebrationAcceleration_z);
    vddata.append(st_3a0100.Angularvelocity_x);
    vddata.append(st_3a0100.Angularvelocity_y);
    vddata.append(st_3a0100.Angularvelocity_z);
    vddata.append(st_3a0100.LinearAcceleration_x);
    vddata.append(st_3a0100.LinearAcceleration_y);
    vddata.append(st_3a0100.LinearAcceleration_z);
    vddata.append(st_3a0100.Quaternion_w);
    vddata.append(st_3a0100.Quaternion_x);
    vddata.append(st_3a0100.Quaternion_y);
    vddata.append(st_3a0100.Quaternion_z);
    emit sigChartsUpdate(m_slProtoKeys, vddata, m_sPortN, m_iProtoIndex);

}

bool C3a0100Protocol::sum8CheckSum(const QByteArray msg){
    uint16_t umsgchecksum = 0;
    uint16_t ucalchecksum = 0;
    //特殊，校验码在size-4开始的两个字节
    ::memcpy(&umsgchecksum, msg.data() + msg.size() - 4, 2);
    //字节1到字节62的累加和
    for (int i = 1; i < msg.size() - 4;) {
        ucalchecksum += (unsigned char)msg[i];
        i++;
    }

    if(umsgchecksum == ucalchecksum){
        return true;
    }
    qDebug()<<"sum8CheckSum:"<<umsgchecksum<<"|"<<ucalchecksum;
    return false;
}

bool C3a0100Protocol::preInit(){
    CBaseProtocol::preInit();
    return true;
}



