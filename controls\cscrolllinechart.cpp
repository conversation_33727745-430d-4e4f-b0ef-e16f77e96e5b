﻿/***************************************************************************
**                                                                        **
**  QCustomPlot, an easy to use, modern plotting widget for Qt            **
**  Copyright (C) 2011-2022 Emanuel <PERSON>hammer                            **
**                                                                        **
**  This program is free software: you can redistribute it and/or modify  **
**  it under the terms of the GNU General Public License as published by  **
**  the Free Software Foundation, either version 3 of the License, or     **
**  (at your option) any later version.                                   **
**                                                                        **
**  This program is distributed in the hope that it will be useful,       **
**  but WITHOUT ANY WARRANTY; without even the implied warranty of        **
**  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         **
**  GNU General Public License for more details.                          **
**                                                                        **
**  You should have received a copy of the GNU General Public License     **
**  along with this program.  If not, see http://www.gnu.org/licenses/.   **
**                                                                        **
****************************************************************************
**           Author: Emanuel Eichhammer                                   **
**  Website/Contact: https://www.qcustomplot.com/                         **
**             Date: 06.11.22                                             **
**          Version: 2.1.1                                                **
****************************************************************************/

#include "cscrolllinechart.h"
#include "ui_cscrolllinechart.h"
#include <QFile>
#include <QColor>
#include "ctlhtools.h"

#define SCREEN_WITH 1000
#define MAX_DATA_SIZE 12000   //实时最大数据量100w

CScrollLineChart::CScrollLineChart(QWidget *parent) :
  QWidget(parent),
  ui(new Ui::CScrollLineChart)
{
  ui->setupUi(this);
  setupPlot();
  QElapsedTimer t;
  mTag1 = NULL;
  m_i64Counter = 0l;

  // configure scroll bars:
  // Since scroll bars only support integer values, we'll set a high default range of -500..500 and
  // divide scroll bar position values by 100 to provide a scroll range -5..5 in floating point
  // axis coordinates. if you want to dynamically grow the range accessible with the scroll bar,
  // just increase the minimum/maximum values of the scroll bars as needed.
  ui->verticalScrollBar->setRange(-500, 500);

  // create connection between axes and scroll bars:
  connect(ui->horizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(horzScrollBarChanged(int)));
  //connect(ui->verticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(vertScrollBarChanged(int)));
  connect(ui->plot->xAxis, SIGNAL(rangeChanged(QCPRange)), this, SLOT(xAxisChanged(QCPRange)));
  connect(ui->plot->yAxis, SIGNAL(rangeChanged(QCPRange)), this, SLOT(yAxisChanged(QCPRange)));
  connect(ui->plot, &QCustomPlot::mousePress, this, &CScrollLineChart::slotMousePress);
  //connect(ui->plot, &QCustomPlot::mouseWheel, this, &CScrollLineChart::slotMouseWheel);

  ui->horizontalScrollBar->setRange(0, ui->plot->graph(0)->dataCount());
  ui->plot->axisRect()->axis(QCPAxis::atLeft, 0)->setBasePen(QPen(QColor("#7FFFD4")));
  ui->plot->axisRect()->axis(QCPAxis::atBottom, 0)->setBasePen(QPen(QColor("#7FFFD4")));
  ui->plot->axisRect()->axis(QCPAxis::atBottom, 0)->setTickLabelColor(QColor("#4169E1"));
  ui->plot->axisRect()->axis(QCPAxis::atLeft, 0)->setTickLabelColor(QColor("#4169E1"));
  ui->plot->axisRect()->axis(QCPAxis::atBottom, 0)->setTickLabelFont(QFont("Microsoft YaHei",10));
  ui->plot->axisRect()->axis(QCPAxis::atLeft, 0)->setTickLabelFont(QFont("Microsoft YaHei",10));

  ui->plot->xAxis->setRange(0, SCREEN_WITH);
  bool fundrang;
  ui->plot->yAxis->setRange(0, 2 * qCeil(ui->plot->graph(0)->getValueRange(fundrang).upper), Qt::AlignCenter);
  //ui->plot->yAxis->setRange(0, 2, Qt::AlignCenter);
  m_hasCursor = false;
  m_iTimerId = -1;
  //m_iTimerId = startTimer(100);

  ui->verticalScrollBar->setDisabled(true);
  ui->horizontalScrollBar->setDisabled(true);

  ui->verticalScrollBar->setVisible(false);
  ui->horizontalScrollBar->setVisible(false);

  m_bIsRealFlush = true;

  m_dLowerY = 0;
  m_dUpperY = 0;

  m_lMaxCount = 0l;    //数据量

  m_fmariny = 0.05;    //默认数据冗余比例

  m_iWindRange = SCREEN_WITH;  //实时窗口显示数据宽度

  connect(&m_tflushTimer, &QTimer::timeout, this, &CScrollLineChart::slotTImeOut);

  //ui->plot->setSelectionTolerance(1);
  //ui->plot->setOpenGl(true);

  connect(ui->plot, &QCustomPlot::itemClick, this, [](QCPAbstractItem *item, QMouseEvent *event){
      qDebug()<<"plot click:"<<item->objectName();
  });

  //m_itemTracer = new QCPItemTracer(ui->plot);

  ////渐变色
  QLinearGradient plotGradient;
  plotGradient.setStart(0, 0);
  plotGradient.setFinalStop(500,0);
  plotGradient.setColorAt(0, QColor("#FFFFF0"));
  plotGradient.setColorAt(1, QColor("#FAFAD2"));
  ui->plot->setBackground(plotGradient);

  //ui->plot->xAxis->grid()->setZeroLinePen(QPen(QColor(Qt::green)));
  //ui->plot->yAxis->grid()->setZeroLinePen(QPen(QColor(Qt::green)));
  for(QPen pen:m_vPens){
      pen.setWidthF(5.0);
  }
}

CScrollLineChart::~CScrollLineChart()
{
  delete ui;
}

void CScrollLineChart::setupPlot()
{
  // The following plot setup is mostly taken from the plot demos:
  ui->plot->addGraph();
  ui->plot->graph(0)->setPen(m_vPens.at(m_graphNum));

  //ui->plot->graph(0)->rescaleAxes(true);//设置数据自适应范围
  //ui->plot->graph(0)->setSelectable(QCP::SelectionType::stMultipleDataRanges);//数据多选

  ui->plot->axisRect()->setupFullAxesBox(true);
  //鼠标拖动及滚轮操作
  ui->plot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables);
  //ui->plot->setInteractions(QCP::iRangeDrag);
  ui->plot->axisRect()->setRangeZoomFactor(1.2,1);
  ui->plot->legend->setVisible(true);
  ui->plot->legend->setFont(QFont("Bebas", 9));
  ui->plot->legend->setTextColor(QColor("#4B0082"));
  ui->plot->graph(0)->setName("uninit");
  m_graphNum++;
  // 点状图
  //ui->plot->graph(0)->setLineStyle(QCPGraph::lsNone);
  //ui->plot->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, 2));
  qDebug()<<"setup"<<ui->plot->graph(0)->parentPlot();
  ui->plot->graph(0)->setSelectable(QCP::stSingleData);
  //m_itemTracer->setGraph(ui->plot->graph(0));
}

void CScrollLineChart::setTotalGraph(int num){
    int createnum = ui->plot->graphCount();
    //将多余的曲线移除
    if(createnum > num){
        for(int i = createnum; i > num; i--){
            ui->plot->removeGraph(i - 1);
            m_graphNum--;
        }
        return;
    }

    //创建足够的曲线
    for(int i = createnum; i < num; i++){
        ui->plot->addGraph();
        ui->plot->graph(i)->setPen(m_vPens.at(i));
        //ui->plot->graph(i)->rescaleAxes(true);//设置数据自适应范围
        //ui->plot->graph(i)->setSelectable(QCP::SelectionType::stMultipleDataRanges);//数据多选
        m_graphNum++;
    }
}


void CScrollLineChart::horzScrollBarChanged(int value)
{
    //qDebug()<<"horzScrollBarChanged:"<<value<<ui->plot->xAxis->range().center()<<m_lCurrentRang<<m_lCurrentRang;
    //if (qAbs(ui->plot->xAxis->range().center()-(value * m_lCurrentRang)) > m_lCurrentRang) // 去除拖动的绘制和滚轮导致的二次冲毁
    //{
    //   qDebug()<<"ui->plot->xAxis->setRange";
    //   ui->plot->xAxis->setRange(value * m_lCurrentRang, ui->plot->xAxis->range().size(), Qt::AlignLeft);
    //   ui->plot->replot();
    //}

    if (qAbs(ui->plot->xAxis->range().center() - value) > 0.01) { // if user is dragging plot, we don't want to replot twice
        ui->plot->xAxis->setRange(value, ui->plot->xAxis->range().size(), Qt::AlignCenter);
        ui->plot->replot();
    }
}

void CScrollLineChart::vertScrollBarChanged(int value)
{
  if (qAbs(ui->plot->yAxis->range().center()+value/100.0) > 0.01) // if user is dragging plot, we don't want to replot twice
  {
    //起始位置、显示范围、对齐方式
    ui->plot->yAxis->setRange(-value/100.0, ui->plot->yAxis->range().size(), Qt::AlignLeft);
    ui->plot->replot();
  }
}

void CScrollLineChart::xAxisChanged(QCPRange range)
{
    //qDebug()<<"xAxisChanged xAxis:"<<m_lCurrentRang<<range<<ui->plot->xAxis->range().upper<<ui->plot->xAxis->range().lower<<ui->plot->xAxis->range().upper - ui->plot->xAxis->range().lower;
     //if(m_lCurrentRang != qRound(ui->plot->xAxis->range().size())){
     //    m_lCurrentRang = qRound(ui->plot->xAxis->range().size());
     //    if(m_lCurrentRang == 0){
     //        return;
     //    }
     //    int maxrange = qCeil(m_lMaxCount / m_lCurrentRang) + 1;  //+1是为了保证能够展示所有数据
     //    ui->horizontalScrollBar->setRange(0, maxrange);
     //    qDebug()<<"m_dCurrentCenter";
     //}
     ////qDebug()<<"m_dCurrentCenter"<<qRound(range.center() / m_lCurrentRang);
     //ui->horizontalScrollBar->setValue(qRound(range.center() / m_lCurrentRang));
    ui->horizontalScrollBar->setValue(qRound(range.center())); // adjust position of scroll bar slider
    ui->horizontalScrollBar->setPageStep(qRound(range.size())); // adjust size of scroll bar slider
}

void CScrollLineChart::yAxisChanged(QCPRange range)
{
  //ui->verticalScrollBar->setValue(qRound(-range.center()*100.0)); // adjust position of scroll bar slider
  //ui->verticalScrollBar->setPageStep(qRound(range.size()*100.0)); // adjust size of scroll bar slider
}

void CScrollLineChart::slotMousePress(QMouseEvent* e){

    if(ui->plot->selectedGraphs().size() > 0){
        double currentx = ui->plot->xAxis->pixelToCoord(e->pos().x());
        double currenty = ui->plot->yAxis->pixelToCoord(e->pos().y());
        //qDebug()<<"select:"<<ui->plot->selectedGraphs().first()->name()<<currentx<<currenty;
        QToolTip::showText(mapToGlobal(e->pos()), QString("X(%1)/Y(%2)").arg(currentx).arg(currenty));
    }
    //qDebug()<<"press xAxis:"<<mapToGlobal(e->pos()).rx()<<e->pos().x()<<ui->plot->xAxis->range().lower<<ui->plot->graph(0)->dataCount();

}

void CScrollLineChart::slotMouseWheel(QWheelEvent *e){
    //ui->plot->xAxis->setRangeUpper(ui->plot->graph(0)->dataCount());
    //ui->plot->xAxis->setRangeLower(0);
    /*if(ui->plot->xAxis->range().upper > ui->plot->graph(0)->dataCount() * 2){
        ui->plot->xAxis->setRange(0, 1000, Qt::AlignRight);
        ui->plot->replot();
    }*/
    //qDebug()<<"slotMouseWheel xAxis:"<<m_lCurrentRang<<ui->plot->xAxis->range().upper<<ui->plot->xAxis->range().lower<<ui->plot->xAxis->range().size();

}

void CScrollLineChart::mouseMoveEvent(QMouseEvent *e){
    //qDebug()<<"move:"<<e->pos();
    QVariant val;
    if(ui->plot->graph(0)->selectTest(e->pos(), false, &val) != -1.0){
        //qDebug()<<val.toDouble();
    }
    bool fundrang;
    //qDebug()<<"size:"<<e->y()<<ui->plot->height()<<ui->plot->graph(0)->getValueRange(fundrang).upper<<ui->plot->graph(0)->getValueRange(fundrang).lower;
    //qDebug()<<ui->plot->graph(0)->valueAxis()->range().upper<<ui->plot->graph(0)->valueAxis()->range().lower;
    //double inter_value = ui->plot->graph(0)->getValueRange(fundrang).upper - ui->plot->graph(0)->getValueRange(fundrang).lower;
    //double current_value = ui->plot->graph(0)->getValueRange(fundrang).upper - (((e->y()) * 1.0000 / (ui->plot->height())) * inter_value);
    double inter_value = ui->plot->graph(0)->valueAxis()->range().upper - ui->plot->graph(0)->valueAxis()->range().lower;
    double current_value = ui->plot->graph(0)->valueAxis()->range().upper - (((e->y() - 13) * 1.0000 / (ui->plot->height() - 35)) * inter_value);
    if(current_value > ui->plot->graph(0)->valueAxis()->range().upper){
        current_value = ui->plot->graph(0)->valueAxis()->range().upper;
    }
    if(current_value < ui->plot->graph(0)->valueAxis()->range().lower){
        current_value = ui->plot->graph(0)->valueAxis()->range().lower;
    }
    mTag1->updatePosition(current_value);
    mTag1->setText(QString::number(current_value, 'f', 4));
    ui->plot->replot();
}

void CScrollLineChart::genCursor()
{
    if(mTag1 != NULL || !isVisible()){
        return;
    }
    int len = ui->plot->axisRect()->axis(QCPAxis::atRight)->padding();
    //qDebug()<<ui->plot->axisRect()->size()<<len;
    ui->plot->axisRect()->axis(QCPAxis::atRight)->setPadding(66);
    mTag1 = new CAxisTag(ui->plot->axisRect()->axis(QCPAxis::atRight));
    mTag1->setPen(ui->plot->graph(0)->pen());
    ui->plot->setAttribute(Qt::WA_TransparentForMouseEvents);
    ui->plot->replot();
    mTag1->geneCursor(ui->plot->axisRect()->width());
    mTag1->updatePosition(ui->plot->yAxis->range().lower);
    mTag1->setText(QString::number(ui->plot->yAxis->range().lower, 'f', 4));
    ui->plot->replot();
    m_hasCursor = true;
    stopFlush();
}

void CScrollLineChart::delCursor()
{
    if(mTag1 != NULL){
        ui->plot->setAttribute(Qt::WA_TransparentForMouseEvents, false);
        delete mTag1;
        mTag1 = NULL;
        ui->plot->axisRect()->axis(QCPAxis::atRight)->setPadding(5);
        ui->plot->replot();
        m_hasCursor = false;
    }
    //若是实时，则开启刷新，否则不用刷新
    if(m_bIsRealFlush){
        startFlush();
    }
}

void CScrollLineChart::resizeEvent(QResizeEvent *e){
    if(m_hasCursor){
        //qDebug()<<"margins().left:"<<mPlot->axisRect()->margins().left()<<mPlot->axisRect()->axis(QCPAxis::atRight, 0)->padding()<< mPlot->axisRect()->axis(QCPAxis::atRight, 1)->offset();
        //qDebug()<<ui->plot->width() - ui->plot->axisRect()->margins().left() - 30 - 72<<mGraph1->dataCount();
        mTag1->geneCursor(ui->plot->axisRect()->width());
        //mTag2->geneCursor(mPlot->axisRect()->width());
        ui->plot->replot();
    }

}

void CScrollLineChart::appendData(double ddata, int graphindex){
    if(graphindex > m_graphNum){
        return;
    }

    //离线
    if(!m_bIsRealFlush){
        if(m_dUpperX > m_dLowerX){
            m_i64Counter++;
            if(m_i64Counter < m_dLowerX || m_i64Counter > m_dUpperX){
                return;
            }
        }

        if(m_dLowerY == m_dUpperY){
            ui->plot->graph(graphindex)->addData(ui->plot->graph(graphindex)->dataCount(), ddata);
        }else{
            if(ddata < m_dLowerY){
                ui->plot->graph(graphindex)->addData(ui->plot->graph(graphindex)->dataCount(), m_dLowerY);
            }else if(ddata > m_dUpperY){
                ui->plot->graph(graphindex)->addData(ui->plot->graph(graphindex)->dataCount(), m_dUpperY);
            }else{
                ui->plot->graph(graphindex)->addData(ui->plot->graph(graphindex)->dataCount(), ddata);
            }
        }
    }else{//实时
        //if(ui->plot->graph(graphindex)->dataCount() > MAX_DATA_SIZE){
        //    ui->plot->graph(graphindex)->data()->removeBefore(MAX_DATA_SIZE/2);
        //}
        ui->plot->graph(graphindex)->addData(ui->plot->graph(graphindex)->dataCount(), ddata);
    }
    //qDebug()<<"appendData:"<<ddata<<ui->plot->yAxis->range().lower<<m_dLowerY<<m_dUpperY;
    //m_bars->addData(m_bars->dataCount(), ddata);
    m_lMaxCount = ui->plot->graph(0)->dataCount();
}

void CScrollLineChart::slotTImeOut(){
    //仅在视图页面显示的时候绘制
    if(m_lMaxCount < 1 || !isVisible()){
        return;
    }
    //qDebug()<<"visible true";

    if(ui->plot->xAxis->range().upper < m_iWindRange || !m_bIsRealFlush){
        //qDebug()<<"m_bIsRealFlush"<<m_bIsRealFlush;
        ui->plot->xAxis->setRange(-m_iWindRange/10, m_lMaxCount + m_iWindRange / 5, Qt::AlignLeft);
    }else{
        ui->plot->xAxis->setRange(m_lMaxCount - m_iWindRange * 4/5, m_lMaxCount + m_iWindRange/5);
    }
    double dupper = 0.0;
    double dlower = 0.0;
    //计算y轴显示范围
    QVector<double> vMaxVals;
    QVector<double> vMinVals;
    QCP::SignDomain signDomain = QCP::sdBoth;
    bool foundRange;
    for(int i = 0; i < m_graphNum; i++){
        QCPRange newRange = ui->plot->graph(i)->getValueRange(foundRange, signDomain, ui->plot->graph()->keyAxis()->range());
        vMaxVals.append(newRange.upper);
        vMinVals.append(newRange.lower);
    }

    std::sort(vMaxVals.begin(), vMaxVals.end());
    dupper = vMaxVals.last();

    std::sort(vMinVals.begin(), vMinVals.end());
    dlower = vMinVals.first();


    //qDebug()<<"dupper-dlower"<<dupper<<dlower<<m_bIsRealFlush<<m_graphNum;
    //ui->plot->graph()->rescaleValueAxis(false, true);
    //qDebug()<<"dupper-dlower:"<<dupper<<dlower;

    double maxrange = qMax(qAbs(dupper), qAbs(dlower));
    double boardrange = maxrange * m_fmariny; //5%余量

    if(dupper < 0){
        ui->plot->yAxis->setRange(dlower - boardrange, dupper + boardrange);
    }else if(CTlhTools::isDoubleZero(dupper) && CTlhTools::isDoubleZero(dlower)){
        ui->plot->yAxis->setRange(-1, 1);
    }else if(CTlhTools::isDoubleZero(dupper)){
        ui->plot->yAxis->setRange(dlower - boardrange, -boardrange);
    }else if(CTlhTools::isDoubleZero(dlower)){
        ui->plot->yAxis->setRange( -boardrange, dupper + boardrange);
    }
    else{
        ui->plot->yAxis->setRange(dlower > 0 ?(dlower - boardrange):(dlower - boardrange), dupper + boardrange);
    }
        //qDebug()<<"MAX:"<<qMax(dupper, dlower)<<dupper<<dlower<<boardrange;

    if(!m_bIsRealFlush){
        ui->horizontalScrollBar->setDisabled(false);
    }
    ui->plot->replot();
}

void CScrollLineChart::reset(){
    for(int i = 0; i < m_graphNum; i++){
        ui->plot->graph(i)->data().data()->clear();
        ui->plot->graph(i)->rescaleAxes();
    }
}

void CScrollLineChart::stopFlush(){
    m_lCurrentRang = 0;
    m_bIsRealFlush = false;
    ui->horizontalScrollBar->setRange(0, m_lCurrentRang);
    ui->verticalScrollBar->setDisabled(false);
    ui->horizontalScrollBar->setDisabled(false);

    QVector<long> vMaxcounts;
    for(int i = 0; i < m_graphNum; i++){
        vMaxcounts.append(ui->plot->graph(i)->dataCount());
        //qDebug()<<"ui->plot->graph(i)->dataCount():"<<ui->plot->graph(i)->dataCount();
    }

    std::sort(vMaxcounts.begin(), vMaxcounts.end());

    m_lMaxCount =vMaxcounts.last();

    ui->horizontalScrollBar->setRange(0, m_lMaxCount);

    slotTImeOut();
    if(m_tflushTimer.isActive()){
        m_tflushTimer.stop();
    }
    m_iTimerId = -1;
}

void CScrollLineChart::startFlush(){
    m_bIsRealFlush = true;
    ui->verticalScrollBar->setDisabled(true);
    ui->horizontalScrollBar->setDisabled(true);
    if(!m_tflushTimer.isActive()){
        m_tflushTimer.start(120);
    }
}

void CScrollLineChart::setTitleName(QString tname, int index){
    if(index > m_graphNum){
        return;
    }
    ui->plot->graph(index)->setName(tname);
}

void CScrollLineChart::setValidScrollBar(bool val){
    //ui->verticalScrollBar->setVisible(val);
    ui->horizontalScrollBar->setVisible(val);
}

void CScrollLineChart::ressetHRange(qint64 dmin, qint64 dmax, float marinrate){
    qDebug()<<"ressetHRange:"<<dmin<<dmax;
    m_dLowerX = dmin;
    m_dUpperX = dmax;
    m_fmarinx = marinrate;
    m_i64Counter = 0l;
}
void CScrollLineChart::ressetVRange(double dmin, double dmax, float marinrate){
    m_dLowerY = dmin;
    m_dUpperY = dmax;
    m_fmariny = marinrate;
}

void CScrollLineChart::getDataRange(QVector<double> &vrange){
    if(m_dUpperX == 0){
        vrange.append(0);
        bool fundrang;
        double dupper = ui->plot->graph(0)->getValueRange(fundrang).upper;
        double dlower = ui->plot->graph(0)->getValueRange(fundrang).lower;
        vrange.append(ui->plot->graph(0)->dataCount());
        vrange.append(0.0);
        vrange.append(dlower);
        vrange.append(dupper);
        vrange.append(m_fmariny);
    }else{
        vrange.append(m_dLowerX);
        vrange.append(m_dUpperX);
        vrange.append(m_fmarinx);
        vrange.append(m_dLowerY);
        vrange.append(m_dUpperY);
        vrange.append(m_fmariny);
    }
    //qDebug()<<"getDataRange"<<dupper<<dlower;
}

void CScrollLineChart::setWindRange(int widthrange){
    m_iWindRange = widthrange;
}
