﻿#include "c55aa00protocol.h"
#include "csubpagemanager.h"
#include "cdritestpage.h"
#include "cdrivetestpage.h"
#include "cdataanapage.h"
#include "cgyrocalibpage.h"
#include "ctlhtools.h"
#include <QDebug>

C55aa00Protocol::C55aa00Protocol(QObject *parent) : CBaseProtocol(parent)
{

     CDriTestPage *dripage = static_cast<CDriTestPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DriTest"));
     CDataAnaPage *danapage = static_cast<CDataAnaPage *>(CSubPageManager::GetInstance()->GetObjectbyName("tab_DataAna"));
    if(dripage != NULL){
        qDebug()<<"connect C55aa00Protocol";
        connect(this, &C55aa00Protocol::sigDataUpdate, dripage, &CDriTestPage::slotDataShow);
        connect(this, &C55aa00Protocol::sigStatuUpdate,  dripage, &CDriTestPage::slotStatusShow);
    }
    m_slProtoKeys.append(QString::fromLocal8Bit("帧序号:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("陀螺:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("温度:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("陀螺平均值:"));
    m_slProtoKeys.append(QString::fromLocal8Bit("温度平均值:"));

    m_slFreqKeys.append(QString::fromLocal8Bit("采样序号:"));
    m_slFreqKeys.append(m_slProtoKeys.mid(3, 4));

    m_iFramIndx = -1;
    setFrameErr(E_FRAME_OK);
    m_icatFreqCounts = 0;
    m_lcatCurrCounts = 0l;
    m_davggyr = 0.0;
    m_davgtemp = 0.0;
}

bool C55aa00Protocol::setProtoLength(const QByteArray &barr){
    m_bMsgHead = barr.mid(0, 2);
    m_uMsgLen = sizeof (Stru55aa00);

    return true;
}

C55aa00Protocol::~C55aa00Protocol(){
    qDebug()<<"~CA5A500Protocol";
}

void C55aa00Protocol::paseMsg(const QByteArray msg){
    Stru55aa00 st_55aa00;
    //qDebug()<<"paseMsg:"<<msg.toHex(' ');
}

bool C55aa00Protocol::preInit(){
    return true;
}
