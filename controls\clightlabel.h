﻿#ifndef CLIGHTLABEL_H
#define CLIGHTLABEL_H

#include <QObject>
#include <QLabel>

/*
* 视图模式下圆形灯控件
*/
class CLightLabel : public QLabel
{
    Q_OBJECT
public:
    explicit CLightLabel(QWidget *parent = nullptr);

    void paintEvent(QPaintEvent *event) override;

    void drawBorderOut(QPainter *painter);
    void drawBorderIn(QPainter *painter);
    void drawBg(QPainter *painter);
    void drawText(QPainter *painter);
    void drawOverlay(QPainter *painter);
    void setLightGreen();
    void setBgColor(const QColor &bgColor);
    void settext(QString text);

signals:

private:
    QString m_stext;               //文本
    QColor textColor;           //文字颜色
    QColor alarmColor;          //报警颜色
    QColor normalColor;         //正常颜色

    QColor borderOutColorStart; //外边框渐变开始颜色
    QColor borderOutColorEnd;   //外边框渐变结束颜色
    QColor borderInColorStart;  //里边框渐变开始颜色
    QColor borderInColorEnd;    //里边框渐变结束颜色
    QColor bgColor;             //背景颜色

    bool showRect;              //显示成矩形
    bool canMove;               //是否能够移动
    bool showOverlay;           //是否显示遮罩层
    QColor overlayColor;        //遮罩层颜色

};

#endif // CLIGHTLABEL_H
