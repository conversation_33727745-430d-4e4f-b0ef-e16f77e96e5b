﻿#ifndef ATTITUDEWIDGET_H
#define ATTITUDEWIDGET_H

#include <QWidget>
#include <QPainter>
#include <cmath>
#include <QDebug>

/*
*飞行视图页面绘制类
*/
class CAttitudeWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CAttitudeWidget(QWidget *parent = nullptr);
    /*设置姿态参数*/
    void setAttitude(float pitch, float roll);
    void paintEvent(QPaintEvent *event) override;
    void drawAttitudeIndicator(QPainter &painter);
    void drawHorizon(QPainter &painter, float pitch, int side);
    void drawPitchScale(QPainter &painter, float pitch, int side);
    void drawFixedElements(QPainter &painter, int centerX, int centerY, int side);
    void drawMarinGrid(QPainter &painter);
    void drawAlVal(QPainter &painter, double pitch, double roll, double heading);
    void drawRollScale(QPainter &painter, int centerX, int centerY, int side);
    void drawArraw(QPainter &painter, int centerX, int centerY, int side);

private:
    float m_fpitch;
    float m_froll;

signals:

};

#endif // ATTITUDEWIDGET_H
