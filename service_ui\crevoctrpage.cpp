﻿#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QMultiMap>
#include "cturntparseservice.h"
#include "crevoctrpage.h"
#include "ui_crevoctrpage.h"
#include "csseriservice.h"
#include "cstatuslabel.h"
#include "ctlhtools.h"
#include <QApplication>
#include <QDesktopWidget>
#include "QXlsx.h"

CRevoCtrPage::CRevoCtrPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CRevoCtrPage)
{
    ui->setupUi(this);

    m_oseriPage = NULL;

    init();

    ui->bt_instop->setDisabled(true);
    ui->bt_outstop->setDisabled(true);

    ui->bt_instart->setDisabled(true);
    ui->bt_outstart->setDisabled(true);

    ui->bt_stopwork->setDisabled(true);

    m_bOutInPlace = false;
    m_bInEnable = false;
    m_bOutInPlace = false;
    m_bInInPlace = false;
    m_sPortn = "";
    m_sTurnServ = NULL;

    ui->pb_testprecess->setValue(100);

    m_dTurntdlg = new CTurntableJEditDialog(this);

    connect(ui->cb_incontrol, &QComboBox::currentTextChanged, this, &CRevoCtrPage::slotInMotionModChange);
    connect(ui->cb_outcontrol, &QComboBox::currentTextChanged, this, &CRevoCtrPage::slotOutMotionModChange);

    //QDesktopWidget *dswid = QApplication::desktop();
    //qDebug()<<dswid->width()<<dswid->height()<<ui->lab_outstat->width()<<ui->lab_outstat->height();
    //ui->lab_outstat->resize(200,200);

}

CRevoCtrPage::~CRevoCtrPage()
{
    delete ui;
}

void CRevoCtrPage::setSeriPage(CcserialPage *page){
    m_oseriPage = page;
}

void CRevoCtrPage::init(){
    for(QObject *obj:findChildren<QLabel *>(QRegularExpression("lab_status*"))){
        QLabel *lab = static_cast<QLabel *>(obj);
        lab->setStyleSheet("background-color:#228B22;");
        //qDebug()<<obj->objectName();
    }

}

void CRevoCtrPage::slotOutMotionModChange(const QString &text){
    if(text == QString::fromLocal8Bit("速率")){
        ui->lab_site1->setVisible(false);
        ui->ed_site1->setVisible(false);
        ui->lab_sname1->setVisible(false);
        ui->lab_speed1->setText(QString::fromLocal8Bit("目标速度"));
        ui->lab_accel1->setText(QString::fromLocal8Bit("目标加速度"));
        ui->ed_speed1->setText("10.0000");
        ui->ed_accel1->setText("10.0000");
    }else if(text == QString::fromLocal8Bit("摇摆")){
        ui->lab_site1->setVisible(false);
        ui->ed_site1->setVisible(false);
        ui->lab_sname1->setVisible(false);
        ui->lab_speed1->setText(QString::fromLocal8Bit("目标幅值"));
        ui->lab_accel1->setText(QString::fromLocal8Bit("目标频率  "));
        ui->ed_speed1->setText("1.0000");
        ui->ed_accel1->setText("1.0000");
    }else{
        ui->lab_site1->setVisible(true);
        ui->ed_site1->setVisible(true);
        ui->lab_sname1->setVisible(true);
        ui->lab_speed1->setText(QString::fromLocal8Bit("目标速度"));
        ui->lab_accel1->setText(QString::fromLocal8Bit("目标加速度"));
        ui->ed_site1->setText("0.0000");
        ui->ed_speed1->setText("10.0000");
        ui->ed_accel1->setText("10.0000");
    }

}

void CRevoCtrPage::slotInMotionModChange(const QString &text){
    if(text == QString::fromLocal8Bit("速率")){
        ui->lab_site2->setVisible(false);
        ui->ed_site2->setVisible(false);
        ui->lab_sname2->setVisible(false);
        ui->lab_speed2->setText(QString::fromLocal8Bit("目标速率"));
        ui->lab_accel2->setText(QString::fromLocal8Bit("目标加速度"));
        ui->ed_speed2->setText("10.0000");
        ui->ed_accel2->setText("10.0000");
    }else if(text == QString::fromLocal8Bit("摇摆")){
        ui->ed_site2->setVisible(false);
        ui->lab_site2->setVisible(false);
        ui->lab_sname2->setVisible(false);
        ui->lab_speed2->setText(QString::fromLocal8Bit("目标幅值"));
        ui->lab_accel2->setText(QString::fromLocal8Bit("目标频率  "));
        ui->ed_speed2->setText("1.0000");
        ui->ed_accel2->setText("1.0000");

    }else{
        ui->lab_site2->setVisible(true);
        ui->ed_site2->setVisible(true);
        ui->lab_sname2->setVisible(true);
        ui->lab_speed2->setText(QString::fromLocal8Bit("目标速率"));
        ui->lab_accel2->setText(QString::fromLocal8Bit("目标加速度"));
        ui->ed_site2->setText("0.0000");
        ui->ed_speed2->setText("10.0000");
        ui->ed_accel2->setText("10.0000");
    }
}

void CRevoCtrPage::slotDataShow(const QStringList datakeys, const QStringList dataValues, const QString sportN, const int protoindex){
    if(dataValues.size() < 34){
        qDebug()<<"dataValues size is:"<<dataValues.size();
        return;
    }

    if(m_sTurnServ == NULL || m_sPortn != sportN){
        m_sPortn = sportN;
        //将原来的信号槽解绑
        if(m_sTurnServ != NULL){
            disconnect(this, &CRevoCtrPage::SigSeriWrite, m_sTurnServ, &CSseriService::slotSeriWrite);
        }
        m_sTurnServ = m_oseriPage->getServByPortN(sportN);
        //重新绑定到新信号与槽
        if(m_sTurnServ != NULL){
            connect(this, &CRevoCtrPage::SigSeriWrite, m_sTurnServ, &CSseriService::slotSeriWrite, Qt::BlockingQueuedConnection);
        }
    }
    //ui->label_1->setText(dataValues.at(0));
    //ui->label_41->setText(dataValues.at(1));
    //ui->label_41->setText(dataValues.at(2));
    //ui->label_41->setText(dataValues.at(3));
    //ui->label_41->setText(dataValues.at(4));
    //ui->label_41->setText(dataValues.at(5));
    //ui->label_41->setText(dataValues.at(6));
    //ui->label_41->setText(dataValues.at(7));
    //"background-color:#228B22;"
    //QString sColorPre = "background-color:";
    //7-内轴运动状态
    ui->bt_inenable->setText(dataValues.at(8));
    if(dataValues.at(8) == QString::fromLocal8Bit("断开")){
        //已使能
        m_bInEnable = true;
        ui->bt_instart->setDisabled(false);
    }else{
        m_bInEnable = false;
    }

    QStringList inslColor;
    inslColor.append(dataValues.at(10));
    inslColor.append(dataValues.at(11));
    inslColor.append(dataValues.at(9));
    //是否到位
    if(dataValues.at(9) == "#228B22"){
        m_bInInPlace = true;
    }else{
        m_bInInPlace = false;
    }
    inslColor.append(dataValues.at(12));
    inslColor.append(dataValues.at(13));
    inslColor.append(dataValues.at(16));
    inslColor.append(dataValues.at(14));
    inslColor.append(dataValues.at(15));
    inslColor.append(dataValues.at(17));
    inslColor.append(dataValues.at(18));
    ui->lab_instat->statFlush(inslColor, dataValues.at(7));
    /*ui->lab_status11->setStyleSheet(sColorPre + dataValues.at(9) + ";");
    ui->lab_status12->setStyleSheet(sColorPre + dataValues.at(10) + ";");
    ui->lab_status13->setStyleSheet(sColorPre + dataValues.at(11) + ";");
    ui->lab_status14->setStyleSheet(sColorPre + dataValues.at(12) + ";");
    ui->lab_status15->setStyleSheet(sColorPre + dataValues.at(13) + ";");
    ui->lab_status16->setStyleSheet(sColorPre + dataValues.at(14) + ";");
    ui->lab_status17->setStyleSheet(sColorPre + dataValues.at(15) + ";");
    ui->lab_status18->setStyleSheet(sColorPre + dataValues.at(16) + ";");
    ui->lab_status12->setStyleSheet(sColorPre + dataValues.at(17) + ";");
    ui->lab_status19->setStyleSheet(sColorPre + dataValues.at(18) + ";");
    */
    ui->lineEdit_8->setText(dataValues.at(19));
    ui->lineEdit_7->setText(dataValues.at(20));
    //qDebug()<<dataValues.at(20).mid(0, 4);
    if(m_sInRunStatus == E_stStop && dataValues.at(20).mid(0, 4) == "0.00"){
        m_sInRunStatus = E_stStillness;
        //ui->lab_motionst2->setText(QString::fromLocal8Bit("静止"));
    }
    //21-外轴运动状态
    ui->bt_outenable->setText(dataValues.at(22));
    if(dataValues.at(22) == QString::fromLocal8Bit("断开")){
        //已使能
        m_bOutEnable = true;
        ui->bt_outstart->setDisabled(false);
    }else{
        m_bOutEnable = false;
    }

    QStringList outlColor;
    outlColor.append(dataValues.at(24));
    outlColor.append(dataValues.at(25));
    outlColor.append(dataValues.at(23));
    //是否到位
    if(dataValues.at(23) == "#228B22"){
        m_bOutInPlace = true;
    }else{
        m_bOutInPlace = false;
    }
    outlColor.append(dataValues.at(26));
    outlColor.append(dataValues.at(27));
    outlColor.append(dataValues.at(30));
    outlColor.append(dataValues.at(28));
    outlColor.append(dataValues.at(29));
    outlColor.append(dataValues.at(31));
    outlColor.append(dataValues.at(32));

    ui->lab_outstat->statFlush(outlColor, dataValues.at(21));
    /*ui->lab_status2->setStyleSheet(sColorPre + dataValues.at(23) + ";");
    ui->lab_status1->setStyleSheet(sColorPre + dataValues.at(24) + ";");
    ui->lab_status4->setStyleSheet(sColorPre + dataValues.at(25) + ";");
    ui->lab_status5->setStyleSheet(sColorPre + dataValues.at(26) + ";");
    ui->lab_status6->setStyleSheet(sColorPre + dataValues.at(27) + ";");
    ui->lab_status7->setStyleSheet(sColorPre + dataValues.at(28) + ";");
    ui->lab_status8->setStyleSheet(sColorPre + dataValues.at(29) + ";");
    ui->lab_status9->setStyleSheet(sColorPre + dataValues.at(30) + ";");
    ui->lab_status3->setStyleSheet(sColorPre + dataValues.at(31) + ";");
    ui->lab_status20->setStyleSheet(sColorPre + dataValues.at(32) + ";");
    */
    ui->lineEdit_6->setText(dataValues.at(33));
    ui->lineEdit_5->setText(dataValues.at(34));
    if(m_sOutRunStatus == E_stStop && dataValues.at(34).mid(0, 4) == "0.00"){
        m_sOutRunStatus = E_stStillness;
        //ui->lab_motionst1->setText(QString::fromLocal8Bit("静止"));
    }
}



void CRevoCtrPage::on_bt_inenable_clicked()
{
    bool result = false;
    if(m_bInEnable){
        result = enable_CmdTo_Turntable(0, 0);
    }else{
        result = enable_CmdTo_Turntable(0, 1);
    }
    if(!result){
        QMessageBox::critical(this, QString::fromLocal8Bit("串口使能失败"), QString::fromLocal8Bit("请检查串口连接是否正常开启！"));
    }

}

void CRevoCtrPage::on_bt_outenable_clicked()
{
    bool result = false;
    if(m_bOutEnable){
        result = enable_CmdTo_Turntable(1, 0);
    }else{
        result = enable_CmdTo_Turntable(1, 1);
    }
    if(!result){
        QMessageBox::critical(this, QString::fromLocal8Bit("串口使能失败"), QString::fromLocal8Bit("请检查串口连接是否正常开启！"));
    }
}

void CRevoCtrPage::on_bt_outstart_clicked()
{
    if(!m_bOutEnable){
        QMessageBox::critical(this, QString::fromLocal8Bit("执行失败"), QString::fromLocal8Bit("外轴未使能，请先将外轴使能！"));
        return;
    }
    float fsit = ui->ed_site1->text().toFloat();
    float fspeed = ui->ed_speed1->text().toFloat();  //幅值
    float faccel = ui->ed_accel1->text().toFloat();  //速率
    QString optype = ui->cb_outcontrol->currentText();
    MotionStatus oldst = m_sOutRunStatus;
    bool opresult;

    if((optype == QString::fromLocal8Bit("摇摆")) && (qAbs(faccel) > 5.0000 || qAbs(fspeed) > 20.0000)){
        QString validParm = QString::fromLocal8Bit("摇摆有效参数: 幅值<=|20| °,频率<=|5|Hz");
        QMessageBox::critical(this, QString::fromLocal8Bit("参数无效"), validParm);
        return;
    }else{
        QString validParm = QString::fromLocal8Bit("外轴有效参数:位置<=|360| °,外轴速率<=|360| °/s，加速度<=|100| °/ss");
        if(qAbs(fsit) > 360.0000 || qAbs(fspeed) > 360.0000 || qAbs(faccel) > 100.0000){
            QMessageBox::critical(this, QString::fromLocal8Bit("参数无效"), validParm);
            return;
        }
    }
    //qDebug()<<ui->cb_outcontrol->currentText();
    //qDebug()<<"params"<<optype<<fsit<<fspeed<<faccel;
    if(optype == QString::fromLocal8Bit("位置")){
        m_sOutRunStatus = E_stSit;
        opresult = motion_CmdTo_Turntable(1, E_Sit, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("速率")){
        m_sOutRunStatus = E_stSpeed;
        opresult = motion_CmdTo_Turntable(1, E_Speed, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("摇摆")){
        m_sOutRunStatus = E_stSwing;
        opresult = motion_CmdTo_Turntable(1, E_Swing, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("找零")){
        m_sOutRunStatus = E_stGiv;
        opresult = motion_CmdTo_Turntable(1, E_GiveChg, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("相对位置")){
        m_sOutRunStatus = E_stRpos;
        opresult = motion_CmdTo_Turntable(1, E_RelatPos, fsit, fspeed, faccel);
    }else{
        motion_CmdTo_Turntable(1, E_NoChange, fsit, fspeed, faccel);
    }

    if(opresult){
        ui->cb_outcontrol->setDisabled(true);
        //ui->lab_motionst1->setText(optype);
        ui->bt_outstop->setDisabled(false);
        if(m_sOutRunStatus != E_stSpeed){
            ui->bt_outstart->setDisabled(true);
            ui->ed_site1->setDisabled(true);
            ui->ed_speed1->setDisabled(true);
            ui->ed_accel1->setDisabled(true);
        }
    }else{
        m_sOutRunStatus = oldst;
    }
}

void CRevoCtrPage::on_bt_outstop_clicked()
{
    if(motion_CmdTo_Turntable(1, E_Stop, 0.0, 0.0, 0.0)){
        ui->bt_outstop->setDisabled(true);
        ui->bt_outstart->setDisabled(false);
    }
    ui->cb_outcontrol->setDisabled(false);
    ui->ed_site1->setDisabled(false);
    ui->ed_speed1->setDisabled(false);
    ui->ed_accel1->setDisabled(false);
    m_sOutRunStatus = E_stStop;
    //ui->lab_motionst1->setText(QString::fromLocal8Bit("停车"));
}

void CRevoCtrPage::on_bt_instart_clicked()
{
    if(!m_bInEnable){
        QMessageBox::critical(this, QString::fromLocal8Bit("执行失败"), QString::fromLocal8Bit("内轴未使能，请先将内轴使能！"));
        return;
    }
    float fsit = ui->ed_site2->text().toFloat();
    float fspeed = ui->ed_speed2->text().toFloat();
    float faccel = ui->ed_accel2->text().toFloat();
    QString optype = ui->cb_incontrol->currentText();
    bool opresult;
    MotionStatus oldst = m_sInRunStatus;
    //qDebug()<<qAbs(fsit)<<qAbs(fspeed)<<qAbs(faccel);
    if((optype == QString::fromLocal8Bit("摇摆")) && (qAbs(faccel) > 5.0000 || qAbs(fspeed) > 20.0000)){
        QString validParm = QString::fromLocal8Bit("摇摆有效参数: 幅值<=|20| °,频率<=|5|Hz");
        QMessageBox::critical(this, QString::fromLocal8Bit("参数无效"), validParm);
        return;
    }else{
        if(qAbs(fsit) > 360.0000 || qAbs(fspeed) > 500.0000 || qAbs(faccel) > 100.0000){
            QString validParm = QString::fromLocal8Bit("内轴有效参数:位置<=|360.00| °,内轴速率<=|500.00| °/s，加速度<=|100| °/ss");
            QMessageBox::critical(this, QString::fromLocal8Bit("参数无效"), validParm);
            return;
        }
    }
    //qDebug()<<"params"<<optype<<fsit<<fspeed<<faccel;
    if(optype == QString::fromLocal8Bit("位置")){
        m_sInRunStatus = E_stSit;
        opresult = motion_CmdTo_Turntable(0, E_Sit, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("速率")){
        m_sInRunStatus = E_stSpeed;
        opresult = motion_CmdTo_Turntable(0, E_Speed, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("摇摆")){
        m_sInRunStatus = E_stSwing;
        opresult = motion_CmdTo_Turntable(0, E_Swing, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("找零")){
        m_sInRunStatus = E_stGiv;
        opresult = motion_CmdTo_Turntable(0, E_GiveChg, fsit, fspeed, faccel);
    }else if(optype == QString::fromLocal8Bit("相对位置")){
        m_sInRunStatus = E_stRpos;
        opresult = motion_CmdTo_Turntable(0, E_RelatPos, fsit, fspeed, faccel);
    }else{
        opresult = motion_CmdTo_Turntable(0, E_NoChange, fsit, fspeed, faccel);
    }
    if(opresult){
        if(m_sInRunStatus != E_stSpeed){
            ui->bt_instart->setDisabled(true);
            ui->ed_site2->setDisabled(true);
            ui->ed_speed2->setDisabled(true);
            ui->ed_accel2->setDisabled(true);
        }
        ui->bt_instop->setDisabled(false);
        ui->cb_incontrol->setDisabled(true);
        //ui->lab_motionst2->setText(optype);
    }else{
        m_sInRunStatus = oldst;
    }
}

void CRevoCtrPage::on_bt_instop_clicked()
{
    if(motion_CmdTo_Turntable(0, E_Stop, 0.0, 0.0, 0.0)){
        ui->bt_instop->setDisabled(true);
        ui->bt_instart->setDisabled(false);
    }
    ui->cb_incontrol->setDisabled(false);
    ui->ed_site2->setDisabled(false);
    ui->ed_speed2->setDisabled(false);
    ui->ed_accel2->setDisabled(false);
    m_sInRunStatus = E_stStop;
    //ui->lab_motionst2->setText(QString::fromLocal8Bit("停车"));
}


bool CRevoCtrPage::enable_CmdTo_Turntable(int turnType, int optype){
    int cmdlen = 46;
    int sendlen = cmdlen;
    char cmdbuf[47] = {0};
    cmdbuf[0] = 0xaa; //head1
    cmdbuf[1] = 0x55; //head2
    cmdbuf[2] = 0x2E; //len
    cmdbuf[3] = 0x01; //dsp编号
    cmdbuf[4] = 0xa1; //命令字
    //内轴 不需要操作此轴，轴号和命令默认0x00即可
    if(m_sPortn.isEmpty()){
        return false;
    }
    if(turnType == 0){
        cmdbuf[5] = 0xC1;
        if(optype == 1){
            //使能
            cmdbuf[6] = 0x01;
        }else{
            //断开
            cmdbuf[6] = 0x02;
        }
    }

    //外轴 不需要操作此轴，轴号和命令默认0x00即可
    if(turnType == 1){
        cmdbuf[9] = 0xC3;
        if(optype == 1){
           //使能
           cmdbuf[10] = 0x01;
        }else{
            //断开
            cmdbuf[10] = 0x02;
        }
    }

    for (int i = 2;i < cmdlen - 2 ;i++) {
        cmdbuf[44] += cmdbuf[i];
    }

    cmdbuf[45] = 0xfe;

    if(m_sTurnServ == NULL){
        return false;
    }
    QByteArray tarr;
    tarr.append(cmdbuf, 46);
    qDebug()<<tarr.size()<<tarr.toHex(' ');
    emit SigSeriWrite(cmdbuf, sendlen);
    return sendlen == cmdlen;
}

bool CRevoCtrPage::motion_CmdTo_Turntable(int turnType, MotionType optype, float parm1, float parm2, float parm3){
    int cmdlen = 46;
    int sendlen = cmdlen;
    char cmdbuf[55] = {0};
    cmdbuf[0] = 0xaa; //head1
    cmdbuf[1] = 0x55; //head2
    cmdbuf[2] = 0x2E; //len
    cmdbuf[3] = 0x01; //dsp编号
    cmdbuf[4] = 0xa2; //命令字
    //内轴 不需要操作此轴，轴号和命令默认0x00即可
    if(turnType == 0){
        switch (optype) {
        case E_NoChange:
            cmdbuf[5] = 0x00;
            break;
        case E_Stop:
            cmdbuf[5] = 0xD0;
            break;
        case E_Sit:
            cmdbuf[5] = 0xD1;
            ::memcpy(cmdbuf + 6, &parm1, 4);
            ::memcpy(cmdbuf + 10, &parm2, 4);
            ::memcpy(cmdbuf + 14, &parm3, 4);
            break;
        case E_Speed:
            cmdbuf[5] = 0xD2;
            ::memcpy(cmdbuf + 6, &parm2, 4);
            ::memcpy(cmdbuf + 10, &parm3, 4);
            break;
        case E_Swing:
            cmdbuf[5] = 0xD3;
            ::memcpy(cmdbuf + 6, &parm2, 4);
            ::memcpy(cmdbuf + 10, &parm3, 4);
            break;
        case E_GiveChg:
            /*用位置命令替代找零，防止转台异常
             * cmdbuf[5] = 0xD4;
            ::memcpy(cmdbuf + 6, &parm2, 4);
            ::memcpy(cmdbuf + 10, &parm3, 4);
            */
            cmdbuf[5] = 0xD1;
            parm1 = 0.00;
            parm2 = 10.00;
            parm3 = 10.00;
            ::memcpy(cmdbuf + 6, &parm1, 4);
            ::memcpy(cmdbuf + 10, &parm2, 4);
            ::memcpy(cmdbuf + 14, &parm3, 4);
            break;
        case E_RelatPos:
            cmdbuf[5] = 0xD5;
            ::memcpy(cmdbuf + 6, &parm1, 4);
            ::memcpy(cmdbuf + 10, &parm2, 4);
            ::memcpy(cmdbuf + 14, &parm3, 4);
            break;
        default:
            cmdbuf[5] = 0x00;
            break;
        }
    }

    //外轴 不需要操作此轴，轴号和命令默认0x00即可
    if(turnType == 1){
        switch (optype) {
        case E_NoChange:
            cmdbuf[31] = 0x00;
            break;
        case E_Stop:
            cmdbuf[31] = 0xD0;
            break;
        case E_Sit:
            cmdbuf[31] = 0xD1;
            ::memcpy(cmdbuf + 32, &parm1, 4);
            ::memcpy(cmdbuf + 36, &parm2, 4);
            ::memcpy(cmdbuf + 40, &parm3, 4);
            break;
        case E_Speed:
            cmdbuf[31] = 0xD2;
            ::memcpy(cmdbuf + 32, &parm2, 4);
            ::memcpy(cmdbuf + 36, &parm3, 4);
            break;
        case E_Swing:
            cmdbuf[31] = 0xD3;
            ::memcpy(cmdbuf + 32, &parm2, 4);
            ::memcpy(cmdbuf + 36, &parm3, 4);
            break;
        case E_GiveChg:
            /*用位置命令替代找零，防止转台异常
             * cmdbuf[31] = 0xD4;
            ::memcpy(cmdbuf + 6, &parm2, 4);
            ::memcpy(cmdbuf + 10, &parm3, 4);
            */
            cmdbuf[31] = 0xD1;
            parm1 = 0.00;
            parm2 = 10.00;
            parm3 = 10.00;
            ::memcpy(cmdbuf + 6, &parm1, 4);
            ::memcpy(cmdbuf + 10, &parm2, 4);
            ::memcpy(cmdbuf + 14, &parm3, 4);
            break;
        case E_RelatPos:
            cmdbuf[31] = 0xD5;
            ::memcpy(cmdbuf + 32, &parm1, 4);
            ::memcpy(cmdbuf + 36, &parm2, 4);
            ::memcpy(cmdbuf + 40, &parm3, 4);
            break;
        default:
            cmdbuf[5] = 0x00;
            break;
        }
    }

    for (int i = 2;i < cmdlen - 2 ;i++) {
        cmdbuf[44] += cmdbuf[i];
    }

    cmdbuf[45] = 0xfe;
    QByteArray tarr;
    tarr.append(cmdbuf, 46);

    //qDebug()<<"motion_CmdTo_Turntable"<<m_sTurnServ<<tarr.toHex(' ');
    if(m_sTurnServ == NULL){
        return false;
    }

    emit SigSeriWrite(cmdbuf, sendlen);
    //qDebug()<<"motion_CmdTo_Turntable end"<<m_sTurnServ;
    return sendlen == cmdlen;

}

bool CRevoCtrPage:: clear_CmdTo_Turntable(int turnType, int optype){
    int cmdlen = 46;
    int sendlen = cmdlen;
    char cmdbuf[55] = {0};
    cmdbuf[0] = 0xaa; //head1
    cmdbuf[1] = 0x55; //head2
    cmdbuf[2] = 0x2E; //len
    cmdbuf[3] = 0x01; //dsp编号
    cmdbuf[4] = 0xa2; //命令字
    //内轴 不需要操作此轴，轴号和命令默认0x00即可
    //0-内轴 1-外轴 3-取消告警
    switch(turnType){
    case 0:
        cmdbuf[5] = 0xC1;
        if(optype == 0){
            cmdbuf[6] = 0x01;  //清零
        }else{
            cmdbuf[6] = 0x02;  //还原
        }
        break;
    case 1:
        cmdbuf[9] = 0xC3;
        if(optype == 0){
            cmdbuf[10] = 0x01;  //清零
        }else{
            cmdbuf[10] = 0x02;  //还原
        }
        break;
    default:
        cmdbuf[23] = 0x01;
    }

    for (int i = 2;i < cmdlen - 2 ;i++) {
        cmdbuf[44] += cmdbuf[i];
    }

    cmdbuf[45] = 0xfe;

    QPointer<CSseriService> serv = m_oseriPage->getServByPortN(m_sPortn);
    if(serv == NULL){
        return false;
    }

    emit SigSeriWrite(cmdbuf, sendlen);
    return sendlen == cmdlen;

}

QString CRevoCtrPage::statusEToStr(MotionStatus es){
    switch (es) {
        case E_stIdle:       //空闲
            return QString::fromLocal8Bit("空闲");
        case E_stStillness:  //静止
            return QString::fromLocal8Bit("静止");
        case E_stSit:        //位置
            return QString::fromLocal8Bit("位置");
        case E_stSpeed:      //速率
            return QString::fromLocal8Bit("速率");
        case E_stSwing:      //摇摆
            return QString::fromLocal8Bit("摇摆");
        case E_stGiv:        //找零
            return QString::fromLocal8Bit("找零");
        case E_stRpos:       //相对位置
            return QString::fromLocal8Bit("相对位置");
        case E_stStop:        //停止
            return QString::fromLocal8Bit("停止");
        default:
            return QString::fromLocal8Bit("空闲");
    }
}


void CRevoCtrPage::on_bt_importfile_clicked()
{
    QString currentDir = QDir::currentPath();
    m_sWorkFiles = QFileDialog::getOpenFileNames(this, QString::fromLocal8Bit("打开脚本文件"), currentDir, tr("Text or Xlsx files(*.txt *.xlsx)"));
    if(m_sWorkFiles.size() > 0){
        on_bt_editfile_clicked();
        QString filenames;
        for(auto str:m_sWorkFiles){
            filenames.append(str);
        }
        ui->ed_workfile->setText(filenames);
    }
}

void CRevoCtrPage::on_bt_editfile_clicked()
{
    QString filedata;
    QByteArray readbuf(CMDLINE_MAXLENGTH, 0x00);
    int readlen = -1;
    QString sautojob;
    for(auto filename:m_sWorkFiles){
        qDebug()<<filename;
        if(filename.endsWith(".txt")){
            QFile jobfile(filename);
            if(!jobfile.open(QIODevice::ReadOnly)){
                QMessageBox::critical(this, QString::fromLocal8Bit("文件打开失败"), QString::fromLocal8Bit("请检查文件是否存在！"));
                return;
            }
            do{
                //readbuf.clear();
                readlen = jobfile.readLine(readbuf.data(), CMDLINE_MAXLENGTH);
                if(!CTlhTools::isValidString(readbuf, QString(".,;-_#\\r\\n"))){
                    QMessageBox::critical(this, QString::fromLocal8Bit("内容格式错误"), QString::fromLocal8Bit("请检查文件内容！") + readbuf);
                    filedata.clear();
                    break;
                }
                filedata.append(readbuf);
            }while(readlen != -1);
        }else if(filename.endsWith(".xlsx")){
            QXlsx::Document xlsx(filename);
            xlsx.selectSheet("Sheet1");
            QXlsx::Worksheet *worksheet = xlsx.currentWorksheet();
            QVariant value;
            QString serno = 0;
            int maxrow = worksheet->dimension().rowCount();
            int maxcol = worksheet->dimension().columnCount();
            qDebug()<<"row-col"<<maxrow<<maxcol;
            for (int i = 2; i<maxrow; i++) {
                QString turn_type;
                QString inop_type;
                QString outop_type;
                QString turn_value;
                QString cat_type;
                QString aix_str = "x";
                QString cmd_data;
                QString until_reach;
                QString cat_timestr;
                QString file_prefix;
                bool stop_flag = false;
                if(worksheet->cellAt(i, 1) == NULL || worksheet->cellAt(i, 1)->value().isNull() || !worksheet->cellAt(i, 1)->value().isValid()){
                    break;
                }
                for (int j = 1;j < maxcol + 1;j++) {
                    if(stop_flag){
                        break;
                    }
                    //value = worksheet->read(i, j);
                    //qDebug()<<"read:"<<value.toString();
                    if(worksheet->cellAt(i, j) == NULL || worksheet->cellAt(i, j)->value().isNull() || !worksheet->cellAt(i, j)->value().isValid()){
                        continue;
                    }
                    //qDebug()<<"cellat:"<<worksheet->cellAt(i, j)->value().toString();
                    switch (j) {
                    case 2://转台工作轴
                        turn_type = worksheet->cellAt(i, j)->value().toString();
                        break;
                    case 3://内轴转动方式
                        if(turn_type[0] == '1'){//仅外轴
                            break;
                        }
                        inop_type = worksheet->cellAt(i, j)->value().toString();
                        if(inop_type[0] == '0'){
                            cmd_data.append("#Inn,Pos,");
                        }else if(inop_type[0] == '1'){
                            cmd_data.append("#Inn,Speed,");
                        }else{
                            cmd_data.append("#Inn,Stop;\n\n");
                        }
                        break;
                    case 4://内轴转动参数
                        if(turn_type[0] == '1' || inop_type[0] == '2'){//仅外轴或stop
                            break;
                        }
                        turn_value = worksheet->cellAt(i, j)->value().toString();
                        if(inop_type[0] == '0'){
                            cmd_data.append(turn_value+",10.0,10.0;\n");
                        }else {
                            cmd_data.append(turn_value+",10.0;\n");
                        }
                        break;
                    case 5://外轴转动方式
                        if(turn_type[0] == '0'){//仅内轴
                            break;
                        }
                        outop_type = worksheet->cellAt(i, j)->value().toString();
                        if(outop_type[0] == '0'){//位置
                            cmd_data.append("#Out,Pos,");
                        }else if(outop_type[0] == '1'){//速率
                            cmd_data.append("#Out,Speed,");
                        }else{//停止
                            cmd_data.append("#Out,Stop;\n\n");
                            stop_flag = true;
                        }
                        break;
                    case 6://外轴转动参数
                        if(turn_type[0] == '0' || outop_type[0] == '2'){//仅内轴或者stop
                            break;
                        }
                        turn_value = worksheet->cellAt(i, j)->value().toString();
                        if(outop_type[0] == '0'){//位置
                            cmd_data.append(turn_value+",10.0,10.0;\n");
                        }else {
                            cmd_data.append(turn_value+",10.0;\n");
                        }
                        break;
                    case 7://是否到位操作
                        until_reach = worksheet->cellAt(i, j)->value().toString();
                        if(until_reach[0] == '0'){
                            cmd_data.append("#YS,UntilReach;\n");
                        }
                        break;
                    case 8://采集方式
                        cat_type = worksheet->cellAt(i, j)->value().toString();
                        if(cat_type[0] == '1'){//不采集
                            cmd_data.append("\n");
                            stop_flag = true;
                        }
                        break;
                    case 9://采集时间
                        cat_timestr = worksheet->cellAt(i, j)->value().toString();
                        cmd_data.append("#YS," + cat_timestr + ",");
                        break;
                    case 10://坐标轴
                        aix_str = worksheet->cellAt(i, j)->value().toString();
                        cmd_data.append("w" + aix_str);
                        break;
                    case 11://文件前缀
                        file_prefix = worksheet->cellAt(i, j)->value().toString();
                        cmd_data.append("_" + file_prefix + ";\n\n");
                        break;
                    default:
                        break;
                    }

                }
                qDebug()<<"cmd_data:"<<cmd_data;
                filedata.append(cmd_data);
            }

        }
        filedata.append("\n\n");
    }

    if(m_sWorkFiles.size() == 0 || filedata.isEmpty()){
        QMessageBox::critical(this, QString::fromLocal8Bit("采集失败"), QString::fromLocal8Bit("请正确选择转台采集脚本!"));
        return;
    }

    //qDebug()<<"filedata:"<<filedata;
    m_dTurntdlg->setJobContent(filedata);
    m_dTurntdlg->show();

}

void CRevoCtrPage::startAutoTurnTWork()
{
    qDebug()<<"startAutoTurnTWork";
    const QList<ES_PAIR> &tasklist = m_dTurntdlg->getParseData();
    //脚本采集完成
    if(tasklist.size() <= m_iIndex){
        auto_Turnt_Work_Complete();
        return;
    }else{
        ui->pb_testprecess->setValue(m_iIndex * 100 / tasklist.size());
    }
    ES_PAIR params = tasklist.at(m_iIndex++);
    //qDebug()<<params.first<<params.second;
    //停止采集
    //如果下次（包括未开始的本次）采集是要到达后才采集，则先停止采集，否则不停
    if(is_Next_needStop(tasklist, m_iIndex - 1)){
        for(QString scom:m_vsDataPortn){
            qDebug()<<"stopWork";
            emit sigSeriOpen(scom, 1, "");
        }
    }

    //发送转台指令
    //等待转台到位
    //开始采集
    //开启定时器延时等待采集
    switch (params.first) {
    case E_INPOS:
        m_bInInPlace =  false;
        motion_CmdTo_Turntable(0, E_Sit, params.second.at(0).toFloat(), params.second.at(1).toFloat(), params.second.at(2).toFloat());
        break;
    case E_INSPEED:
        m_bInInPlace =  false;
        motion_CmdTo_Turntable(0, E_Speed, 0.0, params.second.at(0).toFloat(), params.second.at(1).toFloat());
        break;
    case E_INSWAY:
        m_bInInPlace =  false;
        motion_CmdTo_Turntable(0, E_Swing, 0.0, params.second.at(0).toFloat(), params.second.at(1).toFloat());
        break;
    case E_INFZ:
        m_bInInPlace =  false;
        motion_CmdTo_Turntable(0, E_GiveChg, 0.0, 0.0, 0.0);
        break;
    case E_INSTOP:
        m_bInInPlace =  false;
        motion_CmdTo_Turntable(0, E_Stop, 0.0, 0.0, 0.0);
        break;
    case E_OUTPOS:
        m_bOutInPlace = false;
        motion_CmdTo_Turntable(1, E_Sit, params.second.at(0).toFloat(), params.second.at(1).toFloat(), params.second.at(2).toFloat());
        break;
    case E_OUTSPEED:
        m_bOutInPlace = false;
        motion_CmdTo_Turntable(1, E_Speed, 0.0, params.second.at(0).toFloat(), params.second.at(1).toFloat());
        break;
    case E_OUTSWAY:
        m_bOutInPlace = false;
        motion_CmdTo_Turntable(1, E_Swing, 0.0, params.second.at(0).toFloat(), params.second.at(1).toFloat());
        break;
    case E_OUTFZ:
        m_bOutInPlace = false;
        motion_CmdTo_Turntable(1, E_GiveChg, 0.0, 0.0, 0.0);
        break;
    case E_OUTSTOP:
        m_bOutInPlace = false;
         motion_CmdTo_Turntable(1, E_Stop, 0.0, 0.0, 0.0);
        break;
    case E_DELY:
        //等待到位
        qDebug()<<params.second.at(0)<<m_bInInPlace<<m_bOutInPlace;
        if( params.second.at(0) == "UntilReach" ){
            if(!m_bInInPlace || !m_bOutInPlace){
                m_iIndex--;
                m_iTimerId = startTimer(1000);
                return;
            }else{
                break;
            }
        }else{
            //开始延时采集
            for(QString scom:m_vsDataPortn){
                //begin start seri
                qDebug()<<"start seri:"<<QThread::currentThreadId();
                if(params.second.size() == 2){
                    m_sFilePre = params.second.at(1);
                    emit sigSeriOpen(scom, 0, params.second.at(1));
                }else{
                    //如果文件名为空，则表示继续采集到原来的文件中
                    //emit sigSeriOpen(scom, 0, "");
                }
            }

            qDebug()<<"startWork:"<<params.second.at(0).toInt();
            m_iTimerId = startTimer(params.second.at(0).toInt() * 1000);
            return;
        }
    default:
        break;
    }
    m_iTimerId = startTimer(1000);
    qDebug()<<"startAutoTurnTWork end";
    //停止采集
}


void CRevoCtrPage::on_bt_startwork_clicked()
{
    //先将双轴使能
    m_iIndex = 0;
    /*if(!enable_CmdTo_Turntable(0, 0) || !enable_CmdTo_Turntable(0, 1)){
        QMessageBox::critical(this, QString::fromLocal8Bit("使能失败"), QString::fromLocal8Bit("请检查转台端口是否正常连接!"));
        return;
    }
    */

    QStringList slcom = m_oseriPage->getAvilaCom();
    QPointer<CSseriService> sWorkSer = NULL;
    for(QString com:slcom){
        if(com != m_sPortn && m_oseriPage->getStatusByPortN(com)){
            m_vsDataPortn.append(com);
            sWorkSer = m_oseriPage->getServByPortN(com);
            if(sWorkSer == NULL){
                continue;
            }
            m_vsWorkSer.append(sWorkSer);
            connect(this, &CRevoCtrPage::sigSeriOpen, sWorkSer, &CSseriService::slotSerialOpenOrClose, Qt::BlockingQueuedConnection);
            qDebug()<<"on_bt_startwork_clicked:"<<com<<m_sPortn<<sWorkSer;
            //break;
        }
    }
    if(m_vsWorkSer.size() == 0){
        QMessageBox::critical(this, QString::fromLocal8Bit("采集失败"), QString::fromLocal8Bit("请先打开数据采集端口!"));
        return;
    }

    m_oseriPage->setUpgStatus(true);

    ui->bt_startwork->setDisabled(true);
    ui->bt_stopwork->setDisabled(false);
    ui->bt_instart->setDisabled(true);
    ui->bt_outstart->setDisabled(true);

    //先把数据串口停止
    for(QString scom:m_vsDataPortn){
        emit sigSeriOpen(scom, 1, "");
    }

    startAutoTurnTWork();
    qDebug()<<"on_bt_startwork_clicked";
}

void CRevoCtrPage::timerEvent(QTimerEvent *e){
    killTimer(m_iTimerId);
    startAutoTurnTWork();
}

void CRevoCtrPage::on_bt_stopwork_clicked()
{
    //killTimer(m_iTimerId);
    for(QString scom:m_vsDataPortn){
        emit sigSeriOpen(scom, 1, "");
    }
    //停止采集后需要停止转台并归零
    const QList<ES_PAIR> &tasklist = m_dTurntdlg->getParseData();
    if(m_iIndex < tasklist.size() - 5){
        m_iIndex = tasklist.size() - 5;
    }
    ui->bt_stopwork->setDisabled(true);
}

void CRevoCtrPage::auto_Turnt_Work_Complete(){
    m_sTurnServ = NULL;
    ui->pb_testprecess->setValue(100);
    ui->bt_startwork->setDisabled(false);
    ui->bt_stopwork->setDisabled(true);
    ui->bt_instart->setDisabled(false);
    ui->bt_outstart->setDisabled(false);
    for (CSseriService *ser:m_vsWorkSer) {
        delete ser;
    }
    m_vsWorkSer.clear();
    m_vsDataPortn.clear();

    m_oseriPage->setUpgStatus(false);

    return;
}

bool CRevoCtrPage::is_Next_needStop(const QList<ES_PAIR> &tasklist, int start_index){
    for (int i = start_index; i < tasklist.size();i++) {
        ES_PAIR params = tasklist.at(i);
        if(params.first == E_DELY){
            //下一次采集需要到位或者更换文件名，则需要先停止串口
            if(params.second.at(0) == "UntilReach" || params.second.size() == 2){
                return true;
            }else{
                return false;
            }
        }
    }
    return false;
}
