﻿#include "cflyviewpage.h"
#include "ui_cflyviewpage.h"
#include "cverticalscalwidget.h"
#include "ctophalfboardlab.h"

CFlyViewPage::CFlyViewPage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CFlyViewPage)
{
    ui->setupUi(this);
    m_pwidspeed = static_cast<CVerticalScalWidget *>(ui->wd_speed);
    m_pwidspeed->setDirect(0);
    m_pwidspeed->setBackColor(QColor("#2E2249"), QColor("#4C2F7A"));
    m_pwidspeed->setCurrentVal(0.0);
    m_pwidalt = static_cast<CVerticalScalWidget *>(ui->wd_altitude);
    m_pwidalt->setDirect(1);
    m_pwidalt->setBackColor(QColor("#2E2249"), QColor("#4C2F7A"));
    m_pwidalt->setCurrentVal(0.0);

    m_plabalt = static_cast<CTopHalfBoardLab *>(ui->lab_altitude);
    m_plabalt->setTextVal(0.0);
    m_plabspeed = static_cast<CTopHalfBoardLab *>(ui->lab_speed);
    m_plabspeed->setTextVal(0.0);

    m_pwidatt = static_cast<CAttitudeWidget *>(ui->widget_2);
    m_pwidatt->setAttitude(0.0, 0.0);
    m_pwidyaw = static_cast<CYawIndWidge *>(ui->widget);
    m_pwidyaw->setYaw(0.0);
}

CFlyViewPage::~CFlyViewPage()
{
    delete ui;
}

void CFlyViewPage::showplateview(double pitch, double yaw, double roll, double speed, double hheight){
    m_plabalt->setTextVal(hheight);
    m_pwidalt->setCurrentVal(hheight);
    m_plabspeed->setTextVal(speed);
    m_pwidspeed->setCurrentVal(speed);
    m_pwidatt->setAttitude(pitch, roll);
    m_pwidyaw->setYaw(yaw);
}
