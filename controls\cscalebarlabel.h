﻿#ifndef CSCALEBARLABEL_H
#define CSCALEBARLABEL_H

#include <QLabel>
#include <QPaintEvent>
#include <QMouseEvent>

/*
* 回放模式下刻度尺控件
*/
class CScalebarLabel : public QLabel
{
    Q_OBJECT
public:
    explicit CScalebarLabel(QWidget *parent = nullptr);
    void paintEvent(QPaintEvent *) override;
    void mousePressEvent(QMouseEvent *ev) override;
    void mouseMoveEvent(QMouseEvent *ev) override;
    void mouseReleaseEvent(QMouseEvent *ev) override;
    void redrawppoint();
    void setProcess(float process);
    float getProcess();
    void setTotalProcess(int totalnum);
    void reset();

signals:
    void sigpmoved(int x, int type);
private:
    QPointF pcenter;
    int pradiu;
    bool ispress;   //鼠标按下
    float currentp;
    float m_process;
};

#endif // CSCALEBARLABEL_H
